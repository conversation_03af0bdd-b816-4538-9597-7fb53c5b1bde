#!/bin/bash

source .env

COMMIT_INFO_MESSAGE="Validação novos testes QA Team pipeline"

MODULO="vendasOnlinePratique"

docker pull $IMAGE_LATEST

#docker run --add-host "$HOST_DNS":"$HOST_PIPELINE" --rm -e COMMIT_INFO_MESSAGE="$COMMIT_INFO_MESSAGE" -e CYPRESS_BASE_URL=$VENDAS_URL $IMAGE run -P $MODULO --config baseUrl=$VENDAS_URL --record --key e6c67c62-3b08-4905-bef6-430198540257
docker run --add-host "$HOST_DNS":"$HOST_PIPELINE" --rm -e CYPRESS_VENDAS_URL=$VENDAS_URL -e CYPRESS_URL_API=$API_URL -e COMMIT_INFO_MESSAGE="$COMMIT_INFO_MESSAGE" -e CYPRESS_BASE_URL=$VENDAS_URL $IMAGE_LATEST run -P $MODULO --config baseUrl=$VENDAS_URL --record --key e6c67c62-3b08-4905-bef6-430198540257


