{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"vendas": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {"@schematics/angular:component": {"style": "sass"}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/vendas", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.app.json", "assets": ["src/icon_vendas.png", "src/assets"], "styles": ["src/styles.scss", "node_modules/font-awesome/css/font-awesome.min.css", "node_modules/bootstrap-4-grid/css/grid.min.css", "src/assets/css/pacto.scss"], "scripts": ["node_modules/hammerjs/hammer.min.js"], "es5BrowserSupport": true}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "vendas:build"}, "configurations": {"production": {"browserTarget": "vendas:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "vendas:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["src/styles.scss"], "scripts": [], "assets": ["src/icon_vendas.png", "src/assets"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "vendas-e2e": {"root": "e2e/", "projectType": "application", "prefix": "", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "vendas:serve"}, "configurations": {"production": {"devServerTarget": "vendas:serve:production"}}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": "e2e/tsconfig.e2e.json", "exclude": ["**/node_modules/**"]}}}}}, "defaultProject": "vendas"}