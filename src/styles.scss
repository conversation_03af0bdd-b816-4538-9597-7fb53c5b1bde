@import "~@angular/material/prebuilt-themes/indigo-pink.css";

.cursor-pointer{
  cursor: pointer;
}

.disabled-red {
  background-color: #ffced1;
}

.title {
    display: contents;
    font-size: 30px;
    color: #2C343B;
    font-family: Europa;
}

.subtitle {
    margin-left: 45px !important;
    font-size: 16px;
    color: #bdc3c7;
    text-align: left;
}

.intertitle {
  font-family: Europa;
  font-size: 20px;
  line-height: 20px;
  color: #2C343B;
  align-items: center;
}

.nunito-sans16 {
  font-family: Europa;
  font-size: 16px;
  font-weight: 400;
  line-height: 16px;
  color: #2C343B;
}

.nunito-sans18 {
  font-family: Europa;
  font-size: 18px;
  line-height: 20px;
  text-align: left;
  color: #7F7F7F;
}



.title-Card {
  font-family: Europa;
  font-size: 18px;
  text-align: center;
  color: #2C343B
}

.subTitle-Card {
  font-family: Europa;
  font-size: 16px;
  text-align: left;
  color: #bdc3c7
}

.interTitle-Card {
  font-family: Europa;
  font-size: 16px;
  text-align: left;
  color: #2C343B
}

.interTitle-Card-radio {
  font-family: Europa;
  font-size: 15px;
  text-align: left;
  color: #80868b
}




.popins14 {
  font-weight: bold;
  line-height: 20px;
  text-align: center;
  font-size: 14px;
  color: #1998FC;
}


.tooltip-custom {
  white-space: pre-line;
}
