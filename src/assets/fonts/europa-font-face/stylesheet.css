@font-face {
    font-family: 'Europa';
    src: url('Europa-Light.eot');
    src: url('Europa-Light.eot?#iefix') format('embedded-opentype'),
        url('Europa-Light.woff2') format('woff2'),
        url('Europa-Light.woff') format('woff'),
        url('Europa-Light.ttf') format('truetype');
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: 'Europa';
    src: url('Europa-BoldItalic.eot');
    src: url('Europa-BoldItalic.eot?#iefix') format('embedded-opentype'),
        url('Europa-BoldItalic.woff2') format('woff2'),
        url('Europa-BoldItalic.woff') format('woff'),
        url('Europa-BoldItalic.ttf') format('truetype');
    font-weight: bold;
    font-style: italic;
}

@font-face {
    font-family: 'Europa';
    src: url('Europa-Regular.eot');
    src: url('Europa-Regular.eot?#iefix') format('embedded-opentype'),
        url('Europa-Regular.woff2') format('woff2'),
        url('Europa-Regular.woff') format('woff'),
        url('Europa-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Europa';
    src: url('Europa-Italic.eot');
    src: url('Europa-Italic.eot?#iefix') format('embedded-opentype'),
        url('Europa-Italic.woff2') format('woff2'),
        url('Europa-Italic.woff') format('woff'),
        url('Europa-Italic.ttf') format('truetype');
    font-weight: normal;
    font-style: italic;
}

@font-face {
    font-family: 'Europa';
    src: url('Europa-LightItalic.eot');
    src: url('Europa-LightItalic.eot?#iefix') format('embedded-opentype'),
        url('Europa-LightItalic.woff2') format('woff2'),
        url('Europa-LightItalic.woff') format('woff'),
        url('Europa-LightItalic.ttf') format('truetype');
    font-weight: 300;
    font-style: italic;
}

@font-face {
    font-family: 'Europa';
    src: url('Europa-Bold.eot');
    src: url('Europa-Bold.eot?#iefix') format('embedded-opentype'),
        url('Europa-Bold.woff2') format('woff2'),
        url('Europa-Bold.woff') format('woff'),
        url('Europa-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
}

