@font-face {
  font-family: 'Pacto-Icons-Fonts';
  src:  url('../fonts/pacto-fonts/fonts/Pacto-Icons-Fonts.eot?th4cks');
  src:  url('../fonts/pacto-fonts/fonts/Pacto-Icons-Fonts.eot?th4cks#iefix') format('embedded-opentype'),
  url('../fonts/pacto-fonts/fonts/Pacto-Icons-Fonts.ttf?th4cks') format('truetype'),
  url('../fonts/pacto-fonts/fonts/Pacto-Icons-Fonts.woff?th4cks') format('woff'),
  url('../fonts/pacto-fonts/fonts/Pacto-Icons-Fonts.svg?th4cks#Pacto-Icons-Fonts') format('svg');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Europa';
  src: url('../fonts/europa-font-face/Europa-Light.eot');
  src: url('../fonts/europa-font-face/Europa-Light.eot?#iefix') format('embedded-opentype'),
  url('../fonts/europa-font-face/Europa-Light.woff2') format('woff2'),
  url('../fonts/europa-font-face/Europa-Light.woff') format('woff'),
  url('../fonts/europa-font-face/Europa-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Europa';
  src: url('../fonts/europa-font-face/Europa-BoldItalic.eot');
  src: url('../fonts/europa-font-face/Europa-BoldItalic.eot?#iefix') format('embedded-opentype'),
  url('../fonts/europa-font-face/Europa-BoldItalic.woff2') format('woff2'),
  url('../fonts/europa-font-face/Europa-BoldItalic.woff') format('woff'),
  url('../fonts/europa-font-face/Europa-BoldItalic.ttf') format('truetype');
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Europa';
  src: url('../fonts/europa-font-face/Europa-Regular.eot');
  src: url('../fonts/europa-font-face/Europa-Regular.eot?#iefix') format('embedded-opentype'),
  url('../fonts/europa-font-face/Europa-Regular.woff2') format('woff2'),
  url('../fonts/europa-font-face/Europa-Regular.woff') format('woff'),
  url('../fonts/europa-font-face/Europa-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Europa';
  src: url('../fonts/europa-font-face/Europa-Italic.eot');
  src: url('../fonts/europa-font-face/Europa-Italic.eot?#iefix') format('embedded-opentype'),
  url('../fonts/europa-font-face/Europa-Italic.woff2') format('woff2'),
  url('../fonts/europa-font-face/Europa-Italic.woff') format('woff'),
  url('../fonts/europa-font-face/Europa-Italic.ttf') format('truetype');
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Europa';
  src: url('../fonts/europa-font-face/Europa-LightItalic.eot');
  src: url('../fonts/europa-font-face/Europa-LightItalic.eot?#iefix') format('embedded-opentype'),
  url('../fonts/europa-font-face/Europa-LightItalic.woff2') format('woff2'),
  url('../fonts/europa-font-face/Europa-LightItalic.woff') format('woff'),
  url('../fonts/europa-font-face/Europa-LightItalic.ttf') format('truetype');
  font-weight: 300;
  font-style: italic;
}

@font-face {
  font-family: 'Europa';
  src: url('../fonts/europa-font-face/Europa-Bold.eot');
  src: url('../fonts/europa-font-face/Europa-Bold.eot?#iefix') format('embedded-opentype'),
  url('../fonts/europa-font-face/Europa-Bold.woff2') format('woff2'),
  url('../fonts/europa-font-face/Europa-Bold.woff') format('woff'),
  url('../fonts/europa-font-face/Europa-Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
}

