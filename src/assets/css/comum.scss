
*{
  font-family: Europa;
}
a{
  cursor: pointer;
}
.pacto-right {
  text-align: right;
}
input#idnomecartao{
  text-transform: uppercase;
}
.pacto-btn-primary {
  margin: auto;
  margin-top: 15px;
  width: 100%;
  text-align: center;
  color: #ffffff;
  height: 40px;
  line-height: 40px;
  font-size: 18px;
  font-weight: bold;
  border-radius: 4px;
  box-shadow: 0 10px 15px 0 rgba(223, 223, 223, 0.5), inset 3px 4px 11px 0 rgba(1, 1, 1, 0.09);
  background-color: #f39c12;
  display: block;
  text-decoration:none;
}
.pacto-no-margin-padding{
  margin: 0;
  padding: 0;
}

.pacto-center{
  text-align: center;
}
.pacto-mg-top-pequeno{
  margin-top: 15px;
}
.pacto-mg-top-medio{
  margin-top: 30px;
}
.pacto-mg-top-grande{
  margin-top: 50px;
}
.pacto-padding-medio{
  padding: 15px;
}
.ta-center{
  text-align: center;
}
.ta-left{
  text-align: left;
}
.ta-right{
  text-align: right;
}

.grid2Columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
}

.gridPlanoRec {
  display: grid;
  text-align: left;
}

#parcelasdiv {
  display: none;
}

.colunafixa {
  position: sticky;
  top: 10px;
}
