import { BrowserModule } from '@angular/platform-browser';
import { NgModule, LOCALE_ID } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {NgbDatepickerI18n, NgbDatepickerModule, NgbTooltipModule} from '@ng-bootstrap/ng-bootstrap';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './components/app-component/app.component';
import { PlanosComponent } from './components/planos/planos.component';
import { ListaplanosComponent } from './components/planos/listaplanos/listaplanos.component';
import { HeaderComponent } from './components/header/header.component';
import { ItemplanoComponent } from './components/planos/itemplano/itemplano.component';
import { UnidadeSelecionadaComponent } from './components/unidade-selecionada/unidade-selecionada.component';
import { PlanoSelecionadoComponent } from './components/planos/plano-selecionado/plano-selecionado.component';
import { FooterComponent } from './components/footer/footer.component';
import { CheckoutComponent } from './components/checkout/checkout.component';
import { InputComponent } from './components/forms/input/input.component';
import { TextMaskModule } from 'angular2-text-mask';
import { CartaoCreditoComponent } from './components/checkout/cartao-credito/cartao-credito.component';
import { HttpClientModule, HttpClient } from '@angular/common/http';
import ptBr from '@angular/common/locales/pt';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';
import { registerLocaleData } from '@angular/common';
import { PagamentoComponent } from './components/pagamento/pagamento.component';
import { InfoPagamentoComponent } from './components/info-pagamento/info-pagamento.component';
import { ModalPlanoMobileComponent } from './components/modal-plano-mobile/modal-plano-mobile.component';
import { VisualizarDocumentoComponent } from './components/visualizar-documento/visualizar-documento.component';
import { BandeiraCartaoComponent } from './components/bandeira-cartao/bandeira-cartao.component';
import { RECAPTCHA_V3_SITE_KEY, RecaptchaV3Module } from 'ng-recaptcha';
import { PlanoCreditoComponent } from './components/planos/plano-credito/plano-credito.component';
import { PlanoRecorrenteComponent } from './components/planos/plano-recorrente/plano-recorrente.component';
import { PlanoRecorrenteParcOperadoraComponent } from './components/planos/plano-recorrente-parcelamento-operadora/plano-recorrente-parc-operadora.component';
import { PlanoCreditoParcOperadoraComponent } from './components/planos/plano-credito-parcelamento-operadora/plano-credito-parc-operadora.component';
import { PlanoInicioFuturoComponent } from './components/planos/plano-inicio-futuro/plano-inicio-futuro.component';
import { PlanoConvencionalComponent } from './components/planos/plano-convencional/plano-convencional.component';
import { PlanoConvencionalParcOperadoraComponent } from './components/planos/plano-convencional-parc-operadora/plano-convencional-parc-operadora.component';
import { PosPagamentoComponent } from './components/pos-pagamento/pospagamento.component';
import { ListaprodutoComponent } from './components/produtos/listaproduto/listaproduto.component';
import { ProdutosComponent } from './components/produtos/produtos/produtos.component';
import { ItemprodutoComponent } from './components/produtos/itemproduto/itemproduto.component';
import { ProdutosSelecionadosComponent } from './components/produtos/produtos-selecionados/produtos-selecionados.component';
import { LojaComponent } from './components/loja/loja/loja.component';
import { SliderComponent } from './components/produtos/slider/slider.component';
import { SliderItemDirective } from './components/produtos/slider/slider-item.directive';
import { CadastroClienteComponent } from './components/checkout/cadastro-cliente/cadastro-cliente/cadastro-cliente.component';
import { VisualizarDocumentoLinkPagamentoComponent } from './components/visualizar-documento/visualizar-documento-link-pagamento/visualizar-documento-link-pagamento.component';
import { DebitoPixComponent } from './components/checkout/debito-pix/debito-pix.component';
import { AulasComponent } from './components/aulas/aulas.component';
import { CalendarioComponent } from './components/aulas/calendario/calendario.component';
import { PeriodoDiaComponent } from './components/aulas/periodo-dia/periodo-dia.component';
import { SlotComponent } from './components/aulas/slot/slot.component';
import { AulasMarcadasComponent } from './components/aulas/aulas-marcadas/aulas-marcadas.component';
import { ProdutoDiariaComponent } from './components/aulas/produto-diaria/produto-diaria.component';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { BoletoComponent } from './components/checkout/boleto/boleto.component';
import { EmailOptinComponent } from './components/email-optin/email-optin.component';
import { AgradecimentoComponent } from './components/email-optin/agradecimento/agradecimento.component';
import { UnsubscribeComponent } from './components/email-optin/unsubscribe/unsubscribe.component';
import { DescadastradoOptinComponent } from './components/email-optin/unsubscribe/descadastrado-optin/descadastrado-optin.component';
import { AutorizacoesComponent } from './components/autorizacoes/autorizacoes.component';
import { CustomDatepickerI18nPT  } from './components/aulas/calendario/CustomDatepickerI18nPT';
import { CustomDatepickerI18nEN  } from './components/aulas/calendario/CustomDatepickerI18nEN';
import {VideoObservacaoComponent} from './components/app-component/video-observacao/video-observacao.component';
import { ModalPreCadastroComponent } from './components/modal-pre-cadastro/modal-pre-cadastro.component';
import { ModalidadeComponent } from './components/modalidade/modalidade.component';
import { TurmaComponent } from './components/turma/turma.component';
import { NascimentoComponent } from './components/nascimento/nascimento.component';
import {MatDatepickerModule} from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'; // se ainda não estiver importado
import { MatNativeDateModule } from '@angular/material/core';
import { TruncatePipe } from './components/footer/truncate.pipe';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AgendaLocacaoAmbienteComponent } from './components/locacoes/agenda-locacao-ambiente/agenda-locacao-ambiente.component';
import { PeriodoLocacaoComponent } from './components/locacoes/periodo-locacao/periodo-locacao.component';
import { SlotLocacaoComponent } from './components/locacoes/slot-locacao/slot-locacao.component';
import { LocacoesMarcadasComponent } from './components/locacoes/locacoes-marcadas/locacoes-marcadas.component';
import { LocacoesSelecionadasCheckoutComponent } from './components/locacoes/locacoes-selecionadas-checkout/locacoes-selecionadas-checkout.component';

registerLocaleData(ptBr);

export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

@NgModule({
  declarations: [
    AppComponent,
    PlanosComponent,
    ListaplanosComponent,
    HeaderComponent,
    ItemplanoComponent,
    UnidadeSelecionadaComponent,
    PlanoSelecionadoComponent,
    FooterComponent,
    InputComponent,
    CheckoutComponent,
    CartaoCreditoComponent,
    PagamentoComponent,
    InfoPagamentoComponent,
    ModalPlanoMobileComponent,
    VisualizarDocumentoComponent,
    BandeiraCartaoComponent,
    PlanoCreditoComponent,
    PlanoRecorrenteComponent,
    PlanoRecorrenteParcOperadoraComponent,
    PlanoCreditoParcOperadoraComponent,
    PlanoInicioFuturoComponent,
    PlanoConvencionalComponent,
    PlanoConvencionalParcOperadoraComponent,
    PosPagamentoComponent,
    ListaprodutoComponent,
    ProdutosComponent,
    ItemprodutoComponent,
    ProdutosSelecionadosComponent,
    LojaComponent,
    SliderComponent,
    SliderItemDirective,
    CadastroClienteComponent,
    SliderItemDirective,
    VisualizarDocumentoLinkPagamentoComponent,
    SliderItemDirective,
    DebitoPixComponent,
    AulasComponent,
    CalendarioComponent,
    PeriodoDiaComponent,
    SlotComponent,
    AulasMarcadasComponent,
    ProdutoDiariaComponent,
    BoletoComponent,
    EmailOptinComponent,
    AgradecimentoComponent,
    UnsubscribeComponent,
    DescadastradoOptinComponent,
    AutorizacoesComponent,
    VideoObservacaoComponent,
    ModalPreCadastroComponent,
    ModalidadeComponent,
    TurmaComponent,
    NascimentoComponent,
    TruncatePipe,
    AgendaLocacaoAmbienteComponent,
    PeriodoLocacaoComponent,
    SlotLocacaoComponent,
    LocacoesMarcadasComponent,
    LocacoesSelecionadasCheckoutComponent,
  ],
  imports: [
    BrowserModule,
    HttpClientModule,
    [SweetAlert2Module.forRoot()],
    TextMaskModule,
    AppRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    RecaptchaV3Module,
    NgbDatepickerModule,
    NgbTooltipModule,
    HttpClientModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    BrowserAnimationsModule,
    MatNativeDateModule,
    MatTooltipModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      }
    })
  ],
  exports: [
    TextMaskModule,
    FormsModule,
    ReactiveFormsModule
  ],
  providers: [
    { provide: NgbDatepickerI18n, useClass: CustomDatepickerI18nPT },
    { provide: LOCALE_ID, useValue: 'pt' },
    { provide: RECAPTCHA_V3_SITE_KEY, useValue: '6LfQVbIUAAAAAJkFWWnSKLo9ATaKt3axLDRvVkK9' },
    // Adicione o provedor NgbDatepickerI18n com base na configuração do idioma
],
  bootstrap: [AppComponent]
})
export class AppModule { }
