@import "../../../../assets/css/variaveis";

.caixa-plano-selecionado {
  text-align: center;
  padding: 20px 20px 20px 20px;
  margin: 10px 0;
  background: $branco;
  border: 1px solid rgba(189, 195, 199, 0.5);
  box-sizing: border-box;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
}

.primeiraparcela {
  font-weight: 900;
  margin: 15px 0;
  font-size: 25px;
  display: block;
}

.leiacontrato {
  margin: 23px 0;
  font-size: 18px;
}

.plano-selec {
  width: 100%;
  padding: 25px;
  margin-top: 15px;
  height: 236px;
  border-radius: 4px;
  background-color: #eff2f7;
}

.selecionou {
  color: rgba(44, 52, 59, 0.5);
}

.linha-selec {
  margin-top: 15px;
  padding-top: 15px;
  border-top: solid 2px rgba(189, 195, 199, 0.3);
  font-size: 12px;
}

.linha-selec-responsive {
  width: 88%;
  margin-left: 7%;
  margin-top: 32%;
  padding-top: 15px;
  border-top: solid 2px rgba(189, 195, 199, 0.3);
  font-size: 12px;
}

.linha-selec-responsive2 {
  width: 88%;
  margin-left: 7%;
  margin-top: 33%;
  padding-top: 15px;
  border-top: solid 2px rgba(189, 195, 199, 0.3);
  font-size: 12px;
}

.linha-selec-responsive3 {
  width: 88%;
  margin-left: 7%;
  margin-top: 35%;
  padding-top: 15px;
  border-top: solid 2px rgba(189, 195, 199, 0.3);
  font-size: 12px;
}

.linha-selec-plano-prod {
  margin-top: 24px;
  padding-top: 15px;
  border-top: solid 2px rgba(189, 195, 199, 0.3);
  font-size: 12px;
}

.linha-selec-anuid-parce-default {
  margin-top: 5%;
  padding-top: 15px;
  border-top: solid 2px rgba(189, 195, 199, 0.3);
  font-size: 12px;
}

.linha-selec-anuid-default {
  margin-top: 10%;
  padding-top: 15px;
  border-top: solid 2px rgba(189, 195, 199, 0.3);
  font-size: 12px;
}

.linha-selec-anuid {
  margin-top: 30px;
  padding-top: 15px;
  border-top: solid 2px rgba(189, 195, 199, 0.3);
  font-size: 12px;
}

.linha-selec-2 {
  margin-top: 50%;
}

.controle-top {
  margin-top: 12%;
}

.controle-top-default {
  margin-top: 1%;
}

.linha-selec-1 {
  margin-top: 15%;
}

.nome-planoResumo {
  text-align: left;
  text-transform: capitalize;
  font-size: 24px;
  font-weight: 900;
}

.nome-plano {
  i {
    margin-right: 10px;
  }

  text-transform: capitalize;
  font-size: 25px;
  vertical-align: middle;
  margin: 15px 0;
}

.primeira-parcela-top {
  text-transform: capitalize;
  //position: absolute;
  //margin-left: -55%;
  font-weight: bold;
  font-size: 20px;
  color: #2c343b;
  line-height: 16px;
}

.total-primeira-parcela-top {
  font-weight: bold;
  font-size: 32px;
  width: 100%;
}

.sub-primeira-parcela-top {
  margin-top: 10px;
  font-size: 12px;
  color: rgba(44, 52, 59, 0.5);
}

.sub-cupom-top {
  position: absolute;
  margin-top: 8%;
  margin-left: -55%;
  font-size: 12px;
  line-height: 16px;
  color: rgba(44, 52, 59, 0.5);
}

.mensalidade {
  font-weight: 900;
  margin-top: 10px;
}

.plano-inclui {
  text-transform: capitalize;
  font-weight: bold;
  font-size: 20px;
  color: #2c343b;
}

.sub-plano-produto-inclui {
  width: 100%;
  text-align: center;
}

.sub-cupom {
  width: 100%;
  text-align: left;
  padding-left: 6%;
}

.produto-inclui {
  text-transform: capitalize;
  font-weight: bold;
  font-size: 20px;
  color: #2c343b;
}

.anuid-inclui {
  font-weight: bold;
  font-size: 20px;
  color: #2c343b;
  text-align: left;
}

.total-anuid {
  font-weight: bold;
  font-size: 32px;
  text-align: right;
}

.desc-anuid-inclui {
  font-weight: bold;
  font-size: 16px;
  color: rgba(44, 52, 59, 0.5);
  text-align: left;
}

.desc-anuid-parcelada {
  font-weight: bold;
  font-size: 17px;
  color: rgba(44, 52, 59, 0.5);
}

.desc-total-anuid {
  font-weight: bold;
  font-size: 24px;
  color: rgba(44, 52, 59, 0.5);
  text-align: right;
}

.desc-anuid-parc {
  font-weight: bold;
  right: -26%;
  font-size: 15px;
  line-height: 16px;
  color: rgba(44, 52, 59, 0.5);
}

.selecionar {
  box-shadow: 0 2px 3px 0 rgba(44, 52, 59, 0.1);
  cursor: pointer;
  width: 130px;
  color: #ffffff;
  height: 30px;
  border-radius: 4px;
  margin-left: 25px;
  display: inline-flex;

  .btn {
    text-align: center;
    font-size: 12px;
    width: 100px;
    line-height: 30px;
    vertical-align: middle;
  }

  .detalhes {
    line-height: 30px;
    width: 30px;
    vertical-align: middle;
    text-align: center;
  }
}

.desconto {
  margin: 10px 2px;
  vertical-align: middle;
  font-weight: bold;
}

.modalidade,
.detalhe {
  margin: 10px 2px;
  display: inline-block;
  font-weight: bold;
  font-size: 16px;
  color: rgba(44, 52, 59, 0.5);

  i,
  span {
    vertical-align: middle;
  }

  i {
    margin-right: 3px;
  }
}

.titulo {
  display: block;
  font-weight: 300;
  color: $textoclaro;
}

.mens {
  text-align: left;
  font-size: 16px;
  color: rgba(44, 52, 59, 0.5);
}

.parc-mens {
  width: 100%;
  text-align: right;
  color: rgba(44, 52, 59, 0.5);
}

.parc-mens-0 {
  text-align: right;
}

.ade {
  text-align: left;
  font-size: 16px;
  color: rgba(44, 52, 59, 0.5);
}

.ade-responsive {
  text-align: left;
  font-size: 16px;
  color: rgba(44, 52, 59, 0.5);
}

.parc-ade {
  text-align: right;
  width: 100%;
  color: rgba(44, 52, 59, 0.5);
}

.parc-ade-responsive {
  text-align: right;
  width: 100%;
  color: rgba(44, 52, 59, 0.5);
}

.parc-ade-0 {
  text-align: right;
  //margin-left: -1%;
}

.prod {
  text-align: left;
  font-size: 16px;
  color: rgba(44, 52, 59, 0.5);
}

.prod-responsive {
  text-align: left;
  font-size: 16px;
  color: rgba(44, 52, 59, 0.5);
}

.parc-prod {
  text-align: right;
  width: 100%;
  color: rgba(44, 52, 59, 0.5);
}
.parc-prod-responsive {
  text-align: right;
  width: 100%;
  color: rgba(44, 52, 59, 0.5);
}

.parc-prod-0 {
  text-align: right;
}

.anuid {
  text-align: left;
  font-size: 16px;
  color: rgba(44, 52, 59, 0.5);
}

.parc-anuid {
  text-align: right;
  width: 100%;
  color: rgba(44, 52, 59, 0.5);
}

.anuid-responsive {
  text-align: left;
  font-size: 16px;
  color: rgba(44, 52, 59, 0.5);
}

.anuid-responsive2 {
  text-align: left;
  font-size: 16px;
  color: rgba(44, 52, 59, 0.5);
}

.parc-anuid-responsive {
  text-align: right;
  width: 100%;
  color: rgba(44, 52, 59, 0.5);
}

.parc-anuid-responsive2 {
  text-align: right;
  width: 100%;
  color: rgba(44, 52, 59, 0.5);
}

.parc-anuid-0 {
  text-align: right;
  //margin-left: -1%;
}

.venc {
  text-align: left;
  font-size: 16px;
  color: rgba(44, 52, 59, 0.5);
}

.parc-venc {
  text-align: right;
  width: 100%;
  color: rgba(44, 52, 59, 0.5);
}

.detalhe {
  display: block;
}

.valor-desc {
  margin-top: 1vh;
  width: 100%;
  margin-left: 55%;
  color: rgba(44, 52, 59, 0.5);
}

.remover-cupom {
  font-size: 12px;
  line-height: 27px;
  margin-left: 2%;
  position: absolute;
}

.nome-cupom {
  margin-top: 1vh;
  text-align: center;
  font-weight: bold;
}

@media only screen and (max-width: 748px) {
  .modal {
    color: $textoescuro;
    text-align: center;
    border-radius: 5px;
    border: $bordacinza 1px solid;
    background-color: #ffffff;
    width: calc(100% - 20px);
    margin: auto;
    height: auto;
    padding: 10px;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
  }
  .layer {
    display: block;
    background-color: rgba(0, 0, 0, 0.5);
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
  }

  .selecionar {
    margin: auto;
    margin-top: 15px;
    width: 98%;
    height: 40px;

    .btn {
      font-size: 14px;
      width: calc(100% - 40px);
      line-height: 40px;
    }

    .detalhes {
      line-height: 40px;
      width: 40px;
    }
  }
}

.textRight {
  text-align: right;
}

.grid2Columns19 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  margin-top: 30px;
}

.linha-selec-total {
  margin-top: 15px;
  padding-top: 15px;
  border-top: solid 2px rgba(189, 195, 199, 0.3);
  font-size: 12px;
}

.gridPlanoSel {
  display: flex;
  align-items: center;
}

.gridPlanoSelLeft {
  padding: 0 15px 0 15px;
}

.gridPlanoSelRight {
  display: grid;
  text-align: left;
}

.divAnuidade {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
}
.custom-select-taxa-matricula {
  text-align: center;
  border-radius: 0.25em;
  margin: 10px 0;
  padding: 0.25em 0.5em;
  box-shadow: 0 10px 15px 0 #dfdfdf80;
  border: solid 1px #bdc3c7;
  cursor: pointer;
  font-size: 16px;
  font-family: "Europa";
   width: 95%;
}

ul {
  list-style-type: none;
  padding-left: 20px;
  padding-right: 20px;
}
.detalhamento-parcela{
  font-size: 12px;
}
