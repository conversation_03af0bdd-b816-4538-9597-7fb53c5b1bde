<div class="caixa-plano-selecionado">
  <div class="gridPlanoSel">
    <div class="gridPlanoSelLeft">
      <img src="assets/images/icon/icon-shopping-bag.png" height="40" width="37" />
    </div>
    <div class="gridPlanoSelRight">
      <span class="selecionou">{{"planos.plano-recorrente.plano-selecionado"|translate}}:</span>
      <span class="nome-planoResumo" [style.color]="getConfig().cor">{{getPlanoSelecionado().nome.toLowerCase()}}</span>
    </div>
  </div>
  <pacto-video-observacao [videoYouTubeCode]="getPlanoSelecionado().videoSiteUrl"
                          [observacao]="getPlanoSelecionado().observacaoSite">
  </pacto-video-observacao>
  <div class="linha-selec"></div>
  <div [style.border-left-color]="getConfig().cor">
    <div class="arrow-right" [style.border-left-color]="getConfig().cor"></div>
    <!--      CONFIGURAÇÃO DETALHAR PARCELAS DESMARCADA-->
    <div class="span_5_of_5 ta-center pedido ta-center grid2Columns">
      <div class="gridPlanoRec">
        <!--   <span class="primeira-parcela-top">{{"planos.plano-recorrente.total-primeira-parcela"|translate}}</span>     -->
        <span class="primeira-parcela-top">{{"planos.plano-recorrente.sera-cobrada"|translate}} {{getDescricaoCobrancaPrimeiraParcela()}}</span>
        <a *ngIf="detalharParcelas()" [style.color]="getConfig().cor" class="sub-primeira-parcela-top" id="detalharPrimeiraParcela"
          (click)="mostraPrimeiraParcelas()">{{"planos.plano-recorrente.detalhar-primeira-cobranca"|translate}} </a>
      </div>
      <div class="textRight">
        <span class="total-primeira-parcela-top" [style.color]="getConfig().cor">{{ ((getCupom() ?
          getValorSeraCobradoHojeDesconto() : getValorPrimeiraParcela()) | currency: this.getMoeda(): 'symbol')}}</span>
      </div>
    </div>
    <div class="textRight" id="parcelasPlanoDivP" style="display: none" #parcelasPlanoDivP>
      <ul>
        <li *ngIf="this.valorPrimeiraParcela != 0" class="detalhe">{{"planos.plano-recorrente.parcela"|translate}}:
          {{(this.getValorPrimeiraParcelaDesconto() | currency: this.getMoeda(): 'symbol')}} </li>
        <li *ngIf="this.valorAdesao != 0" class="detalhe">{{"planos.plano-recorrente.adesao"|translate}}:
          {{(this.getValorAdesaoDesconto() | currency: this.getMoeda(): 'symbol')}}</li>
        <li *ngIf="this.valorAnuidadeDesconto != 0 " class="detalhe">{{"planos.plano-recorrente.anuidade"|translate}}:
          {{(this.getValorAnuidadeDesconto() | currency: this.getMoeda(): 'symbol')}}</li>
        <li *ngIf="this.valorProduto !=0" class="detalhe">{{"planos.plano-recorrente.produto"|translate}}:
          {{(this.getValorProduto() | currency: this.getMoeda(): 'symbol')}}</li>
      </ul>
    </div>
    <!--      FIM CONFIGURAÇÃO DESMARCADA-->
    <!--      CONFIGURAÇÃO DETALHAR PARCELAS MARCADA-->
    <div *ngIf="detalharParcelas()" class="span_5_of_5 ta-center pedido ta-center">
      <div *ngIf="validarCobrarPrimeiraParcelaSempre() " class="grid2Columns19">
        <span class="mens"
          *ngIf="validarCobrarPrimeiraParcelaSempre()">{{"planos.plano-recorrente.mensalidade"|translate}}:</span>
        <span [className]="getPlanoSelecionado().primeiraParcela === 0.0 ? 'parc-mens parc-mens-0' : 'parc-mens'"
          *ngIf="validarCobrarPrimeiraParcelaSempre()">{{(getPlanoSelecionado().mensalidade)|
          currency:this.getMoeda():'symbol'}}</span>
      </div>
      <div *ngIf="getPlanoSelecionado().adesao != 0" class="grid2Columns19">
        <span *ngIf="getPlanoSelecionado().adesao != 0"
          [className]="getClassAdeTextResponsive()">{{"planos.plano-recorrente.adesao"|translate}}:</span>
        <span [className]="getClassAdeValueResponsive()" *ngIf="getPlanoSelecionado().adesao != 0">{{(getCupom() ?
          (getValorAdesaoDesconto()| currency:this.getMoeda():'symbol') : (getPlanoSelecionado().adesao|
          currency:this.getMoeda():'symbol'))}}</span>
      </div>
      <div *ngIf="getPlanoSelecionado().matricula != 0" class="grid2Columns19">
        <span *ngIf="getPlanoSelecionado().matricula != 0"
          [className]="getClassAdeTextResponsive()">{{"planos.plano-recorrente.matricula"|translate}}:</span>
        <span [className]="getClassAdeValueResponsive()" *ngIf="getPlanoSelecionado().matricula != 0">{{getPlanoSelecionado().matricula|
          currency:this.getMoeda():'symbol'}}</span>
      </div>
      <div>
        <label class="titulo" for="dividirMatricula"
          *ngIf="getPlanoSelecionado().nrVezesParcelarMatricula > 1 && getPlanoSelecionado().matricula">
          {{'divisao-taxa-matricula.dividir-matricula-em-quantas-vezes' | translate}} </label>
        <select name="dividirMatricula" class="custom-select-taxa-matricula" [(ngModel)]="vezesEscolhidasParcelarMatricula"
          (change)='this.getVezesEscolhidasParcelarMatricula()'
          *ngIf="getPlanoSelecionado().nrVezesParcelarMatricula > 1 && getPlanoSelecionado().matricula">
          <option
            *ngFor="let loop of counter(this.getPlanoSelecionado().nrVezesParcelarMatricula) ;let i= index, let f = first"
            [attr.selected]="first" [ngValue]="i+1">
            <p *ngIf="f"> {{'divisao-taxa-matricula.nao-dividir-taxa' | translate}}</p>
            <p *ngIf="!f"> {{'divisao-taxa-matricula.dividir-em' | translate}} {{i+1}}
              {{'divisao-taxa-matricula.parcelas-de' | translate}} {{(getPlanoSelecionado().matricula / [i+1]) |
              currency:this.getMoeda():'symbol' }}</p>
          </option>
        </select>
      </div>
      <div *ngIf="(getValorProRata() > 0.0)" class="grid2Columns19">
        <span [className]="getClassProdTextResponsive()">{{"planos.plano-recorrente.pro-rata"|translate}}:</span>
        <span [className]="getClassProdValueResponsive()">{{getValorProRata() | currency:this.getMoeda():
          'symbol'}}</span>
      </div>
      <div *ngIf="(getValorTotalProdutosSelecionados() > 0.0 && cobrarProdutoJuntoAdesaoMatricula())"
        class="grid2Columns19">
        <span [className]="getClassProdTextResponsive()">{{"planos.plano-recorrente.produtos"|translate}}:</span>
        <span [className]="getClassProdValueResponsive()">{{(getValorTotalProdutosSelecionados() +
          getPlanoSelecionado().valorProdutos)| currency:this.getMoeda():'symbol'}}</span>
      </div>
      <div *ngIf="getMesAnuidade() && (this.planoService.planoSelecionado.parcelasAnuidade.length === 0) "
        class="grid2Columns19">
        <span [className]="getClassAnuidTextResponsive()"
          *ngIf="getMesAnuidade() && (this.planoService.planoSelecionado.parcelasAnuidade.length === 0)">{{"planos.plano-recorrente.anuidade"|translate}}:</span>
        <span [className]="getClassAnuidValueResponsive() "
          *ngIf="getMesAnuidade() && (this.planoService.planoSelecionado.parcelasAnuidade.length === 0)">{{(getCupom() ?
          (getValorAnuidadeDesconto()| currency:this.getMoeda():'symbol') : ((getParcelarAnuidade() ?
          getValorAnuidadeParcelaUm() : getPlanoSelecionado().anuidade)| currency:this.getMoeda():'symbol'))}}</span>
      </div>
    </div>
  </div>
  <!--      FIM CONFIGURAÇÃO MARCADA-->
  <div class="linha-selec-total"></div>
  <a class="detalhe" id="detalhe" [style.color]="getConfig().cor" (click)="mostraParcelas()" id="detalharParcelas"
    type="button">{{"planos.plano-recorrente.detalhar-parcelas"|translate}}</a>
  <div id="parcelasPlanoDiv" style="display: none" #parcelasPlanoDiv>
    <ul>
      <li class="detalhe" [id]="id('detalhe-total',i)" *ngFor="let p of getParcelas()  let i = index "
        [attr.data-index]="i"> {{p.split('|')[1]}} <a [id]="id('simbolo',i)"
          *ngIf="!p.toString().indexOf('true|')" (click)="mostraDetalheaParcelas(i)"
          [style.color]="getConfig().cor">+</a>
        <div style="display: none" [ngStyle]="{'display': this.deatlhe[i] == true ? 'block' : 'none'}">
          <ul>
            <li class="detalhe" [id]="id('detalhe-parcela',i)">
              {{"planos.plano-recorrente.valor-da-parcela"|translate}}: {{this.parcelaIdex |
              currency:this.getMoeda():'symbol'}} </li>
            <li class="detalhe" [id]="id('detalhe-anuidade',i)">
              {{"planos.plano-recorrente.valor-da-anuidade"|translate}}: {{
              (this.planoService.planoSelecionado.parcelasAnuidade.length > 0)?
              (this.planoService.planoSelecionado.parcelasAnuidade[0].valor | currency:this.getMoeda():'symbol' ) : 0}}
            </li>
          </ul>
        </div>
      </li>
    </ul>
  </div>
  <div class="linha-selec-total"></div>
  <div class="span_2_of_2 detalhesplano">
    <!--      CUPOM DESCONTO-->
    <span *ngIf="getCupom()" class="plano-inclui">{{"planos.plano-recorrente.cupom-desconto"|translate}}<a
        class="remover-cupom" [style.color]="getConfig().cor"
        (click)="limparCupom()">[{{"planos.plano-recorrente.remover"|translate}}]</a></span>
    <div *ngIf="getCupom()" class="sub-cupom">
      <div class="nome-cupom">{{getCupom().numeroCupom.toUpperCase()}}</div>
      <span class="desconto">{{"planos.plano-recorrente.desconto-aplicado"|translate|uppercase}}:</span>
      <span *ngFor="let premio of  getCupom().listaPremios">
        <div *ngIf="exibirPremio(premio, this.planoService.planoSelecionado.nome.toUpperCase())">
          <span class="modalidade">{{premio.descricaoPremio}}:</span>
          <span *ngIf="premio.percentualDesconto != 0.0" class="valor-desc">{{premio.percentualDesconto}}%</span>
          <span *ngIf="premio.valorDesconto != 0.0" class="valor-desc">{{premio.valorDesconto|
            currency:this.getMoeda():'symbol'}}</span>
        </div>
      </span>
    </div>
    <div *ngIf="getCupom()" class="linha-selec-plano-prod"></div>
    <!--      DIA VENCIMENTO-->
    <span *ngIf="(this.formGroup.get('diavencimento') != null && this.formGroup.get('diavencimento').value)"
      class="produto-inclui">{{"planos.plano-recorrente.dia-de-vencimento"|translate}}</span>
    <div *ngIf="(this.formGroup.get('diavencimento') != null && this.formGroup.get('diavencimento').value)"
      class="sub-plano-produto-inclui">
      <span class="modalidade">{{this.formGroup.get('diavencimento').value}}
        {{"planos.plano-recorrente.de-todo-mes"|translate}}</span>
    </div>
    <div *ngIf="(this.formGroup.get('diavencimento') != null && this.formGroup.get('diavencimento').value)"
      class="linha-selec-plano-prod"></div>
    <!--      INICIO CONTRATO-->
    <span class="produto-inclui"
      *ngIf="getConfig() && getConfig().camposAdicionais.indexOf('INICIO_CONTRATO') > -1">{{"planos.plano-recorrente.inicio-do-contrato"|translate}}</span>
    <pacto-input *ngIf="getConfig() && getConfig().camposAdicionais.indexOf('INICIO_CONTRATO') > -1"
      idinput="iddatainiciocontrato" label="Informe uma data:" (focusout)="getValidarData()"
      [name]="'dataInicioContrato'" [type]="'date'" [pactoFormGroup]="formGroup"></pacto-input>
    <span class="plano-inclui">{{"planos.plano-recorrente.esse-plano-inclui"|translate}}</span>
    <div class="sub-plano-produto-inclui">
      <span *ngFor="let m of getPlanoSelecionado().modalidades" class="modalidade">
        <i [style.color]="getConfig().cor" class="tem pct-check-circle"></i> <span>{{m}}</span>
      </span>
    </div>
    <div class="linha-selec-plano-prod"></div>
    <div *ngIf="getPlanoSelecionado().produtos">
      <span class="produto-inclui">{{"planos.plano-recorrente.produtos-no-plano"|translate}}</span>
      <div class="sub-plano-produto-inclui">
        <span *ngFor="let p of getProdutosPlano()" class="modalidade">
          <i [style.color]="getConfig().cor" class="tem pct-check-circle"></i> <span>{{p}}</span>
        </span>
      </div>
    </div>
    <div *ngIf="getPlanoSelecionado().produtos" class="linha-selec-plano-prod"></div>
    <div *ngIf="apresentarValorAnuidade() && !getParcelarAnuidade()" class="divAnuidade">
      <span class="anuid-inclui">{{"planos.plano-recorrente.anuidade"|translate}}</span>
      <span class="total-anuid" [style.color]="getConfig().cor">{{(getCupom() ? (getValorAnuidadeDesconto()|
        currency:this.getMoeda():'symbol') : (getPlanoSelecionado().anuidade|
        currency:this.getMoeda():'symbol'))}}</span>
    </div>
    <div *ngIf="apresentarValorAnuidade() && !getParcelarAnuidade()" class="controle-top"></div>
    <div *ngIf="!apresentarValorAnuidade()" class="controle-top-default"></div>
    <div *ngIf="getParcelarAnuidade()">
      <span
        class="desc-anuid-parcelada">{{getParcelaDescricaoPluSing(getPlanoSelecionado().parcelasAnuidade.length)}}</span>
      <span *ngFor="let pa of getPlanoSelecionado().parcelasAnuidade">
        <div class="desc-anuid-parc">{{getAnuidadeDescricao(pa, getPlanoSelecionado().parcelasAnuidade.length)}}</div>
      </span>
    </div>
    <div *ngIf="!getParcelarAnuidade() && getPlanoSelecionado().anuidade > 0.0" class="divAnuidade">
      <span class="desc-anuid-inclui"
        *ngIf="getPlanoSelecionado().mesAnuidade.includes('Parcela')">{{"planos.plano-recorrente.a-anuidade-sera-cobrada-junto-com-a"|translate}}:</span>
      <span class="desc-anuid-inclui"
        *ngIf="!getPlanoSelecionado().mesAnuidade.includes('Parcela')">{{"planos.plano-recorrente.mes-em-que-sera-cobrada-a-anuidade"|translate}}:</span>
      <span class="desc-total-anuid">{{getMesAnoCobrancaAnuidade()}}</span>
      <span class="desc-anuid-inclui"
        *ngIf="!getPlanoSelecionado().mesAnuidade.includes('Parcela')">{{"planos.plano-recorrente.valor-da-anuidade"|translate}}:</span>
      <span class="desc-total-anuid">{{ getValorAnuindade() | currency:this.getMoeda():'symbol'}}</span>
    </div>
    <div *ngIf="getParcelarAnuidade()" class="linha-selec-anuid-parce-default"></div>
    <div *ngIf="!apresentarValorAnuidade() && !getParcelarAnuidade()" class="linha-selec-anuid-default"></div>
    <div *ngIf="apresentarValorAnuidade() && !getParcelarAnuidade()" class="linha-selec-anuid"></div>
    <div class="leiacontrato">
      <pacto-visualizar-documento [tipo]="'contrato'" [inside]="true" [plano]="getPlanoSelecionado().codigo"
        [textoLink]="'global.leiacontrato'|translate"></pacto-visualizar-documento>
    </div>
  </div>
</div>
