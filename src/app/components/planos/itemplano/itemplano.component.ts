import { Component, Input, OnInit } from '@angular/core';
import { Plano } from '@base-core/plano/plano.model';
import { PlanoService } from '@base-core/plano/plano.service';
import { EmpresaService } from '@base-core/empresa/empresa.service';
import { Config } from '@base-core/empresa/config.model';
import {FacebookPixelService} from '@base-core/analytics/facebook-pixel.service';
declare let fbq: Function;

@Component({
  selector: 'pacto-item-plano',
  templateUrl: './itemplano.component.html',
  styleUrls: ['./itemplano.component.scss']
})
export class ItemplanoComponent implements OnInit {
  @Input() plano: Plano;
  @Input() apenasDetalhar = false;
  detalhando: boolean;
  constructor(private planoService: PlanoService,
    private empresaService: EmpresaService,
    private pixelService: FacebookPixelService) { }

  ngOnInit() {
  }

  getPlanoSelecionado(): Plano {
    return this.planoService.planoSelecionado;
  }
  getConfig(): Config {
    return this.empresaService.config;
  }

  selecionarPlano(pl): void {
    this.clearListaPlanosSelecionados();
    this.planoService.planoSelecionado = pl;

    const params = {
      planoNome: this.getPlanoSelecionado().nome,
      planoValor: this.getPlanoSelecionado().valorTotalDoPlano
    };
    this.pixelService.triggerEventFacebookSelecionarPlano(params);
  }
  desselecionarPlano(pl): void {
    this.planoService.planoSelecionado = null;
  }
  detalhar(): void {
    this.detalhando = true;
  }
  fecharDetalhar(): void {
    this.detalhando = false;
  }

  getMoeda(): string {
    return this.empresaService.unidadeSelecionada.moeda
  }

  clearListaPlanosSelecionados() {
    return this.planoService.planoSelecionado = null
  }

  getApresentarValorDoPlano(): boolean {
    return this.empresaService.config.apresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano;
  }
}
