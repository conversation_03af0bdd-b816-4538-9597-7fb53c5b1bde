<div class="caixa-plano row"
     [ngClass]="{'selecionado' : (getPlanoSelecionado() && getPlanoSelecionado().codigo == plano.codigo && apenasDetalhar === false), 'detalhando' : (detalhando || apenasDetalhar === true) } ">

  <div id="resumo" style="display: flex; flex-direction: row; justify-content: space-between; min-width: 60%; min-width: 60%; padding-right: 10px; padding-left: 10px;">
    <div style="display: flex; flex: 2;">
      <i class="pct-package" style="margin-left: 0px;"></i>
      <span style="margin-top: 7px;" class="nome-plano">{{plano.nome.toLowerCase()}}</span>
    </div>

    <div style="display: flex; flex: 1;">
      <div class="info-plano">
        <span *ngIf="plano.regimeRecorrencia" class="titulo">{{ "planos.itemplano.adesao" | translate}}</span>
        <span *ngIf="plano.regimeRecorrencia" class="valor">{{plano.adesao| currency: this.getMoeda() :'symbol'}}</span>
        <span *ngIf="!plano.regimeRecorrencia" class="titulo">{{ "planos.itemplano.matricula" | translate}}</span>
        <span *ngIf="!plano.regimeRecorrencia" class="valor">{{plano.matricula| currency: this.getMoeda() :'symbol'}}</span>
      </div>
      <div class="info-plano">
        <span *ngIf="plano.regimeRecorrencia" class="titulo">{{ "planos.itemplano.anuidade" | translate}}</span>
        <span *ngIf="plano.regimeRecorrencia" class="valor">{{plano.anuidade| currency: this.getMoeda() :'symbol'}}</span>
        <span *ngIf="plano.qtdCreditoPlanoCredito" class="titulo">{{ "planos.itemplano.creditos" | translate}}</span>
        <span *ngIf="plano.qtdCreditoPlanoCredito" class="valor">{{plano.qtdCreditoPlanoCredito}}</span>
      </div>
      <div class="info-plano">
        <span *ngIf="plano.regimeRecorrencia && !plano.parcelamentoOperadora" class="titulo">{{ "planos.itemplano.recorrencia" | translate}}</span>
        <span *ngIf="plano.parcelamentoOperadora || !plano.regimeRecorrencia" class="titulo">{{ "planos.itemplano.duracao" | translate}}</span>
        <span *ngIf="plano.fidelidade == 0  && plano.quantidadeDeDiasDuracaoPlano === 0" class="valor">{{ "planos.itemplano.nenhuma" | translate}}</span>
        <span *ngIf="plano.fidelidade == 1 && plano.quantidadeDeDiasDuracaoPlano === 0" class="valor">{{ "planos.itemplano.um-mes" | translate}}</span>
        <span *ngIf="plano.fidelidade > 1  && plano.quantidadeDeDiasDuracaoPlano === 0" class="valor">{{plano.fidelidade}} {{ "planos.itemplano.meses" | translate}}</span>
        <span *ngIf="plano.quantidadeDeDiasDuracaoPlano > 0" class="valor">{{plano.quantidadeDeDiasDuracaoPlano}} {{ "planos.itemplano.dias" | translate}}</span>
      </div>
      <div class="info-plano">
        <span *ngIf="getApresentarValorDoPlano()" class="titulo">{{ "planos.itemplano.valor-total" | translate}}</span>
        <span *ngIf="getApresentarValorDoPlano()" class="valor">{{plano.valorTotalDoPlano | currency:this.getMoeda():'symbol'}}</span>
      </div>
    </div>
    </div>
  <div style="display: flex; flex: 2;">
    <div class="mensalidade" style="display: ruby">
      <div id="infoMensalidade" style="flex: 2; margin-top: 6px;">
      <span *ngIf="plano.primeiraParcela == plano.mensalidade && plano.vendaComTurma">{{ "planos.itemplano.a-partir-de" | translate}} </span>
      <span class="valor" *ngIf="plano.primeiraParcela != plano.mensalidade">{{plano.primeiraParcela| currency: this.getMoeda() :'symbol'}}</span>
      <span class="valor" *ngIf="plano.primeiraParcela == plano.mensalidade">{{plano.mensalidade| currency: this.getMoeda() :'symbol'}}</span>
      <span *ngIf="plano.primeiraParcela != plano.mensalidade">/1º {{ "planos.itemplano.mes" | translate}}*</span>
      <span *ngIf="plano.primeiraParcela == plano.mensalidade">/{{ "planos.itemplano.mes" | translate}}</span>

      <span *ngIf="(getPlanoSelecionado() && getPlanoSelecionado().codigo == plano.codigo) && apenasDetalhar === false" class="selecionar selecionado"
            [style.background-color]="getConfig()?.cor" [style.margin]="'9px'">
        <span class="btn" (click)="desselecionarPlano(plano)">{{ "planos.itemplano.selecionado" | translate|uppercase }}</span>
        <span class="detalhes"><i class="pct-check-circle"></i> </span>
      </span>
      </div>
      <span *ngIf="(!getPlanoSelecionado() || getPlanoSelecionado().codigo != plano.codigo) && apenasDetalhar === false" class="selecionar"
            [style.background-color]="getConfig()?.cor" [style.margin]="'9px'">
        <span class="btn" (click)="selecionarPlano(plano)">{{ "planos.itemplano.selecionar"  | translate |uppercase}}</span>
        <span class="detalhes" (click)="fecharDetalhar()" *ngIf="detalhando"><i class="pct-chevron-up"></i> </span>
        <span class="detalhes" (click)="detalhar()" *ngIf="!detalhando"><i class="pct-chevron-down"></i> </span>
      </span>
    </div>
  </div>

  <div id="detalhes" style="width: -webkit-fill-available;">
    <div class="span_2_of_2 detalhesplano">
      <div *ngIf="plano.descricaoEncantamento">{{plano.descricaoEncantamento}}</div>

      <div id="detalhesModalidades" style="display: flex; flex-direction: column; align-items: start;">
        <span style="font-weight: bold;">{{ "planos.itemplano.esse-plano-inclui" | translate}}:</span>
        <span *ngFor="let m of plano.modalidades" class="modalidade">
         <i [style.color]="getConfig()?.cor" class="tem pct-check-circle"></i> <span>{{m}}</span>
        </span>
      </div>


      <div *ngIf="plano.produtos" [style.margin-left]="'40px'" id="detalhesPlanoProdutos" style="display: flex; flex-direction: column; align-items: start;">
        <span style="font-weight: bold;">{{ "planos.itemplano.produtos-no-plano" | translate}}:</span>
        <span>{{plano.produtos}}</span>
      </div>

      <div *ngIf="plano.qtdCreditoPlanoCredito && apenasDetalhar === false">
        <span class="inclui">{{ "planos.itemplano.quantidade-de-creditos-a-adquirir" | translate}}:</span>
        <span>{{plano.qtdCreditoPlanoCredito}}</span>
      </div>

      <div *ngIf="plano.anuidade > 0.0">
        <span class="inclui">{{ "planos.itemplano.mes-em-que-sera-cobrada-a-anuidade" | translate}}:</span>
        <span>{{plano.mesAnuidade}}</span>
      </div>

      <div *ngIf="plano.primeiraParcela != plano.mensalidade">
        <span class="inclui">* {{plano.mensalidade| currency: this.getMoeda() :'symbol'}} {{ "planos.itemplano.nos-outros-meses" | translate}}</span>
      </div>

      <div class="leiacontrato">
        <pacto-visualizar-documento [tipo]="'contrato'"
                                    [plano]="plano.codigo"
                                    [textoLink]="'global.leiacontrato'|translate"></pacto-visualizar-documento>
      </div>
    </div>
  </div>

</div>

