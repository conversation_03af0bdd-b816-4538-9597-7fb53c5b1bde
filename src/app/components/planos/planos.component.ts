import { Component, OnInit } from '@angular/core';
import {Plano} from '@base-core/plano/plano.model';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {ActivatedRoute} from '@angular/router';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {Config} from '@base-core/empresa/config.model';
import {Empresa} from '@base-core/empresa/empresa.model';
import { Router } from '@angular/router';
import {PaginaVendasOnLine} from "@base-core/negociacao/acesso-pagina";
import {ClienteService} from '@base-core/cliente/cliente.service';
@Component({
  selector: 'pacto-planos',
  templateUrl: './planos.component.html',
  styleUrls: ['./planos.component.scss']
})
export class PlanosComponent implements OnInit {
  planos: Array<Plano> = [];

  constructor(private negociacaoService: NegociacaoService,
              private route: ActivatedRoute,
              private empresaService: EmpresaService,
              private clienteService: ClienteService,
              private router: Router) {

    const currentUrl = window.location.href;
    // Split na URL usando 'utm_data' como referência
    const splitUrl = currentUrl.split('utm_data');
    const urlPossuiUtmDataNaUrl = splitUrl.length > 1;

    if (urlPossuiUtmDataNaUrl) {
      const part2 = 'utm_data' + splitUrl[1];
      this.negociacaoService.utm_data = part2;
    }

    window.localStorage.removeItem('usuario');
    this.route.queryParams.subscribe(params => {
      if (params['k'] && params['un']) {
        this.clienteService.chave = params['k'];
        this.negociacaoService.chave = params['k'];
        this.negociacaoService.codunidade = params['un'];
        window.localStorage.setItem('chave', params['k']);
        window.localStorage.setItem('unidade', params['un']);
      } else {
        this.negociacaoService.chave = window.localStorage.getItem('chave');
        this.negociacaoService.codunidade = window.localStorage.getItem('unidade');
      }
      if (params['ct']) {
        window.localStorage.setItem('planoCategoria', params['ct']);
        this.negociacaoService.categoriaPlano = Number(params['ct']);
      }
      if (params['evento']) {
        this.negociacaoService.codigoEvento = Number(params['evento']);
      }
      if (params['us']) {
        this.negociacaoService.usuarioResponsavel = params['us'];
        window.localStorage.setItem('usuario', params['us']);
      } else {
        window.localStorage.removeItem('usuario');
      }
    });
  }

  ngOnInit() {
    this.loadUnidade();
    this.negociacaoService.registrarAcessoPagina(PaginaVendasOnLine[PaginaVendasOnLine.PLANO], this.router.url);
    localStorage.removeItem('limparDadosCheckout');
  }
  loadUnidade(): void {
    if (!this.empresaService.unidadeSelecionada) {
      this.empresaService.obterEmpresa(
        this.negociacaoService.chave,
        this.negociacaoService.codunidade)
          .subscribe(data => this.empresaService.unidadeSelecionada = data);

      this.empresaService.obterConfigs(this.negociacaoService.chave,
        this.negociacaoService.codunidade).subscribe(data => this.empresaService.config = data);
    }
  }
  obterTituloCheckout(): string {
    return (this.empresaService != null &&
    this.empresaService.config != null &&
    this.empresaService.config.titulocheckout != null ? this.empresaService.config.titulocheckout : 'Bora Treinar?');
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  getUnidadeSelecionada(): Empresa {
    return this.empresaService.unidadeSelecionada;
  }

}
