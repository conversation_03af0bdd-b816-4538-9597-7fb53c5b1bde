import { AfterViewInit, Component, OnInit } from '@angular/core';
import { Plano } from '@base-core/plano/plano.model';
import { PlanoService } from '@base-core/plano/plano.service';
import { NegociacaoService } from '@base-core/negociacao/negociacao.service';
import { EmpresaService } from '@base-core/empresa/empresa.service';

@Component({
  selector: 'pacto-lista-planos',
  templateUrl: './listaplanos.component.html',
  styleUrls: ['./listaplanos.component.scss']
})
export class ListaplanosComponent implements OnInit, AfterViewInit {

  planos: Array<Plano> = [];

  constructor(private planoService: PlanoService,
    private empresaService: EmpresaService,
    private negociacaoService: NegociacaoService) { }

  ngOnInit() {
    this.loadPlanos();
    this.clearListaPlanosSelecionados();
  }
  ngAfterViewInit() {
    this.clearListaPlanosSelecionados();
  }
  loadPlanos(): void {
    if (this.negociacaoService.categoriaPlano != null && this.negociacaoService.categoriaPlano > 0) {
      this.planos = new Array<Plano>();
      this.planoService.obterPlanos(this.negociacaoService.chave,
        this.negociacaoService.codunidade).subscribe(data => {
          console.log("If", data)
          data.forEach(plano => {
            if (plano.categorias && plano.categorias.length > 0) {
              plano.categorias.forEach(categoriaPlano => {
                if (categoriaPlano === this.negociacaoService.categoriaPlano) {
                  this.planos.push(plano);
                }
              });
            }
          });
        });
      this.planoService.planos = this.planos;
    } else {
      this.selecionarPlano(null);
      this.planoService.obterPlanos(this.negociacaoService.chave, this.negociacaoService.codunidade).subscribe(data => {
        this.planos = data;
        this.planoService.planos = this.planos;
      });
    }
  }
  selecionarPlano(pl): void {
    this.planoService.planoSelecionado = pl;
  }

  obterTituloCheckout(): string {
    return (this.empresaService != null &&
      this.empresaService.config != null &&
      this.empresaService.config.titulocheckout != null ? this.empresaService.config.titulocheckout : 'Bora Treinar?');
  }
  clearListaPlanosSelecionados() {
    return this.planoService.planoSelecionado = null
  }
}
