<div class="caixa-plano-selecionado">
  <span class="titulo">{{"planos.plano-inicio-futuro.plano-selecionado"|translate}}: </span>
  <span class="nome-planoResumo" [style.color]="getConfig().cor">{{getPlanoSelecionado().nome.toLowerCase()}}</span>

  <div class="container" [style.border-left-color]="getConfig().cor">

    <div class="arrow-right" [style.border-left-color]="getConfig().cor"></div>

    <!--      PLANO RECORRENTE CONFIGURAÇÃO DETALHAR PARCELAS DESMARCADA-->
    <div *ngIf="!detalharParcelas() && getPlanoSelecionado().regimeRecorrencia" class="span_5_of_5 ta-center pedido ta-center">
      <span class="titulo" *ngIf="!cobrarPrimeiraParcelaSempre() && getPlanoSelecionado().adesao > 0.0">{{"planos.plano-inicio-futuro.adesao"|translate}}:</span>
      <span class="primeiraparcela" [style.color]="getConfig().cor"
            *ngIf="!cobrarPrimeiraParcelaSempre() && getPlanoSelecionado().adesao > 0.0">{{(getPlanoSelecionado().adesao)| currency:this.getMoeda():'symbol'}}</span>
      <span class="titulo" *ngIf="cobrarPrimeiraParcelaSempre()">{{"planos.plano-inicio-futuro.o-valor-a-ser-cobrado-agora-sera"|translate}}:</span>
      <span *ngIf="!getPlanoSelecionado().parcelamentoOperadora && cobrarPrimeiraParcelaSempre()" class="primeiraparcela" [style.color]="getConfig().cor">{{ ((getCupom() ?
        getValorASerCobradoAgora() : getValorPrimeiraParcela()) | currency: this.getMoeda(): 'symbol')}}</span>
      <span *ngIf="getPlanoSelecionado().parcelamentoOperadora && cobrarPrimeiraParcelaSempre()" class="primeiraparcela" [style.color]="getConfig().cor"><span>{{(getCupom() ?
        getValorASerCobradoAgora() : getValorPrimeiraParcela()) | currency:this.getMoeda():'symbol'}}</span></span>
    </div>
    <!--      FIM CONFIGURAÇÃO DESMARCADA-->

    <!--      PLANO RECORRENTE CONFIGURAÇÃO DETALHAR PARCELAS MARCADA-->
    <div *ngIf="detalharParcelas() && getPlanoSelecionado().regimeRecorrencia" class="span_5_of_5 ta-center pedido ta-center">
      <span class="titulo" *ngIf="getPlanoSelecionado().primeiraParcela > 0.0">{{"planos.plano-inicio-futuro.mensalidade-1"|translate}}:</span>
      <span class="primeiraparcela" [style.color]="getConfig().cor">{{ ((getCupom() ?
        getValorPrimeiraParcelaDesconto() : getValorPrimeiraParcela()) | currency: this.getMoeda(): 'symbol')}}</span>
      <span class="titulo" *ngIf="getPlanoSelecionado().adesao > 0.0">{{"planos.plano-inicio-futuro.adesao"|translate}}:</span>
      <span class="primeiraparcela" [style.color]="getConfig().cor"
            *ngIf="getPlanoSelecionado().adesao > 0.0">{{(getPlanoSelecionado().adesao)| currency:this.getMoeda():'symbol'}}</span>
      <span class="titulo" *ngIf="(getValorTotalProdutosSelecionados() > 0.0 || getPlanoSelecionado().valorProdutos > 0.0)">{{"planos.plano-inicio-futuro.produtos"|translate}}:</span>
      <span class="primeiraparcela" [style.color]="getConfig().cor"
            *ngIf="(getValorTotalProdutosSelecionados() > 0.0 || getPlanoSelecionado().valorProdutos > 0.0)">{{(getValorTotalProdutosSelecionados() + getPlanoSelecionado().valorProdutos)| currency:this.getMoeda():'symbol'}}</span>
      <span class="titulo"
            *ngIf="getPlanoSelecionado().anuidade > 0.0 && (getPlanoSelecionado().mesAnuidade == 'Parcela 1' || getPlanoSelecionado().anuidadeAgora)">{{"planos.plano-inicio-futuro.anuidade"|translate}}:</span>
      <span class="primeiraparcela" [style.color]="getConfig().cor"
            *ngIf="getPlanoSelecionado().anuidade > 0.0 && (getPlanoSelecionado().mesAnuidade == 'Parcela 1' || getPlanoSelecionado().anuidadeAgora)">{{(getPlanoSelecionado().anuidade)| currency:this.getMoeda() :'symbol'}}</span>
      <span class="titulo">{{"planos.plano-inicio-futuro.o-valor-a-ser-cobrado-agora-sera"|translate}}:</span>
      <span class="primeiraparcela" [style.color]="getConfig().cor" *ngIf="!cobrarPrimeiraParcelaSempre()">{{getValorPrimeiraParcela() | currency:this.getMoeda():'symbol'}}</span>
      <span class="primeiraparcela" [style.color]="getConfig().cor" *ngIf="cobrarPrimeiraParcelaSempre()">{{ ((getCupom() ?
        getValorPrimeiraParcelaDesconto() : getValorPrimeiraParcela()) | currency: this.getMoeda(): 'symbol')}}</span>
    </div>
  </div>
  <!--      FIM CONFIGURAÇÃO MARCADA-->

  <!--      PLANO CONVENCIONAL CONFIGURAÇÃO DETALHAR PARCELAS DESMARCADA-->
  <div *ngIf="!detalharParcelas() && !getPlanoSelecionado().regimeRecorrencia" class="span_5_of_5 ta-center pedido ta-center">
    <span class="titulo" *ngIf="!cobrarPrimeiraParcelaSempre() && getPlanoSelecionado().matricula > 0.0">{{"planos.plano-inicio-futuro.matricula"|translate}}:</span>
    <span class="primeiraparcela" [style.color]="getConfig().cor"
          *ngIf="!cobrarPrimeiraParcelaSempre() && getPlanoSelecionado().matricula > 0.0">{{(getPlanoSelecionado().matricula)| currency:this.getMoeda():'symbol'}}</span>

    <span class="titulo" *ngIf="cobrarPrimeiraParcelaSempre()">{{"planos.plano-inicio-futuro.o-valor-a-ser-cobrado-agora-sera"|translate}}:</span>
    <span *ngIf="!getPlanoSelecionado().parcelamentoOperadora" class="primeiraparcela" [style.color]="getConfig().cor">{{(getPlanoSelecionado().matricula + getPlanoSelecionado().primeiraParcela)| currency:this.getMoeda():'symbol'}}</span>
    <span *ngIf="getPlanoSelecionado().parcelamentoOperadora" class="primeiraparcela" [style.color]="getConfig().cor"><span>{{getValorTotal() + (getPlanoSelecionado().mesAnuidade && getPlanoSelecionado().anuidadeAgora ? getPlanoSelecionado().anuidade : 0) | currency:this.getMoeda():'symbol'}}</span></span>
  </div>
  <!--      FIM CONFIGURAÇÃO DESMARCADA-->

  <!--      PLANO CONVENCIONAL CONFIGURAÇÃO DETALHAR PARCELAS MARCADA-->
  <div *ngIf="detalharParcelas() && !getPlanoSelecionado().regimeRecorrencia" class="span_5_of_5 ta-center pedido ta-center">
    <span class="titulo" *ngIf="getPlanoSelecionado().primeiraParcela > 0.0">{{"planos.plano-inicio-futuro.mensalidade-1"|translate}}:</span>
    <span class="primeiraparcela" [style.color]="getConfig().cor">{{(getPlanoSelecionado().primeiraParcela)| currency:this.getMoeda():'symbol'}}</span>
    <span class="titulo" *ngIf="getPlanoSelecionado().matricula > 0.0">{{"planos.plano-inicio-futuro.matricula"|translate}}:</span>
    <span class="primeiraparcela" [style.color]="getConfig().cor"
          *ngIf="getPlanoSelecionado().matricula > 0.0">{{(getPlanoSelecionado().matricula)| currency:this.getMoeda():'symbol'}}</span>
    <span class="titulo" *ngIf="(getValorTotalProdutosSelecionados() > 0.0 || getPlanoSelecionado().valorProdutos > 0.0)">Produtos:</span>
    <span class="primeiraparcela" [style.color]="getConfig().cor"
          *ngIf="(getValorTotalProdutosSelecionados() > 0.0 || getPlanoSelecionado().valorProdutos > 0.0)">{{(getValorTotalProdutosSelecionados() + getPlanoSelecionado().valorProdutos)| currency:this.getMoeda():'symbol'}}</span>
    <span class="titulo">{{"planos.plano-inicio-futuro.o-valor-a-ser-cobrado-agora-sera"|translate}}:</span>
    <span class="primeiraparcela" [style.color]="getConfig().cor" *ngIf="!cobrarPrimeiraParcelaSempre()">{{(getValorTotalProdutosSelecionados() + getPlanoSelecionado().matricula)| currency:this.getMoeda():'symbol'}}</span>
    <span class="primeiraparcela" [style.color]="getConfig().cor" *ngIf="cobrarPrimeiraParcelaSempre()">{{(getValorTotalProdutosSelecionados() + getPlanoSelecionado().matricula + getPlanoSelecionado().primeiraParcela)| currency:this.getMoeda():'symbol'}}</span>
  </div>
<!--      FIM CONFIGURAÇÃO MARCADA-->

  <!--      CONFIGURAÇÃO DETALHAR PARCELAS MARCADA-->
  <a class="detalhe" [style.color]="getConfig().cor" (click)="mostraParcelas()" type="button">{{"planos.plano-inicio-futuro.detalhar-parcelas"|translate}}</a>
  <div id="parcelasdiv" #parcelasdiv>
        <span *ngFor="let p of getParcelas()" class="detalhe">
          <span>{{p}}</span>
        </span>
  </div>
  <!--      CONFIGURAÇÃO DETALHAR PARCELAS MARCADA-->

  <!--      CUPOM DESCONTO-->
  <div class="linha-selec-total"></div>
  <span *ngIf="getCupom()" class="plano-inclui">{{"planos.plano-inicio-futuro.cupom-desconto"|translate}}<a
    class="remover-cupom" [style.color]="getConfig().cor"
    (click)="limparCupom()">[{{"planos.plano-inicio-futuro.remover"|translate}}]</a></span>
  <div *ngIf="getCupom()" class="sub-cupom">
    <div class="nome-cupom">{{getCupom().numeroCupom.toUpperCase()}}</div>
    <span class="desconto">{{"planos.plano-inicio-futuro.desconto-aplicado"|translate|uppercase}}:</span>
    <span *ngFor="let premio of  getCupom().listaPremios">
        <div *ngIf="exibirPremio(premio, this.planoService.planoSelecionado.nome.toUpperCase())">
          <span class="modalidade">{{premio.descricaoPremio}}:</span>
          <span *ngIf="premio.percentualDesconto != 0.0" class="valor-desc">{{premio.percentualDesconto}}%</span>
          <span *ngIf="premio.valorDesconto != 0.0" class="valor-desc">{{premio.valorDesconto|
            currency:this.getMoeda():'symbol'}}</span>
        </div>
      </span>
  </div>
  <div *ngIf="getCupom()" class="linha-selec-plano-prod"></div>
  <!--     FIM CUPOM DESCONTO-->

  <div *ngIf="(this.formGroup.get('diavencimento') != null && this.formGroup.get('diavencimento').value)" class="span_5_of_5 ta-center pedido ta-center">
    <span class="titulo">{{"planos.plano-inicio-futuro.dia-de-vencimento"|translate}}:</span>
    <span class="primeiraparcela" [style.color]="getConfig().cor">{{this.formGroup.get('diavencimento').value}}</span>
  </div>

  <div class="span_2_of_2 detalhesplano">

    <div *ngIf="getPlanoSelecionado().descricaoEncantamento">{{getPlanoSelecionado().descricaoEncantamento}}</div>

    <span class="titulo">{{"planos.plano-inicio-futuro.esse-plano-inclui"|translate}}:</span>
    <span *ngFor="let m of getPlanoSelecionado().modalidades" class="modalidade">
       <i [style.color]="getConfig().cor" class="tem pct-check-circle"></i> <span>{{m}}</span>
    </span>

    <div *ngIf="getPlanoSelecionado().produtos">
      <span class="titulo">{{"planos.plano-inicio-futuro.produtos-no-plano"|translate}}:</span>
      <span *ngFor="let p of getProdutosPlano()" class="modalidade">
       <i [style.color]="getConfig().cor" class="tem pct-check-circle"></i> <span>{{p}}</span>
    </span>
    </div>

    <div *ngIf="getPlanoSelecionado().anuidade > 0.0">
      <span class="titulo"
            *ngIf="detalharParcelas() && getPlanoSelecionado().anuidade > 0.0 && (getPlanoSelecionado().mesAnuidade != 'Parcela 1' && !getPlanoSelecionado().anuidadeAgora)">{{"planos.plano-inicio-futuro.anuidade"|translate}}:</span>
      <span class="primeiraparcela" [style.color]="getConfig().cor"
            *ngIf="detalharParcelas() && getPlanoSelecionado().anuidade > 0.0 && (getPlanoSelecionado().mesAnuidade != 'Parcela 1' && !getPlanoSelecionado().anuidadeAgora)">{{(getPlanoSelecionado().anuidade)| currency:this.getMoeda():'symbol'}}</span>
      <span class="titulo">{{"planos.plano-inicio-futuro.mes-em-que-sera-cobrada-a-anuidade"|translate}}:</span>
      <span [style.color]="getConfig().cor" class="primeiraparcela">{{getMesAnoCobrancaAnuidade()}}</span>
    </div>
  </div>

  <div class="leiacontrato">
    <pacto-visualizar-documento [tipo]="'contrato'" [inside]="true"
                                [plano]="getPlanoSelecionado().codigo"
                                [textoLink]="'global.leiacontrato'|translate"></pacto-visualizar-documento>
  </div>
</div>
