import {Component, Input, OnInit, ElementRef, ViewChild} from '@angular/core';
import {Router} from '@angular/router';
import {PlanoService} from '@base-core/plano/plano.service';
import {Plano} from '@base-core/plano/plano.model';
import {Config} from '@base-core/empresa/config.model';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {FormGroup, Validators} from '@angular/forms';
import {ProdutoService} from '@base-core/produto/produto.service';
import {Cupom} from '@base-core/cupom-desconto/cupom.model';
import {PremioCupom} from '@base-core/cupom-desconto/premio-cupom.model';

@Component({
  selector: 'pacto-plano-inicio-futuro',
  templateUrl: './plano-inicio-futuro.component.html',
  styleUrls: ['./plano-inicio-futuro.component.scss']
})
export class PlanoInicioFuturoComponent implements OnInit {
  @Input() formGroup: FormGroup;
  @ViewChild('parcelasdiv') parcelasContainer: ElementRef<HTMLDivElement>;
  router: string;

  constructor(private _router: Router,
              private empresaService: EmpresaService,
              private negociacaoService: NegociacaoService,
              private produtoService: ProdutoService,
              private planoService: PlanoService) {
    this.router = _router.url;
  }

  ngOnInit() {
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  getMoeda(): string {
    return this.empresaService.unidadeSelecionada.moeda;
  }

  getPlanoSelecionado(): Plano {
    return this.planoService.planoSelecionado;
  }

  getMesAnoCobrancaAnuidade(): string {
    return (this.planoService.planoSelecionado ? this.planoService.planoSelecionado.mesAnuidade : '') +
      (this.negociacaoService.anoCobrancaAnuidade ? ' / ' + this.negociacaoService.anoCobrancaAnuidade : '');
  }

  detalharParcelas(): boolean {
    return ((this.empresaService.config.detalharParcelaTelaCheckout != null &&
      this.empresaService.config.detalharParcelaTelaCheckout.valueOf() === 'true') ? true : false);
  }

  getProdutosPlano(): string[] {
    return this.getPlanoSelecionado().produtos.split('<br/>');
  }

  cobrarPrimeiraParcelaSempre(): boolean {
    return ((this.empresaService.config.cobrarPrimeiraParcelaCompra != null &&
      this.empresaService.config.cobrarPrimeiraParcelaCompra.valueOf() === 'true') ? true : false);
  }

  getValorTotal(): number {
    return this.negociacaoService.valorFinalContrato;
  }

  getValorTotalProdutosSelecionados(): number {
    return this.produtoService.getValorTotalProdutosSelecionados();
  }

  mostraParcelas() {
    console.log('Element Display 13: ' + this.parcelasContainer.nativeElement);
    const display = this.parcelasContainer.nativeElement.style.display;
    console.log('Display show 14: ' + display);
    if (display !== 'none') {
      this.parcelasContainer.nativeElement.style.display = 'none';
    } else {
      this.parcelasContainer.nativeElement.style.display = 'block';
    }
  }

  getParcelas(): string[] {
    const valores: Array<string> = [];
    let anoEMes;
    let descricaoParcela: Array<string> = [];
    if (!this.cobrarPrimeiraParcelaSempre() && this.getPlanoSelecionado().inicioFuturo) {
      //se não cobra a primeira parcela agora, e é inicio futuro, a primeira parcela do plano entra na lista de parcelas
      let numeroParcela = 0;
      for (let i = 0; i < this.negociacaoService.parcelas.length; i++) {
        numeroParcela++;
        anoEMes = this.negociacaoService.parcelas[i].descricao.split('-');
        descricaoParcela = anoEMes[1];
        valores.push(numeroParcela + 'ª Parcela -' + descricaoParcela + ' ' + this.getMoeda() + ' ' +
          this.negociacaoService.parcelas[i].valor.toFixed(2));
      }
      return valores;
    } else {
      let numeroParcela = 1;
      for (let i = 1; i < this.negociacaoService.parcelas.length; i++) {
        numeroParcela++;
        anoEMes = this.negociacaoService.parcelas[i].descricao.split('-');
        descricaoParcela = anoEMes[1];
        valores.push(numeroParcela + 'ª Parcela -' + descricaoParcela + ' ' + this.getMoeda() + ' ' +
          this.negociacaoService.parcelas[i].valor.toFixed(2));
      }
      return valores;
    }
  }

  getCupom(): Cupom {
    return this.negociacaoService.cupom;
  }

  getValorAnuidadeParcelaUm(): number {
    for (let i = 0; i < this.getPlanoSelecionado().parcelasAnuidade.length; i++) {
      if (this.getPlanoSelecionado().parcelasAnuidade[i].numero === 1) {
        return this.getPlanoSelecionado().parcelasAnuidade[i].valor;
      }
    }
    return 0;
  }

  getValorPrimeiraParcela(): number {
    let valorRetornar = this.validarCobrarPrimeiraParcelaSempre() ? this.planoService.planoSelecionado.primeiraParcela : 0;
    valorRetornar += this.getCupom() ? this.getValorAdesaoDescontoPrimeiraParcela() : this.getPlanoSelecionado().adesao;
    valorRetornar += this.cobrarProdutoJuntoAdesaoMatricula() ? this.getPlanoSelecionado().valorProdutos : 0;
    valorRetornar += (this.getPlanoSelecionado().mesAnuidade && this.getPlanoSelecionado().anuidadeAgora ?
      (this.getCupom() ? this.getValorAnuidadeDescontoPrimeiraParcela() : (this.getParcelarAnuidade() ?
        this.getValorAnuidadeParcelaUm() : this.getPlanoSelecionado().anuidade)) : 0);
    valorRetornar += this.getValorTotalProdutosSelecionados();
    return valorRetornar;
  }

  getValorAnuidadeDescontoPrimeiraParcela(): number {
    let temPremio = false;
    const anuidade = this.getPlanoSelecionado().anuidade;
    for (let i = 0; i < this.getListaPremios().length; i++) {
      if (this.getListaPremios()[i].descricaoPremio === 'ANUIDADE PLANO RECORRENTE') {
        temPremio = true;
        if (this.getListaPremios()[i].percentualDesconto !== 0.0) {
          return ((anuidade / 100) * this.getListaPremios()[i].percentualDesconto);
        } else if (this.getListaPremios()[i].valorDesconto !== 0.0) {
          return anuidade - this.getListaPremios()[i].valorDesconto;
        } else {
          return anuidade;
        }
      }
    }
    return temPremio ? anuidade : 0;
  }

  getListaPremios(): PremioCupom[] {
    const premioCupom = [];
    return this.negociacaoService.cupom === undefined ? premioCupom : this.negociacaoService.cupom.listaPremios;
  }

  getValorPrimeiraParcelaDesconto(): number {
    let parcela1 = this.validarCobrarPrimeiraParcelaSempre() ? this.planoService.planoSelecionado.primeiraParcela : 0;
    const planoEscolhidoAluno = this.planoService.planoSelecionado.nome;
    let temDescontoParcela1PlanoEspecifico = false;

    // ==== INICIO DESCONTO PARCELA 1 ====
    // Existe dois for por ter dois cenários de Cupom, sendo o primeiro for para Desconto com Plano Especifico
    for (let i = 0; i < this.getListaPremios().length; i++) {
      const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
      const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
      if (descricaoPremio === 'PARCELA 1' && planoEscolhidoAluno.toUpperCase() === planoPercorridoArray.toUpperCase()) {
        const percentualDesconto = this.getListaPremios()[i].percentualDesconto;
        const valorDesconto = this.getListaPremios()[i].valorDesconto;
        if (percentualDesconto !== 0.0) {
          parcela1 = parcela1 - ((parcela1 * percentualDesconto) / 100);
          temDescontoParcela1PlanoEspecifico = true;
        } else if (valorDesconto !== 0.0) {
          parcela1 = parcela1 - valorDesconto;
          temDescontoParcela1PlanoEspecifico = true;
        } else {
          parcela1 = parcela1;
          temDescontoParcela1PlanoEspecifico = true;
        }
      }
    }
    // Existe dois for por ter dois cenários de Cupom, sendo o segundo for para Desconto sem Plano Especifico
    if (!temDescontoParcela1PlanoEspecifico) {
      for (let i = 0; i < this.getListaPremios().length; i++) {
        const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
        const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
        if (descricaoPremio === 'PARCELA 1' && planoPercorridoArray === '') {
          const percentualDesconto = this.getListaPremios()[i].percentualDesconto;
          const valorDesconto = this.getListaPremios()[i].valorDesconto;
          if (percentualDesconto !== 0.0) {
            parcela1 = parcela1 - ((parcela1 * percentualDesconto) / 100);
          } else if (valorDesconto !== 0.0) {
            parcela1 = parcela1 - valorDesconto;
          } else {
            parcela1 = parcela1;
          }
        }
      }
    }
    // ==== FIM DESCONTO PARCELA 1 ====

    if(this.getPlanoSelecionado().anuidadeAgora) {
      parcela1 += this.valorAnuidadeParcela1();
    }

    parcela1 += this.getValorAdesaoDescontoPrimeiraParcela();

    return parcela1;
  }

  valorAnuidadeParcela1(): number {
    let anuidade = this.getPlanoSelecionado().anuidade;
    const planoEscolhidoAluno = this.planoService.planoSelecionado.nome;

    if (anuidade > 0) {
      let valorParcelaAnuidadeParcela1 = 0;
      const numeroParcelasAnuidade = this.planoService.planoSelecionado.parcelasAnuidade.length;
      for (let i = 0; i < this.planoService.planoSelecionado.parcelasAnuidade.length; i++) {
        if (this.planoService.planoSelecionado.parcelasAnuidade[i].parcela === 1) {
          valorParcelaAnuidadeParcela1 = this.planoService.planoSelecionado.parcelasAnuidade[i].valor;
        }
      }

      // If porque tem Anuidade Parcela e não parcelada
      // Existe dois for por ter dois cenários de Cupom, sendo o primeiro for para Desconto com Plano Especifico
      let temAnuidadeParcela1PlanoEspecifico = false;
      for (let i = 0; i < this.getListaPremios().length; i++) {
        const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
        const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
        if (descricaoPremio === 'ANUIDADE PLANO RECORRENTE' && planoEscolhidoAluno.toUpperCase() === planoPercorridoArray.toUpperCase()) {
          const percentualDesconto = this.getListaPremios()[i].percentualDesconto;
          const valorDesconto = this.getListaPremios()[i].valorDesconto;
          if (percentualDesconto !== 0.0) {
            if (valorParcelaAnuidadeParcela1 > 0) {
              anuidade = valorParcelaAnuidadeParcela1 - ((valorParcelaAnuidadeParcela1 * percentualDesconto) / 100);
            } else {
              anuidade = anuidade - ((anuidade * percentualDesconto) / 100);
            }
            temAnuidadeParcela1PlanoEspecifico = true;
          } else if (valorDesconto !== 0.0) {
            if (valorParcelaAnuidadeParcela1 > 0) {
              anuidade = valorParcelaAnuidadeParcela1 - (valorDesconto / numeroParcelasAnuidade);
            } else {
              anuidade = anuidade - (valorDesconto / numeroParcelasAnuidade);
            }
            temAnuidadeParcela1PlanoEspecifico = true;
          }
        }
      }
      // Existe dois for por ter dois cenários de Cupom, sendo o segundo for para Desconto sem Plano Especifico
      if (!temAnuidadeParcela1PlanoEspecifico) {
        for (let i = 0; i < this.getListaPremios().length; i++) {
          const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
          const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
          if (descricaoPremio === 'ANUIDADE PLANO RECORRENTE' && planoPercorridoArray === '') {
            const percentualDesconto = this.getListaPremios()[i].percentualDesconto;
            const valorDesconto = this.getListaPremios()[i].valorDesconto;
            if (percentualDesconto !== 0.0) {
              if (valorParcelaAnuidadeParcela1 > 0) {
                anuidade = valorParcelaAnuidadeParcela1 - ((valorParcelaAnuidadeParcela1 * percentualDesconto) / 100);
              } else {
                anuidade = anuidade - ((anuidade * percentualDesconto) / 100);
              }
              temAnuidadeParcela1PlanoEspecifico = true;
            } else if (valorDesconto !== 0.0) {
              if (valorParcelaAnuidadeParcela1 > 0) {
                anuidade = valorParcelaAnuidadeParcela1 - (valorDesconto / numeroParcelasAnuidade);
              } else {
                anuidade = anuidade - (valorDesconto / numeroParcelasAnuidade);
              }
              temAnuidadeParcela1PlanoEspecifico = true;
            }
          }
        }
      }
    }
    return anuidade > 0 ? anuidade : 0;
  }

  validarCobrarPrimeiraParcelaSempre(): boolean {
    const dia = new Date();
    const config = (this.getPlanoSelecionado().cobrarPrimeiraParcelaCompra != null &&
      this.getPlanoSelecionado().cobrarPrimeiraParcelaCompra) ? true : false;
    let diaVencimento = false;

    if (this.formGroup.get('diavencimento') != null && this.formGroup.get('diavencimento').value != null) {
      if (dia.getDate() === this.formGroup.get('diavencimento').value) {
        diaVencimento = true;
      }
    }
    return (config || diaVencimento);
  }

  getValorAdesaoDescontoPrimeiraParcela(): number {
    let temPremio = false;
    const adesao = this.getPlanoSelecionado().adesao;
    for (let i = 0; i < this.getListaPremios().length; i++) {
      if (this.getListaPremios()[i].descricaoPremio === 'ADESÃO PLANO RECORRENTE') {
        temPremio = true;
        if (this.getListaPremios()[i].percentualDesconto !== 0.0) {
          return adesao - ((adesao * this.getListaPremios()[i].percentualDesconto) / 100);
        } else if (this.getListaPremios()[i].valorDesconto !== 0.0) {
          return adesao - this.getListaPremios()[i].valorDesconto;
        } else {
          return adesao;
        }
      }
    }
    return temPremio ? adesao : 0;
  }

  cobrarProdutoJuntoAdesaoMatricula(): boolean {
    const dia = new Date();
    let configProdJunto = ((this.empresaService.config.cobrarProdutoJuntoAdesaoMatricula != null &&
      this.empresaService.config.cobrarProdutoJuntoAdesaoMatricula.valueOf() === 'true'));
    let diaVencimento: boolean;

    if (this.formGroup.get('diavencimento') != null && this.formGroup.get('diavencimento').value != null) {
      if (dia.getDate() !== this.formGroup.get('diavencimento').value && !configProdJunto) {
        diaVencimento = false;
      } else {
        configProdJunto = true;
        diaVencimento = true;
      }
    } else {
      configProdJunto = true;
      diaVencimento = true;
    }
    return (configProdJunto && diaVencimento);
  }

  getParcelarAnuidade(): boolean {
    return this.planoService.planoSelecionado != null &&
      this.planoService.planoSelecionado.parcelasAnuidade != null &&
      this.planoService.planoSelecionado.parcelasAnuidade.length > 0;
  }

  limparCupom() {
    this.negociacaoService.cupom = null;
  }

  exibirPremio(premio: PremioCupom, planoSelecionado: string): boolean {
    let existePremioPlanoEspecifico = false;
    let existePremioPlanoGeral = false;
    // Verifica se para esse premio, tem mais de uma configurado para Plano Especifico e Geral
    for (let i = 0; i < this.getListaPremios().length; i++) {
      const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
      const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
      if (descricaoPremio.toUpperCase() === premio.descricaoPremio.toUpperCase() &&
        planoPercorridoArray.toUpperCase() === planoSelecionado.toUpperCase()) {
        existePremioPlanoEspecifico = true;
      } else if (descricaoPremio.toUpperCase() === premio.descricaoPremio.toUpperCase() && planoPercorridoArray === '') {
        existePremioPlanoGeral = true;
      }
    }
    // Se premio que está validando, só tiver para especifico, retorna true para usar ele
    // Se premio que está validando, só tiver para geral, retorna true para usar ele
    // Se premio que está validando, tiver para especifico e geral, valida se o premio atual é o especifico, se for usa ele e se
    // não for não usa
    if (existePremioPlanoEspecifico && !existePremioPlanoGeral &&
      premio.descricaoPlano.toUpperCase() === planoSelecionado.toUpperCase()) {
      return true;
    } else if (!existePremioPlanoEspecifico && existePremioPlanoGeral) {
      return true;
    } else if (existePremioPlanoEspecifico && existePremioPlanoGeral &&
      planoSelecionado.toUpperCase() === premio.descricaoPlano.toUpperCase()) {
      return true;
    }
    return false;
  }

  getValorASerCobradoAgora(): number {
    let parcela1 = this.getValorPrimeiraParcelaDesconto();
    let adesao = this.getValorAdesaoNoValorASerCobradoAgora();

    return parcela1 + adesao;
  }

  getValorAdesaoNoValorASerCobradoAgora(): number {
    let adesaoComDesconto = this.getValorAdesaoDescontoPrimeiraParcela();
    let adesaoSemDesconto = this.getPlanoSelecionado().adesao;

    return adesaoComDesconto == 0.0 ? adesaoSemDesconto : adesaoComDesconto;
  }

}
