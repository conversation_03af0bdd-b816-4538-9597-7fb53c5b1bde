@import "../../../../assets/css/variaveis";
.caixa-plano-selecionado{
  text-align: center;
  border: solid 1px #bdc3c7;
  border-radius: 5px;
  padding: 20px 0px 25px 0px;
  margin: 20px 0;
}
.primeiraparcela{
  font-weight: 900;
  margin: 15px 0;
  font-size: 25px;
  display: block;
}
.leiacontrato{
  margin: 23px 0;
  font-size: 18px;
}
.nome-planoResumo{
  text-transform: capitalize;
  margin: 15px 0;
  font-size: 20px;
  font-weight: 900;
  display: block;
}
.nome-plano {
  i{
    margin-right: 10px;
  }
  text-transform: capitalize;
  font-size: 25px;
  vertical-align: middle;
  margin: 15px 0;
}
.mensalidade{
  font-weight: 900;
  margin-top: 10px;
}

.selecionar {
  box-shadow: 0 2px 3px 0 rgba(44, 52, 59, 0.1);
  cursor: pointer;
  width: 130px;
  color: #ffffff;
  height: 30px;
  border-radius: 4px;
  margin-left: 25px;
  display: inline-flex;
  .btn{
    text-align: center;
    font-size: 12px;
    width: 100px;
    line-height: 30px;
    vertical-align: middle;
  }
  .detalhes{
    line-height: 30px;
    width: 30px;
    vertical-align: middle;
    text-align: center;
  }
}
.modalidade, .detalhe{
  margin: 10px 7px;
  display: inline-block;
  i, span{
    vertical-align: middle;
  }
  i{
    margin-right: 10px;
  }
}
.titulo {
  display: block;
  font-weight: 300;
  color: $textoclaro;
}
.detalhe{
  display: block;
}

.linha-selec-total {
  margin-top: 15px;
  padding-top: 15px;
  border-top: solid 2px rgba(189, 195, 199, 0.3);
  font-size: 12px;
  width: 90%;
  margin-left: 5%;
}

@media only screen and (max-width: 748px) {
  .modal{
    color: $textoescuro;
    text-align: center;
    border-radius: 5px;
    border: $bordacinza 1px solid;
    background-color: #ffffff;
    width: calc(100% - 20px);
    margin: auto;
    height: auto;
    padding: 10px;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
  }
  .layer{
    display: block;
    background-color: rgba(0,0,0,.5);
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
  }

  .selecionar{
    margin: auto;
    margin-top: 15px;
    width: 98%;
    height: 40px;
    .btn{
      font-size: 14px;
      width: calc(100% - 40px);
      line-height: 40px;
    }
    .detalhes{
      line-height: 40px;
      width: 40px;
    }
  }
}


