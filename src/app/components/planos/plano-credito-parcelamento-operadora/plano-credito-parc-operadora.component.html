<div class="caixa-plano-selecionado">
  <span class="titulo">{{"planos.plano-credito-parc-operadora.plano-selecionado"|translate}}: </span>
  <span class="nome-planoResumo" [style.color]="getConfig().cor">{{getPlanoSelecionado().nome.toLowerCase()}}</span>

  <div class="container" [style.border-left-color]="getConfig().cor">

    <div class="arrow-right" [style.border-left-color]="getConfig().cor"></div>

    <!--      CONFIGURAÇÃO DETALHAR PARCELAS MARCADA-->
    <div *ngIf="detalharParcelas()"
      class="span_5_of_5 ta-center pedido ta-center">

      <!--TÍTULO PRIMEIRA PARCELA PLANO SEM CUPOM-->
      <span class="titulo">{{"planos.plano-credito-parc-operadora.mensalidade-1"|translate}}:</span>
      <!--PRIMEIRA PARCELA PLANO SEM CUPOM-->
      <span class="primeiraparcela p1" *ngIf="!getCupom()" [style.color]="getConfig().cor">{{(getPlanoSelecionado().primeiraParcela)| currency:this.getMoeda():'symbol'}}</span>
      <!--PRIMEIRA PARCELA PLANO COM CUPOM-->
      <span class="primeiraparcela p1" *ngIf="getCupom()" [style.color]="getConfig().cor">{{(getValorPrimeiraParcelaDesconto()| currency: this.getMoeda() :'symbol')}}</span>

      <!--MATRICULA-->
      <span class="titulo" *ngIf="getPlanoSelecionado().matricula > 0.0">{{"planos.plano-credito-parc-operadora.matricula"|translate}}:</span>
      <span class="primeiraparcela p2" [style.color]="getConfig().cor"
            *ngIf="getPlanoSelecionado().matricula > 0.0">{{(getPlanoSelecionado().matricula)| currency:this.getMoeda():'symbol'}}</span>
      <!--PRODUTOS-->
      <span class="titulo" *ngIf="(getValorTotalProdutosSelecionados() > 0.0 || getPlanoSelecionado().valorProdutos > 0.0)">{{"planos.plano-credito-parc-operadora.produtos"|translate}}:</span>
      <span class="primeiraparcela p3" [style.color]="getConfig().cor"
            *ngIf="(getValorTotalProdutosSelecionados() > 0.0 || getPlanoSelecionado().valorProdutos > 0.0)">{{(getValorTotalProdutosSelecionados() + getPlanoSelecionado().valorProdutos)| currency:this.getMoeda():'symbol'}}</span>
      <div>
        <label class="titulo" for="dividirMatricula"
          *ngIf="getPlanoSelecionado().nrVezesParcelarMatricula > 1 && getPlanoSelecionado().matricula">
          {{'planos.plano-credito-parc-operadora.dividir-matricula-em-quantas-vezes' | translate}} </label>
        <select name="dividirMatricula" class="custom-select-taxa-matricula" [(ngModel)]="vezesEscolhidasParcelarMatricula"
          (change)='this.getVezesEscolhidasParcelarMatricula()'
          *ngIf="getPlanoSelecionado().nrVezesParcelarMatricula > 1 && getPlanoSelecionado().matricula">
          <option
            *ngFor="let loop of counter(this.getPlanoSelecionado().nrVezesParcelarMatricula) ;let i= index, let f = first"
            [attr.selected]="first" [ngValue]="i+1">
            <p *ngIf="f"> {{'planos.plano-credito-parc-operadora.nao-dividir-taxa' | translate}}</p>
            <p *ngIf="!f"> {{'planos.plano-credito-parc-operadora.dividir-em' | translate}} {{i+1}}
              {{'planos.plano-credito-parc-operadora.parcelas-de' | translate}} {{(getPlanoSelecionado().matricula / [i+1]) |
              currency:this.getMoeda():'symbol' }}</p>
          </option>
        </select>
      </div>
    </div>


    <!--      FIM CONFIGURAÇÃO MARCADA-->

      <a [style.color]="getConfig().cor" (click)="mostraParcelas()"
        type="button">{{'planos.plano-credito-parc-operadora.detalhar-parcelas'|translate}}</a>
      <div id="parcelasdiv" #parcelasdiv>
        <span *ngFor="let p of getParcelas()" class="detalhe">
          <span [style.color]="getConfig().cor">{{p.split('|')[1]}}</span>
          <p *ngIf="p.split('|')[0] != 'false '" class="detalhamento-parcela">
            {{'planos.plano-credito-parc-operadora.taxa-matricula' | translate}}: {{(getPlanoSelecionado().matricula /
            this.planoService.vezesEscolhidasParcelarTaxaMatricula)|currency:this.getMoeda():'symbol'}} </p>
        </span>
      </div>

    <span class="titulo" >{{"planos.plano-credito-parc-operadora.valor-a-cobrar-parcelado"|translate}}:</span>

    <!--VALOR TOTAL A COBRAR PARCELADO SEM CUPOM-->
    <span class="primeiraparcela p4" *ngIf="!getCupom()" [style.color]="getConfig().cor"><span>{{getValorTotalProdutosSelecionados() + getValorTotal() +
    (getPlanoSelecionado().mesAnuidade && getPlanoSelecionado().anuidadeAgora ? getPlanoSelecionado().anuidade : 0) | currency:this.getMoeda():'symbol'}}</span></span>
    <!--VALOR TOTAL A COBRAR PARCELADO COM CUPOM-->
    <span class="primeiraparcela p4" *ngIf="getCupom()" [style.color]="getConfig().cor"><span>{{getValorTotalProdutosSelecionados() + getValorTotalPlanoComCupom() +
    (getPlanoSelecionado().mesAnuidade && getPlanoSelecionado().anuidadeAgora ? getPlanoSelecionado().anuidade : 0) | currency:this.getMoeda():'symbol'}}</span></span>


    <div *ngIf="getPlanoSelecionado().descricaoEncantamento">{{getPlanoSelecionado().descricaoEncantamento}}</div>


    <span class="titulo">{{"planos.plano-credito-parc-operadora.esse-plano-inclui"|translate}}:</span>
    <span *ngFor="let m of getPlanoSelecionado().modalidades" class="modalidade">
       <i [style.color]="getConfig().cor" class="tem pct-check-circle"></i> <span>{{m}}</span>
    </span>

    <div *ngIf="getPlanoSelecionado().produtos">
      <span class="titulo">{{"planos.plano-credito-parc-operadora.produtos-no-plano"|translate}}:</span>
      <span *ngFor="let p of getProdutosPlano()" class="modalidade">
       <i [style.color]="getConfig().cor" class="tem pct-check-circle"></i> <span>{{p}}</span>
    </span>
    </div>

    <div>
      <span class="titulo">{{"planos.plano-credito-parc-operadora.quantidade-de-creditos-a-adquirir"|translate}}:</span>
      <span class="primeiraparcela p5"
            [style.color]="getConfig().cor">{{getPlanoSelecionado().qtdCreditoPlanoCredito}}</span>
    </div>

  </div>

  <div class="leiacontrato">
    <pacto-visualizar-documento [tipo]="'contrato'" [inside]="true"
                                [plano]="getPlanoSelecionado().codigo"
                                [textoLink]="'global.leiacontrato'|translate"></pacto-visualizar-documento>
  </div>
</div>
