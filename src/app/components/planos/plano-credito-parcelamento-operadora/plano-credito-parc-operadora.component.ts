import {Component, Input, OnInit, ElementRef, ViewChild} from '@angular/core';
import {Router} from '@angular/router';
import {PlanoService} from '@base-core/plano/plano.service';
import {Plano} from '@base-core/plano/plano.model';
import {Config} from '@base-core/empresa/config.model';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {FormGroup, Validators} from '@angular/forms';
import {ProdutoService} from '@base-core/produto/produto.service';
import {Cupom} from '@base-core/cupom-desconto/cupom.model';
import {PremioCupom} from '@base-core/cupom-desconto/premio-cupom.model';

@Component({
  selector: 'pacto-plano-credito-parc-operadora',
  templateUrl: './plano-credito-parc-operadora.component.html',
  styleUrls: ['./plano-credito-parc-operadora.component.scss']
})
export class PlanoCreditoParcOperadoraComponent implements OnInit {
  vezesEscolhidasParcelarMatricula: number = 1

  @Input() formGroup: FormGroup;
  @ViewChild('parcelasdiv') parcelasContainer: ElementRef<HTMLDivElement>;
  test: boolean;
  router: string;

  constructor(private _router: Router,
              private empresaService: EmpresaService,
              private negociacaoService: NegociacaoService,
              private produtoService: ProdutoService,
              private planoService: PlanoService) {
    this.router = _router.url;
  }

  ngOnInit() {
    this.planoService.vezesEscolhidasParcelarTaxaMatricula = 1
    this.getVezesEscolhidasParcelarMatricula()
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  getPlanoSelecionado(): Plano {
    return this.planoService.planoSelecionado;
  }

  getCupom(): Cupom {
    return this.negociacaoService.cupom;
  }

  getValorTotal(): number {
    return this.negociacaoService.valorFinalContrato;
  }
  getMoeda(): string{
    return this.empresaService.unidadeSelecionada.moeda;
  }
  detalharParcelas(): boolean {
    return ((this.empresaService.config.detalharParcelaTelaCheckout != null &&
      this.empresaService.config.detalharParcelaTelaCheckout.valueOf() === 'true') ? true : false);
  }

  getProdutosPlano(): string[] {
    return this.getPlanoSelecionado().produtos.split('<br/>');
  }

  getValorTotalProdutosSelecionados(): number {
    return this.produtoService.getValorTotalProdutosSelecionados();
  }

  mostraParcelas() {
    const display = this.parcelasContainer.nativeElement.style.display;
    if (display !== 'none') {
      this.parcelasContainer.nativeElement.style.display = 'none';
    } else {
      this.parcelasContainer.nativeElement.style.display = 'block';
    }
  }

  getParcelas(): string[] {
    const valores: Array<string> = [];
    let numeroParcela = 1;
    let anoEMes;
    let descricaoParcela: Array<string> = [];
    this.getVezesEscolhidasParcelarMatricula();
    for (let i = 1; i < this.negociacaoService.parcelas.length; i++) {
      numeroParcela++;
      anoEMes = this.negociacaoService.parcelas[i].descricao.split("-");
      descricaoParcela = anoEMes[1];
      let valorDessaParcela = this.negociacaoService.parcelas[i].valor + (this.planoService.vezesEscolhidasParcelarTaxaMatricula > i ? this.getPlanoSelecionado().matricula / this.planoService.vezesEscolhidasParcelarTaxaMatricula : 0)
      valores.push((((this.getPlanoSelecionado().matricula > 0 && this.getVezesEscolhidasParcelarMatricula() > i)) ? "true  |" : "false |") + numeroParcela + "ª Parcela -" + descricaoParcela + ' ' + this.getMoeda() + ' ' + valorDessaParcela.toFixed(2));
    }
    return valores;
  }

  getValorPrimeiraParcelaDesconto(): number {
    let parcela1 = this.validarCobrarPrimeiraParcelaSempre() ? this.planoService.planoSelecionado.primeiraParcela : 0;
    const planoEscolhidoAluno = this.planoService.planoSelecionado.nome;
    let temDescontoParcela1PlanoEspecifico = false;

    // ==== INICIO DESCONTO PARCELA 1 ====
    // Existe dois for por ter dois cenários de Cupom, sendo o primeiro for para Desconto com Plano Especifico
    for (let i = 0; i < this.getListaPremios().length; i++) {
      const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
      const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
      if (descricaoPremio === 'PARCELA 1' && planoEscolhidoAluno.toUpperCase() === planoPercorridoArray.toUpperCase()) {
        const percentualDesconto = this.getListaPremios()[i].percentualDesconto;
        const valorDesconto = this.getListaPremios()[i].valorDesconto;
        if (percentualDesconto !== 0.0) {
          parcela1 = parcela1 - ((parcela1 * percentualDesconto) / 100);
          temDescontoParcela1PlanoEspecifico = true;
        } else if (valorDesconto !== 0.0) {
          parcela1 = parcela1 - valorDesconto;
          temDescontoParcela1PlanoEspecifico = true;
        } else {
          parcela1 = parcela1;
          temDescontoParcela1PlanoEspecifico = true;
        }
      }
    }
    // Existe dois for por ter dois cenários de Cupom, sendo o segundo for para Desconto sem Plano Especifico
    if (!temDescontoParcela1PlanoEspecifico) {
      for (let i = 0; i < this.getListaPremios().length; i++) {
        const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
        const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
        if (descricaoPremio === 'PARCELA 1' && planoPercorridoArray === '') {
          const percentualDesconto = this.getListaPremios()[i].percentualDesconto;
          const valorDesconto = this.getListaPremios()[i].valorDesconto;
          if (percentualDesconto !== 0.0) {
            parcela1 = parcela1 - ((parcela1 * percentualDesconto) / 100);
          } else if (valorDesconto !== 0.0) {
            parcela1 = parcela1 - valorDesconto;
          } else {
            parcela1 = parcela1;
          }
        }
      }
    }
    // ==== FIM DESCONTO PARCELA 1 ====

    parcela1 += this.valorAnuidadeParcela1();

    return parcela1;
  }

  valorAnuidadeParcela1(): number {
    let anuidade = this.getPlanoSelecionado().anuidade;
    const planoEscolhidoAluno = this.planoService.planoSelecionado.nome;

    if (anuidade > 0) {
      let valorParcelaAnuidadeParcela1 = 0;
      const numeroParcelasAnuidade = this.planoService.planoSelecionado.parcelasAnuidade.length;
      for (let i = 0; i < this.planoService.planoSelecionado.parcelasAnuidade.length; i++) {
        if (this.planoService.planoSelecionado.parcelasAnuidade[i].parcela === 1) {
          valorParcelaAnuidadeParcela1 = this.planoService.planoSelecionado.parcelasAnuidade[i].valor;
        }
      }

      // If porque tem Anuidade Parcela e não parcelada
      // Existe dois for por ter dois cenários de Cupom, sendo o primeiro for para Desconto com Plano Especifico
      let temAnuidadeParcela1PlanoEspecifico = false;
      for (let i = 0; i < this.getListaPremios().length; i++) {
        const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
        const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
        if (descricaoPremio === 'ANUIDADE PLANO RECORRENTE' && planoEscolhidoAluno.toUpperCase() === planoPercorridoArray.toUpperCase()) {
          const percentualDesconto = this.getListaPremios()[i].percentualDesconto;
          const valorDesconto = this.getListaPremios()[i].valorDesconto;
          if (percentualDesconto !== 0.0) {
            if (valorParcelaAnuidadeParcela1 > 0) {
              anuidade = valorParcelaAnuidadeParcela1 - ((valorParcelaAnuidadeParcela1 * percentualDesconto) / 100);
            } else {
              anuidade = anuidade - ((anuidade * percentualDesconto) / 100);
            }
            temAnuidadeParcela1PlanoEspecifico = true;
          } else if (valorDesconto !== 0.0) {
            if (valorParcelaAnuidadeParcela1 > 0) {
              anuidade = valorParcelaAnuidadeParcela1 - (valorDesconto / numeroParcelasAnuidade);
            } else {
              anuidade = anuidade - (valorDesconto / numeroParcelasAnuidade);
            }
            temAnuidadeParcela1PlanoEspecifico = true;
          }
        }
      }
      // Existe dois for por ter dois cenários de Cupom, sendo o segundo for para Desconto sem Plano Especifico
      if (!temAnuidadeParcela1PlanoEspecifico) {
        for (let i = 0; i < this.getListaPremios().length; i++) {
          const planoPercorridoArray = this.getListaPremios()[i].descricaoPlano;
          const descricaoPremio = this.getListaPremios()[i].descricaoPremio;
          if (descricaoPremio === 'ANUIDADE PLANO RECORRENTE' && planoPercorridoArray === '') {
            const percentualDesconto = this.getListaPremios()[i].percentualDesconto;
            const valorDesconto = this.getListaPremios()[i].valorDesconto;
            if (percentualDesconto !== 0.0) {
              if (valorParcelaAnuidadeParcela1 > 0) {
                anuidade = valorParcelaAnuidadeParcela1 - ((valorParcelaAnuidadeParcela1 * percentualDesconto) / 100);
              } else {
                anuidade = anuidade - ((anuidade * percentualDesconto) / 100);
              }
              temAnuidadeParcela1PlanoEspecifico = true;
            } else if (valorDesconto !== 0.0) {
              if (valorParcelaAnuidadeParcela1 > 0) {
                anuidade = valorParcelaAnuidadeParcela1 - (valorDesconto / numeroParcelasAnuidade);
              } else {
                anuidade = anuidade - (valorDesconto / numeroParcelasAnuidade);
              }
              temAnuidadeParcela1PlanoEspecifico = true;
            }
          }
        }
      }
    }
    return anuidade > 0 ? anuidade : 0;
  }

  getValorTotalPlanoComCupom(): number {
    let total = 0.0;
    for (let i = 0; i < this.negociacaoService.parcelas.length; i++) {
      // não contabilizar primeira parcela na lista
      if (i !== 0) {
        total += this.negociacaoService.parcelas[i].valor;
      }
    }
    return total + this.getValorPrimeiraParcelaDesconto();
  }

  getListaPremios(): PremioCupom[] {
    const premioCupom = [];
    return this.negociacaoService.cupom === undefined ? premioCupom : this.negociacaoService.cupom.listaPremios;
  }

  validarCobrarPrimeiraParcelaSempre(): boolean {
    const dia = new Date();
    const config = (this.getPlanoSelecionado().cobrarPrimeiraParcelaCompra != null &&
      this.getPlanoSelecionado().cobrarPrimeiraParcelaCompra) ? true : false;
    let diaVencimento = false;

    if (this.formGroup.get('diavencimento') != null && this.formGroup.get('diavencimento').value != null) {
      if (dia.getDate() === this.formGroup.get('diavencimento').value) {
        diaVencimento = true;
      }
    }
    return (config || diaVencimento);
  }
  getVezesEscolhidasParcelarMatricula(): number {
    if (this.vezesEscolhidasParcelarMatricula > 0 && this.vezesEscolhidasParcelarMatricula <= this.getPlanoSelecionado().nrVezesParcelarMatricula) {
      this.planoService.vezesEscolhidasParcelarTaxaMatricula = this.vezesEscolhidasParcelarMatricula
      return this.vezesEscolhidasParcelarMatricula
    } else {

      return 1
    }
  }

  counter(i: number) {
    return new Array(i);
  }
}
