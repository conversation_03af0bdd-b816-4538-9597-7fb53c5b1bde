<pacto-plano-credito [formGroup]="formGroup"
                     *ngIf="getPlanoSelecionado() && getConfig()
                     && getPlanoSelecionado().qtdCreditoPlanoCredito
                     && !getPlanoSelecionado().parcelamentoOperadora
                     && !getPlanoSelecionado().inicioFuturo">
</pacto-plano-credito>

<pacto-plano-recorrente [formGroup]="formGroup"
                        *ngIf="getPlanoSelecionado() && getConfig()
                        && getPlanoSelecionado().regimeRecorrencia
                        && !getPlanoSelecionado().qtdCreditoPlanoCredito
                        && !getPlanoSelecionado().parcelamentoOperadora
                        && !getPlanoSelecionado().inicioFuturo">
</pacto-plano-recorrente>

<pacto-plano-recorrente-parc-operadora [formGroup]="formGroup"
                                       *ngIf="getPlanoSelecionado() && getConfig()
                                        && getPlanoSelecionado().regimeRecorrencia
                                        && !getPlanoSelecionado().qtdCreditoPlanoCredito
                                        && getPlanoSelecionado().parcelamentoOperadora
                                        && !getPlanoSelecionado().inicioFuturo">
</pacto-plano-recorrente-parc-operadora>

<pacto-plano-credito-parc-operadora [formGroup]="formGroup"
                                    *ngIf="getPlanoSelecionado() && getConfig()
                                    && getPlanoSelecionado().qtdCreditoPlanoCredito
                                    && getPlanoSelecionado().parcelamentoOperadora
                                    && !getPlanoSelecionado().inicioFuturo">
</pacto-plano-credito-parc-operadora>

<pacto-plano-inicio-futuro [formGroup]="formGroup"
                           *ngIf="getPlanoSelecionado() && getConfig()
                           && getPlanoSelecionado().inicioFuturo">
</pacto-plano-inicio-futuro>

<pacto-plano-convencional [formGroup]="formGroup"
                        *ngIf="getPlanoSelecionado() && getConfig()
                        && !getPlanoSelecionado().regimeRecorrencia
                        && !getPlanoSelecionado().qtdCreditoPlanoCredito
                        && !getPlanoSelecionado().parcelamentoOperadora
                        && !getPlanoSelecionado().inicioFuturo">
</pacto-plano-convencional>

<pacto-plano-convencional-parc-operadora [formGroup]="formGroup"
                          *ngIf="getPlanoSelecionado() && getConfig()
                        && !getPlanoSelecionado().regimeRecorrencia
                        && !getPlanoSelecionado().qtdCreditoPlanoCredito
                        && getPlanoSelecionado().parcelamentoOperadora
                        && !getPlanoSelecionado().inicioFuturo">
</pacto-plano-convencional-parc-operadora>
