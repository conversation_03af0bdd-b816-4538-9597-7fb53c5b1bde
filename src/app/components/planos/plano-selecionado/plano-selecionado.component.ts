import {Component, Input, OnInit} from '@angular/core';
import {Router} from '@angular/router';
import {PlanoService} from '@base-core/plano/plano.service';
import {Plano} from '@base-core/plano/plano.model';
import {Config} from '@base-core/empresa/config.model';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {FormGroup, Validators} from '@angular/forms';

@Component({
  selector: 'pacto-plano-selecionado',
  templateUrl: './plano-selecionado.component.html',
  styleUrls: ['./plano-selecionado.component.scss']
})
export class PlanoSelecionadoComponent implements OnInit {
  @Input() formGroup: FormGroup;
  router: string;
  constructor(private _router: Router,
              private empresaService: EmpresaService,
              private planoService: PlanoService) {
    this.router = _router.url;
  }

  ngOnInit() {
  }
  getConfig(): Config {
    return this.empresaService.config;
  }
  getPlanoSelecionado(): Plano {
    return this.planoService.planoSelecionado;
  }

  MostraParcelas(el) {
    const display = document.getElementById(el).style.display;
    if (display !== 'none') {
      document.getElementById(el).style.display = 'none';
    } else {
      document.getElementById(el).style.display = 'block';
    }
  }
}
