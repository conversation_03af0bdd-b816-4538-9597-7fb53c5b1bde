<div class="caixa-plano-selecionado">
  <span class="titulo">{{'planos.plano-convencional.plano-selecionado'|translate}}: </span>
  <span class="nome-planoResumo" [style.color]="getConfig().cor">{{getPlanoSelecionado().nome}}</span>

  <pacto-video-observacao [videoYouTubeCode]="getPlanoSelecionado().videoSiteUrl"
                          [observacao]="getPlanoSelecionado().observacaoSite">
  </pacto-video-observacao>

  <div class="container" [style.border-left-color]="getConfig().cor">

    <div class="arrow-right" [style.border-left-color]="getConfig().cor"></div>

    <!--      CONFIGURAÇÃO DETALHAR PARCELAS DESMARCADA-->
    <div *ngIf="!detalharParcelas()" class="span_5_of_5 ta-center pedido ta-center">
      <span class="titulo">{{'planos.plano-convencional.o-valor-da-sua-primeira-parcela-sera'|translate}}:</span>
      <span class="primeiraparcela" *ngIf="!getCupom()" [style.color]="getConfig().cor">{{(getValorTotalProdutosSelecionados() + getPlanoSelecionado().primeiraParcela + getPlanoSelecionado().adesao + getPlanoSelecionado().valorProdutos + (getPlanoSelecionado().mesAnuidade && getPlanoSelecionado().anuidadeAgora ? getPlanoSelecionado().anuidade : 0))| currency:this.getMoeda():'symbol'}}</span>
      <span class="primeiraparcela" *ngIf="getCupom()" [style.color]="getConfig().cor">{{(getValorPrimeiraParcelaDesconto()) | currency:this.getMoeda():'symbol'}}</span>

    </div>
    <!--      FIM CONFIGURAÇÃO DESMARCADA-->

    <!--      CONFIGURAÇÃO DETALHAR PARCELAS MARCADA-->
    <div *ngIf="detalharParcelas()" class="span_5_of_5 ta-center pedido ta-center">
      <span class="titulo" *ngIf="getPlanoSelecionado().primeiraParcela > 0.0">{{'planos.plano-convencional.mensalidade-1'|translate}}:</span>
      <span class="primeiraparcela" *ngIf="!getCupom()" [style.color]="getConfig().cor">{{(getPlanoSelecionado().primeiraParcela)| currency:this.getMoeda():'symbol'}}</span>
      <span class="primeiraparcela" *ngIf="getCupom()" [style.color]="getConfig().cor">{{(getValorParcela1())| currency:this.getMoeda():'symbol'}}</span>
      <span class="titulo" *ngIf="getPlanoSelecionado().matricula > 0.0">{{'planos.plano-convencional.matricula'|translate}}:</span>
      <span class="primeiraparcela" [style.color]="getConfig().cor"
            *ngIf="getPlanoSelecionado().matricula > 0.0">{{(getPlanoSelecionado().matricula)| currency:this.getMoeda():'symbol'}}
         </span>

  <div>
    <label class="titulo" for="dividirMatricula"
      *ngIf="getPlanoSelecionado().nrVezesParcelarMatricula > 1 && getPlanoSelecionado().matricula">
      {{'divisao-taxa-matricula.dividir-matricula-em-quantas-vezes' | translate}} </label>
    <select name="dividirMatricula" class="custom-select-taxa-matricula" [(ngModel)]="vezesEscolhidasParcelarMatricula"
      (change)='this.getVezesEscolhidasParcelarMatricula()'
      *ngIf="getPlanoSelecionado().nrVezesParcelarMatricula > 1 && getPlanoSelecionado().matricula">
      <option
        *ngFor="let loop of counter(this.getPlanoSelecionado().nrVezesParcelarMatricula) ;let i= index, let f = first"
        [attr.selected]="first" [ngValue]="i+1">
        <p *ngIf="f"> {{'divisao-taxa-matricula.nao-dividir-taxa' | translate}}</p>
        <p *ngIf="!f"> {{'divisao-taxa-matricula.dividir-em' | translate}} {{i+1}}
          {{'divisao-taxa-matricula.parcelas-de' | translate}} {{(getPlanoSelecionado().matricula / [i+1]) |
          currency:this.getMoeda():'symbol' }}</p>
      </option>
    </select>
  </div>


      <span *ngIf="(getPlanoSelecionado().valorProdutos > 0.0 || getValorTotalProdutosSelecionados() > 0.0)"
            class="titulo">{{'planos.plano-convencional.produtos'|translate}}:</span>
      <span *ngIf="(getPlanoSelecionado().valorProdutos > 0.0 || getValorTotalProdutosSelecionados() > 0.0)"
            class="primeiraparcela" [style.color]="getConfig().cor">{{(getValorTotalProdutosSelecionados() + getPlanoSelecionado().valorProdutos)| currency:this.getMoeda():'symbol'}}</span>
      <span *ngIf="(getValorProRata() > 0.0)"
            class="titulo">{{'planos.plano-convencional.pro-rata'|translate}}:</span>
      <span *ngIf="(getValorProRata() > 0.0)"
            class="primeiraparcela" [style.color]="getConfig().cor">{{getValorProRata()| currency:this.getMoeda():'symbol'}}</span>
      <span class="titulo">{{'planos.plano-convencional.o-valor-da-sua-primeira-parcela-sera'|translate}}:</span>
      <span class="primeiraparcela" *ngIf="!getCupom()" [style.color]="getConfig().cor">{{(getValorTotalProdutosSelecionados() + getPlanoSelecionado().primeiraParcela + (getPlanoSelecionado().matricula / getVezesEscolhidasParcelarMatricula()) + getPlanoSelecionado().valorProdutos + (getPlanoSelecionado().mesAnuidade && getPlanoSelecionado().anuidadeAgora ? getPlanoSelecionado().anuidade : 0))| currency:this.getMoeda():'symbol'}}</span>
      <span class="tprimeiraparcela" *ngIf="getCupom()"
            [style.color]="getConfig().cor">{{(getValorPrimeiraParcelaDesconto()| currency:this.getMoeda():'symbol')}}</span>
    </div>
  </div>
  <!--      FIM CONFIGURAÇÃO MARCADA-->

  <div class="linha-selec-total"></div>

  <a [style.color]="getConfig().cor" (click)="mostraParcelas()" type="button">{{'planos.plano-convencional.detalhar-parcelas'|translate}}</a>
  <div id="parcelasdiv" #parcelasdiv>
        <span *ngFor="let p of getParcelas()" class="detalhe">
          <span [style.color]="getConfig().cor">{{p.split('|')[1]}}</span>
              <p *ngIf="p.split('|')[0] != 'false '"
              class="detalhamento-parcela">
              {{'divisao-taxa-matricula.taxa-matricula' | translate}}:
              {{(getPlanoSelecionado().matricula / this.planoService.vezesEscolhidasParcelarTaxaMatricula)|currency:this.getMoeda():'symbol'}}
             </p>
         </span>
  </div>

  <div class="linha-selec-total"></div>

  <div *ngIf="(this.formGroup.get('diavencimento') != null && this.formGroup.get('diavencimento').value)" class="span_5_of_5 ta-center pedido ta-center">
    <span class="titulo">{{'planos.plano-convencional.dia-de-vencimento'|translate}}:</span>
    <span class="primeiraparcela" [style.color]="getConfig().cor">{{this.formGroup.get('diavencimento').value}}</span>
  </div>

  <div class="span_2_of_2 detalhesplano">

    <div *ngIf="getPlanoSelecionado().descricaoEncantamento">{{getPlanoSelecionado().descricaoEncantamento}}</div>

    <span class="titulo">{{'planos.plano-convencional.esse-plano-inclui'|translate}}:</span>
    <span *ngFor="let m of getPlanoSelecionado().modalidades" class="modalidade">
       <i [style.color]="getConfig().cor" class="tem pct-check-circle"></i> <span>{{m}}</span>
    </span>

    <div *ngIf="getPlanoSelecionado().produtos">
      <span class="titulo">{{'planos.plano-convencional.produtos-no-plano'|translate}}:</span>
      <span *ngFor="let p of getProdutosPlano()" class="modalidade">
       <i [style.color]="getConfig().cor" class="tem pct-check-circle"></i> <span>{{p}}</span>
    </span>
    </div>
  </div>

  <div class="leiacontrato">
    <pacto-visualizar-documento [tipo]="'contrato'" [inside]="true"
                                [plano]="getPlanoSelecionado().codigo"
                                [textoLink]="'global.leiacontrato'| translate"></pacto-visualizar-documento>
  </div>
</div>
