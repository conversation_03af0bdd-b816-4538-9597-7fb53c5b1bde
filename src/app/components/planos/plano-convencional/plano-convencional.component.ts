import {Component, Input, OnInit, ElementRef, ViewChild} from '@angular/core';
import {Router} from '@angular/router';
import {PlanoService} from '@base-core/plano/plano.service';
import {Plano} from '@base-core/plano/plano.model';
import {Config} from '@base-core/empresa/config.model';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {FormGroup, Validators} from '@angular/forms';
import {ProdutoService} from '@base-core/produto/produto.service';
import {Cupom} from "@base-core/cupom-desconto/cupom.model";
import {PremioCupom} from "@base-core/cupom-desconto/premio-cupom.model";

@Component({
  selector: 'pacto-plano-convencional',
  templateUrl: './plano-convencional.component.html',
  styleUrls: ['./plano-convencional.component.scss']
})
export class PlanoConvencionalComponent implements OnInit {
  vezesEscolhidasParcelarMatricula: number = 1


  @Input() formGroup: FormGroup;
  @ViewChild('parcelasdiv') parcelasContainer: ElementRef<HTMLDivElement>;
  router: string;
  constructor(private _router: Router,
              private empresaService: EmpresaService,
              private negociacaoService: NegociacaoService,
              private produtoService: ProdutoService,
              private planoService: PlanoService) {
    this.router = _router.url;
  }

  ngOnInit() {
    this.planoService.vezesEscolhidasParcelarTaxaMatricula  = 1
    this.getVezesEscolhidasParcelarMatricula()

  }
  getConfig(): Config {
    return this.empresaService.config;
  }
  getPlanoSelecionado(): Plano {
    return this.planoService.planoSelecionado;
  }
  detalharParcelas(): boolean {
    return ((this.empresaService.config.detalharParcelaTelaCheckout != null && this.empresaService.config.detalharParcelaTelaCheckout.valueOf() === 'true') ? true : false);
  }
  getProdutosPlano(): string[] {
    return this.getPlanoSelecionado().produtos.split('<br/>');
  }
  getValorTotalProdutosSelecionados(): number {
    return this.produtoService.getValorTotalProdutosSelecionados();
  }

  getValorProRata(): number {
    return this.negociacaoService.valorProRata;
  }

  getCupom(): Cupom {
    return this.negociacaoService.cupom;
  }

  getListaPremios(): PremioCupom[] {
    return this.negociacaoService.cupom.listaPremios;
  }

  getValorPrimeiraParcela(): number {
    return (this.getValorTotalProdutosSelecionados() + this.getPlanoSelecionado().primeiraParcela +
      this.getPlanoSelecionado().matricula + this.getPlanoSelecionado().valorProdutos +
      (this.getPlanoSelecionado().mesAnuidade && this.getPlanoSelecionado().anuidadeAgora ? this.getPlanoSelecionado().anuidade : 0));
  }

  getValorParcela1(): number {
    var parcela1 = this.getValorPrimeiraParcela();
    for (let i = 0; i < this.getListaPremios().length; i++) {
      if (this.getListaPremios()[i].descricaoPremio === 'PARCELA 1') {
        if (this.getListaPremios()[i].percentualDesconto != 0.0) {
          return parcela1 - ((parcela1 * this.getListaPremios()[i].percentualDesconto) / 100);
        } else if (this.getListaPremios()[i].valorDesconto != 0.0) {
          return parcela1 - this.getListaPremios()[i].valorDesconto;
        } else {
          return parcela1;
        }
      }
    }
    return parcela1;
  }

  getValorPrimeiraParcelaDesconto(): number {
    return this.getValorParcela1() +  + this.getValorTotalProdutosSelecionados();
  }

  mostraParcelas() {
    console.log('Element Display 5: '+this.parcelasContainer.nativeElement);
    const display = this.parcelasContainer.nativeElement.style.display;
    console.log('Display show 6: '+display);
    if (display !== 'none') {
      this.parcelasContainer.nativeElement.style.display = 'none';
    } else {
      this.parcelasContainer.nativeElement.style.display = 'block';
    }
  }
  getMoeda():string{
    return  this.empresaService.unidadeSelecionada.moeda;
  }
  getParcelas(): string[] {
    const valores: Array<string> = [];
    let numeroParcela = 1;
    let anoEMes;
    let descricaoParcela: Array<string> = [];
    this.getVezesEscolhidasParcelarMatricula();

    const limitarQtdParcelasExibir = this.planoService != null && this.planoService.planoSelecionado && this.planoService.planoSelecionado.maxDivisao > 0 && this.planoService.planoSelecionado.maxDivisao < this.negociacaoService.parcelas.length;
    const tamanhoListaPercorrer = limitarQtdParcelasExibir ? this.planoService.planoSelecionado.maxDivisao : this.negociacaoService.parcelas.length;

    for (let i = 1; i < tamanhoListaPercorrer; i++) {
      numeroParcela++;
      anoEMes = this.negociacaoService.parcelas[i].descricao.split("-");
      descricaoParcela = anoEMes[1];
      let valorDessaParcela = this.negociacaoService.parcelas[i].valor + (this.planoService.vezesEscolhidasParcelarTaxaMatricula > i ? this.getPlanoSelecionado().matricula / this.planoService.vezesEscolhidasParcelarTaxaMatricula : 0)
      valores.push((((this.getPlanoSelecionado().matricula > 0 && this.getVezesEscolhidasParcelarMatricula() > i)) ? "true  |" : "false |" )  + numeroParcela + "ª Parcela -" + descricaoParcela + ' ' + this.getMoeda() + ' ' + valorDessaParcela.toFixed(2) );
    }

    return valores;
  }
  getVezesEscolhidasParcelarMatricula():number{
    if (this.vezesEscolhidasParcelarMatricula > 0 && this.vezesEscolhidasParcelarMatricula <= this.getPlanoSelecionado().nrVezesParcelarMatricula){
      this.planoService.vezesEscolhidasParcelarTaxaMatricula = this.vezesEscolhidasParcelarMatricula
      return this.vezesEscolhidasParcelarMatricula
    }else{

      return 1
    }
  }

  counter(i: number) {
    return new Array(i);
  }
}
