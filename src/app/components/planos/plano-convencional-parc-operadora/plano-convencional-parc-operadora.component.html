<div class="caixa-plano-selecionado">
  <span class="titulo">{{ 'planos.plano-convencional-parc-operadora.plano-selecionado' | translate }}: </span>
  <span class="nome-planoResumo" [style.color]="getConfig().cor">{{ getPlanoSelecionado().nome }}</span>

  <div class="container" [style.border-left-color]="getConfig().cor">
    <div class="arrow-right" [style.border-left-color]="getConfig().cor"></div>

    <!-- CONFIGURAÇÃO DETALHAR PARCELAS MARCADA -->
    <div *ngIf="detalharParcelas()" class="span_5_of_5 ta-center pedido ta-center">
      <span class="titulo" *ngIf="getPlanoSelecionado().matricula > 0.0">
        {{ 'planos.plano-convencional-parc-operadora.matricula' | translate }}:
      </span>
      <span class="primeiraparcela po1" [style.color]="getConfig().cor" *ngIf="getPlanoSelecionado().matricula > 0.0">
        {{ getPlanoSelecionado().matricula | currency : this.getMoeda() : 'symbol' }}
      </span>

      <div>
        <label
          class="titulo"
          for="dividirMatricula"
          *ngIf="getPlanoSelecionado().nrVezesParcelarMatricula > 1 && getPlanoSelecionado().matricula"
        >
          {{ 'divisao-taxa-matricula.dividir-matricula-em-quantas-vezes' | translate }}
        </label>
        <select
          name="dividirMatricula"
          class="custom-select-taxa-matricula"
          [(ngModel)]="vezesEscolhidasParcelarMatricula"
          (change)="this.getVezesEscolhidasParcelarMatricula()"
          *ngIf="getPlanoSelecionado().nrVezesParcelarMatricula > 1 && getPlanoSelecionado().matricula"
        >
          <option
            *ngFor="
              let loop of counter(this.getPlanoSelecionado().nrVezesParcelarMatricula);
              let i = index;
              let f = first
            "
            [attr.selected]="f"
            [ngValue]="i + 1"
          >
            <p *ngIf="f">{{ 'divisao-taxa-matricula.nao-dividir-taxa' | translate }}</p>
            <p *ngIf="!f">
              {{ 'divisao-taxa-matricula.dividir-em' | translate }} {{ i + 1 }}
              {{ 'divisao-taxa-matricula.parcelas-de' | translate }}
              {{ getPlanoSelecionado().matricula / [i + 1] | currency : this.getMoeda() : 'symbol' }}
            </p>
          </option>
        </select>
      </div>

      <span
        class="titulo"
        *ngIf="getValorTotalProdutosSelecionados() > 0.0 || getPlanoSelecionado().valorProdutos > 0.0"
      >
        {{ 'planos.plano-convencional-parc-operadora.produtos' | translate }}:
      </span>
      <span
        class="primeiraparcela po2"
        [style.color]="getConfig().cor"
        *ngIf="getValorTotalProdutosSelecionados() > 0.0 || getPlanoSelecionado().valorProdutos > 0.0"
      >
        {{
          getValorTotalProdutosSelecionados() + getPlanoSelecionado().valorProdutos
            | currency : this.getMoeda() : 'symbol'
        }}
      </span>
    </div>
  </div>
  <!-- FIM CONFIGURAÇÃO MARCADA -->

  <div class="linha-selec-total"></div>

  <span class="titulo">{{ 'planos.plano-convencional-parc-operadora.valor-a-cobrar' | translate }}:</span>
  <span class="primeiraparcela po3" [style.color]="getConfig().cor">
    <span>
      {{ obterValorTotal() | currency : this.getMoeda() : 'symbol' }}
    </span>
  </span>
  <span class="primeiraparcela po3" [style.color]="getConfig().cor">
    <span>
      {{ 'em ' + getParcOperadoraSelecionado() + 'x de ' }}
      {{ obterValorTotal() / getParcOperadoraSelecionado() | currency : this.getMoeda() : 'symbol' }}
    </span>
  </span>

  <div
    *ngIf="this.formGroup.get('diavencimento') != null && this.formGroup.get('diavencimento').value"
    class="span_5_of_5 ta-center pedido ta-center"
  >
    <span class="titulo">{{ 'planos.plano-convencional-parc-operadora.dia-de-venciment' | translate }}o:</span>
    <span class="primeiraparcela po4" [style.color]="getConfig().cor">
      {{ this.formGroup.get('diavencimento').value }}
    </span>
  </div>

  <!--      CUPOM DESCONTO-->
  <span *ngIf="getCupom()" class="titulo">
    {{ 'planos.plano-convencional-parc-operadora.cupom-desconto' | translate }}
    <a class="remover-cupom" [style.color]="getConfig().cor" (click)="limparCupom()">
      [{{ 'planos.plano-convencional-parc-operadora.remover' | translate }}]
    </a>
  </span>
  <div *ngIf="getCupom()" class="sub-cupom">
    <div class="nome-cupom">{{ getCupom().numeroCupom.toUpperCase() }}</div>
    <span *ngFor="let premios of getListaPremiosPlanoParcelado()">
      <div>
        <span class="modalidade">
          {{
            premios.descricaoPremio.includes('PARCELA 1')
              ? premios.descricaoPremio.replace('PARCELA 1', 'DESCONTO APLICADO')
              : premios.descricaoPremio
          }}:
        </span>
        <span *ngIf="premios.percentualDesconto != 0.0" class="valor-desc">{{ premios.percentualDesconto }}%</span>
        <span *ngIf="premios.valorDesconto != 0.0" class="valor-desc">
          {{ premios.valorDesconto | currency : this.getMoeda() : 'symbol' }}
        </span>
      </div>
    </span>
  </div>

  <div id="divDetalhesPlanoConvencionalParcOperadora" class="span_2_of_2 detalhesplano">
    <div *ngIf="getPlanoSelecionado().descricaoEncantamento">{{ getPlanoSelecionado().descricaoEncantamento }}</div>

    <span class="titulo">{{ 'planos.plano-convencional-parc-operadora.esse-plano-inclui' | translate }}:</span>
    <span *ngFor="let m of getPlanoSelecionado().modalidades" class="modalidade">
      <i [style.color]="getConfig().cor" class="tem pct-check-circle"></i> <span>{{ m }}</span>
    </span>

    <div *ngIf="getPlanoSelecionado().produtos">
      <span class="titulo">{{ 'planos.plano-convencional-parc-operadora.produtos-no-plano' | translate }}:</span>
      <span *ngFor="let p of getProdutosPlano()" class="modalidade">
        <i [style.color]="getConfig().cor" class="tem pct-check-circle"></i> <span>{{ p }}</span>
      </span>
    </div>
  </div>

  <div class="leiacontrato">
    <pacto-visualizar-documento
      [tipo]="'contrato'"
      [inside]="true"
      [plano]="getPlanoSelecionado().codigo"
      [textoLink]="'global.leiacontrato' | translate"
    ></pacto-visualizar-documento>
  </div>

</div>
