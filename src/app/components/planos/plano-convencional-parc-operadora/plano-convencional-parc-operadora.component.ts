import { Component, Input, OnInit, ElementRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { PlanoService } from '@base-core/plano/plano.service';
import { Plano } from '@base-core/plano/plano.model';
import { Config } from '@base-core/empresa/config.model';
import { EmpresaService } from '@base-core/empresa/empresa.service';
import { NegociacaoService } from '@base-core/negociacao/negociacao.service';
import { FormGroup, Validators } from '@angular/forms';
import { ProdutoService } from '@base-core/produto/produto.service';
import { Cupom } from '@base-core/cupom-desconto/cupom.model';
import { PremioCupom } from '@base-core/cupom-desconto/premio-cupom.model';

@Component({
  selector: 'pacto-plano-convencional-parc-operadora',
  templateUrl: './plano-convencional-parc-operadora.component.html',
  styleUrls: ['./plano-convencional-parc-operadora.component.scss'],
})
export class PlanoConvencionalParcOperadoraComponent implements OnInit {
  vezesEscolhidasParcelarMatricula: number = 1;

  @Input() formGroup: FormGroup;
  @ViewChild('parcelasdiv') parcelasContainer: ElementRef<HTMLDivElement>;
  router: string;

  constructor(
    private _router: Router,
    private empresaService: EmpresaService,
    private negociacaoService: NegociacaoService,
    private produtoService: ProdutoService,
    private planoService: PlanoService,
  ) {
    this.router = _router.url;
  }

  ngOnInit() {
    this.formGroup.get('nrcartao').valueChanges.subscribe((res) => {
      this.produtoService
        .obterValorDaTaxa(
          this.negociacaoService.chave,
          this.negociacaoService.codunidade,
          Number(this.formGroup.get('nrcartao').value.replace(/\s/g, '')),
          Number(this.formGroup.get('parcelasCartao').value),
        )
        .subscribe((res) => {
          this.produtoService.valorDaTaxa = Number(res);
        });
    });

    this.formGroup.get('parcelasCartao').valueChanges.subscribe((res) => {
      this.produtoService
        .obterValorDaTaxa(
          this.negociacaoService.chave,
          this.negociacaoService.codunidade,
          Number(this.formGroup.get('nrcartao').value.replace(/\s/g, '')),
          Number(this.formGroup.get('parcelasCartao').value),
        )
        .subscribe((res) => {
          this.produtoService.valorDaTaxa = Number(res);
        });
    });
  }

  getConfig(): Config {
    return this.empresaService.config;
  }
  getMoeda(): string {
    return this.empresaService.unidadeSelecionada.moeda;
  }
  getPlanoSelecionado(): Plano {
    return this.planoService.planoSelecionado;
  }

  getParcOperadoraSelecionado(): number {
    return this.formGroup.get('parcelasCartao').value;
  }

  getValorAnuidadeParcelaUm(): number {
    for (let i = 0; i < this.getPlanoSelecionado().parcelasAnuidade.length; i++) {
      if (
        this.getPlanoSelecionado().parcelasAnuidade[i].parcelaApresentar.toLowerCase() === 'no mesmo dia da parcela 1'
      ) {
        return this.getPlanoSelecionado().parcelasAnuidade[i].valor;
      }
    }
    return 0;
  }

  getParcelarAnuidade(): boolean {
    return (
      this.planoService.planoSelecionado != null &&
      this.planoService.planoSelecionado.parcelasAnuidade != null &&
      this.planoService.planoSelecionado.parcelasAnuidade.length > 0
    );
  }

  detalharParcelas(): boolean {
    return this.empresaService.config.detalharParcelaTelaCheckout != null &&
      this.empresaService.config.detalharParcelaTelaCheckout.valueOf() === 'true'
      ? true
      : false;
  }

  getProdutosPlano(): string[] {
    return this.getPlanoSelecionado().produtos.split('<br/>');
  }

  getValorTotal(): number {
    return this.negociacaoService.valorFinalContrato;
  }

  getValorTotalProdutosSelecionados(): number {
    return this.produtoService.getValorTotalProdutosSelecionados();
  }

  getValorAnuidadeDesconto(): number {
    var anuidade = this.getPlanoSelecionado().anuidade;
    for (let i = 0; i < this.getListaPremios().length; i++) {
      if (this.getListaPremios()[i].descricaoPremio === 'ANUIDADE PLANO RECORRENTE') {
        if (this.getListaPremios()[i].percentualDesconto != 0.0) {
          return (anuidade / 100) * this.getListaPremios()[i].percentualDesconto;
        } else if (this.getListaPremios()[i].valorDesconto != 0.0) {
          return anuidade - this.getListaPremios()[i].valorDesconto;
        } else {
          return anuidade;
        }
      }
    }
    return anuidade;
  }

  obterValorTotal() {
    const valorSemTaxa =
      this.getValorTotalProdutosSelecionados() +
      (this.getValorTotal() - (this.getCupom() ? this.getValorTotalConcedidoDesconto() : 0)) +
      this.getPlanoSelecionado().valorProdutos +
      this.getPlanoSelecionado().adesao +
      (this.getPlanoSelecionado().mesAnuidade && this.getPlanoSelecionado().anuidadeAgora
        ? this.getPlanoSelecionado().anuidade
        : 0);

    return this.produtoService.valorDaTaxa ? valorSemTaxa + valorSemTaxa * (this.produtoService.valorDaTaxa / 100) : valorSemTaxa;
  }

  cobrarProdutoJuntoAdesaoMatricula(): boolean {
    var dia = new Date();
    var configProdJunto =
      this.empresaService.config.cobrarProdutoJuntoAdesaoMatricula != null &&
      this.empresaService.config.cobrarProdutoJuntoAdesaoMatricula.valueOf() === 'true'
        ? true
        : false;
    var diaVencimento = true;

    if (this.formGroup.get('diavencimento') != null && this.formGroup.get('diavencimento').value != null) {
      if (dia.getDate() != this.formGroup.get('diavencimento').value && !configProdJunto) {
        diaVencimento = false;
      } else {
        configProdJunto = true;
        diaVencimento = true;
      }
    } else {
      configProdJunto = true;
      diaVencimento = true;
    }
    return configProdJunto && diaVencimento;
  }

  getCupom(): Cupom {
    return this.negociacaoService.cupom;
  }

  limparCupom() {
    this.negociacaoService.cupom = null;
  }

  getListaPremios(): PremioCupom[] {
    return this.negociacaoService.cupom.listaPremios;
  }

  getListaPremiosPlanoParcelado(): PremioCupom[] {
    if (this.negociacaoService.cupom && this.negociacaoService.cupom.listaPremios) {
      for (let i = 0; i < this.getListaPremios().length; i++) {
        if (this.getListaPremios()[i].descricaoPremio === 'PARCELA 1') {
          return [this.getListaPremios()[i]];
        }
      }
    }
    return [];
  }

  getValorAdesaoDesconto(): number {
    var matricula = this.getPlanoSelecionado().matricula;
    for (let i = 0; i < this.getListaPremios().length; i++) {
      if (this.getListaPremios()[i].descricaoPremio === 'MATRICULA') {
        if (this.getListaPremios()[i].percentualDesconto != 0.0) {
          return matricula - (matricula * this.getListaPremios()[i].percentualDesconto) / 100;
        } else if (this.getListaPremios()[i].valorDesconto != 0.0) {
          return matricula - this.getListaPremios()[i].valorDesconto;
        } else {
          return matricula;
        }
      }
    }
    return matricula;
  }

  validarCobrarPrimeiraParcelaSempre(): boolean {
    var dia = new Date();
    var config =
      this.empresaService.config.cobrarPrimeiraParcelaCompra != null &&
      this.empresaService.config.cobrarPrimeiraParcelaCompra.valueOf() === 'true'
        ? true
        : false;
    var diaVencimento = false;

    if (this.formGroup.get('diavencimento') != null && this.formGroup.get('diavencimento').value != null) {
      if (dia.getDate() == this.formGroup.get('diavencimento').value) {
        diaVencimento = true;
      }
    }
    return config || diaVencimento;
  }

  getValorPrimeiraParcela(): number {
    return (
      (this.validarCobrarPrimeiraParcelaSempre() ? this.getPlanoSelecionado().primeiraParcela : 0) +
      (this.getCupom() ? this.getValorAdesaoDesconto() : this.getPlanoSelecionado().matricula) +
      (this.cobrarProdutoJuntoAdesaoMatricula() ? this.getPlanoSelecionado().valorProdutos : 0) +
      (this.getPlanoSelecionado().mesAnuidade && this.getPlanoSelecionado().anuidadeAgora
        ? this.getCupom()
          ? this.getValorAnuidadeDesconto()
          : this.getParcelarAnuidade()
          ? this.getValorAnuidadeParcelaUm()
          : this.getPlanoSelecionado().anuidade
        : 0) +
      this.getValorTotalProdutosSelecionados()
    );
  }

  getValorPrimeiraParcelaDesconto(): number {
    var parcela1 = this.getValorPrimeiraParcela();
    for (let i = 0; i < this.getListaPremios().length; i++) {
      if (this.getListaPremios()[i].descricaoPremio === 'PARCELA 1') {
        if (this.getListaPremios()[i].percentualDesconto != 0.0) {
          return parcela1 - (parcela1 * this.getListaPremios()[i].percentualDesconto) / 100;
        } else if (this.getListaPremios()[i].valorDesconto != 0.0) {
          return parcela1 - this.getListaPremios()[i].valorDesconto;
        } else {
          return parcela1;
        }
      }
    }
    return parcela1;
  }

  getValorTotalConcedidoDesconto(): number {
    var parcela1 = 0.0;
    for (let i = 0; i < this.getListaPremios().length; i++) {
      if (this.getListaPremios()[i].descricaoPremio === 'PARCELA 1') {
        if (this.getListaPremios()[i].percentualDesconto != 0.0) {
          return (parcela1 += (this.getValorTotal() * this.getListaPremios()[i].percentualDesconto) / 100);
        } else if (this.getListaPremios()[i].valorDesconto != 0.0) {
          return (parcela1 += this.getListaPremios()[i].valorDesconto);
        } else {
          return parcela1;
        }
      }
    }
    return parcela1;
  }

  mostraParcelas() {
    const display = this.parcelasContainer.nativeElement.style.display;
    if (display !== 'none') {
      this.parcelasContainer.nativeElement.style.display = 'none';
    } else {
      this.parcelasContainer.nativeElement.style.display = 'block';
    }
  }

  getParcelas(): string[] {
    const valores: Array<string> = [];
    let numeroParcela = 1;
    let anoEMes;
    let descricaoParcela: Array<string> = [];
    this.getVezesEscolhidasParcelarMatricula();
    for (let i = 1; i < this.negociacaoService.parcelas.length; i++) {
      numeroParcela++;
      anoEMes = this.negociacaoService.parcelas[i].descricao.split('-');
      descricaoParcela = anoEMes[1];
      let valorDessaParcela =
        this.negociacaoService.parcelas[i].valor +
        (this.planoService.vezesEscolhidasParcelarTaxaMatricula > i
          ? this.getPlanoSelecionado().matricula / this.planoService.vezesEscolhidasParcelarTaxaMatricula
          : 0);
      valores.push(
        (this.getPlanoSelecionado().matricula > 0 && this.getVezesEscolhidasParcelarMatricula() > i
          ? 'true  |'
          : 'false |') +
          numeroParcela +
          'ª Parcela -' +
          descricaoParcela +
          ' ' +
          this.getMoeda() +
          ' ' +
          valorDessaParcela.toFixed(2),
      );
    }
    return valores;
  }

  getVezesEscolhidasParcelarMatricula(): number {
    if (
      this.vezesEscolhidasParcelarMatricula > 0 &&
      this.vezesEscolhidasParcelarMatricula <= this.getPlanoSelecionado().nrVezesParcelarMatricula
    ) {
      this.planoService.vezesEscolhidasParcelarTaxaMatricula = this.vezesEscolhidasParcelarMatricula;
      return this.vezesEscolhidasParcelarMatricula;
    } else {
      return 1;
    }
  }

  counter(i: number) {
    return new Array(i);
  }
}
