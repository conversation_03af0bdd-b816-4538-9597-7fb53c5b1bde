import { Component, Input, OnInit, ElementRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { PlanoService } from '@base-core/plano/plano.service';
import { Plano } from '@base-core/plano/plano.model';
import { Config } from '@base-core/empresa/config.model';
import { EmpresaService } from '@base-core/empresa/empresa.service';
import { NegociacaoService } from '@base-core/negociacao/negociacao.service';
import { FormGroup, Validators } from '@angular/forms';
import { ProdutoService } from '@base-core/produto/produto.service';
import { Cupom } from '@base-core/cupom-desconto/cupom.model';
import { PremioCupom } from '@base-core/cupom-desconto/premio-cupom.model';
import Swal from 'sweetalert2';
import {TranslateService} from '@ngx-translate/core';


@Component({
  selector: 'pacto-plano-recorrente-parc-operadora',
  templateUrl: './plano-recorrente-parc-operadora.component.html',
  styleUrls: ['./plano-recorrente-parc-operadora.component.scss']
})
export class PlanoRecorrenteParcOperadoraComponent implements OnInit {
  vezesEscolhidasParcelarMatricula: number = 1
@Input() formGroup: FormGroup;
@ViewChild('parcelasdiv') parcelasContainer: ElementRef<HTMLDivElement>;
@ViewChild('parcelasPlanoDiv') parcelasPlanoContainer: ElementRef<HTMLDivElement>;
@ViewChild('parcelasPlanoDivP') parcelasPlanoContainerP: ElementRef<HTMLDivElement>;

test: boolean;
router: string;
valorPrimeiraParcela: number;
valorAdesao: number;
valorProduto: number;
valorAnuidadeDesconto: number;
deatlhe: boolean[];
parcelaIdex: number;

constructor(private _router: Router,
  private empresaService: EmpresaService,
  private negociacaoService: NegociacaoService,
  private produtoService: ProdutoService,
  public planoService: PlanoService,
  private translateService: TranslateService) {
  this.router = _router.url;
}

ngOnInit() {
  this.deatlhe = this.getParcelas().map(s => false);

}

getConfig(): Config {
  return this.empresaService.config;
}

mostraPrimeiraParcelas(): void {

  const display = this.parcelasPlanoContainerP.nativeElement.style.display;
  if(display !== 'none') {
  this.parcelasPlanoContainerP.nativeElement.style.display = 'none';
} else {
  this.parcelasPlanoContainerP.nativeElement.style.display = 'block';
}
  }

mostraDetalheaParcelas(index : number): void {


  this.parcelaIdex = this.negociacaoService.parcelas[index + 1].valor;
  this.deatlhe[index] = !this.deatlhe[index];
}

getDescricaoValorPrimeiraParcela() {

  if (this.planoService.planoSelecionado.primeiraParcela == 0) {
    this.valorPrimeiraParcela = 0
  } else {
    // if (this.planoService.planoSelecionado.parcelamentoOperadora) {
    //   this.valorPrimeiraParcela = this.planoService.planoSelecionado.mensalidade / parseInt(this.formGroup.get('parcelasCartao').value)
    // } else {
    this.valorPrimeiraParcela = this.planoService.planoSelecionado.primeiraParcela != this.planoService.planoSelecionado.mensalidade ? this.planoService.planoSelecionado.primeiraParcela : this.planoService.planoSelecionado.mensalidade;
  }
  this.valorAdesao = (this.getCupom() ? this.getValorAdesaoDescontoPrimeiraParcela() : this.getPlanoSelecionado().adesao);
  this.valorProduto = (this.cobrarProdutoJuntoAdesaoMatricula() ? this.getPlanoSelecionado().valorProdutos : 0);
  this.valorAnuidadeDesconto = (this.getPlanoSelecionado().mesAnuidade && this.getPlanoSelecionado().anuidadeAgora ?
    (this.getCupom() ? this.getValorAnuidadeDescontoPrimeiraParcela() :
      (this.getParcelarAnuidade() ? this.getValorAnuidadeParcelaUm() : this.getPlanoSelecionado().anuidade)) : 0) + this.getValorTotalProdutosSelecionados();

}
getValorAnuindade(): number {
  return this.planoService.planoSelecionado.anuidade;
}

getPlanoSelecionado(): Plano {
  return this.planoService.planoSelecionado;
}

getMesAnoCobrancaAnuidade(): string {
  return (this.planoService.planoSelecionado ? this.planoService.planoSelecionado.mesAnuidade : '') + (this.negociacaoService.anoCobrancaAnuidade ? ' / ' + this.negociacaoService.anoCobrancaAnuidade : '');
}

getParcelarAnuidade(): boolean {
  return this.planoService.planoSelecionado != null &&
    this.planoService.planoSelecionado.parcelasAnuidade != null &&
    this.planoService.planoSelecionado.parcelasAnuidade.length > 0;
}

getMesAnuidade(): boolean {
  return this.planoService.planoSelecionado != null &&
    this.planoService.planoSelecionado.mesAnuidade === 'Parcela 1' ||
    (this.planoService.planoSelecionado.parcelasAnuidade != null &&
      this.planoService.planoSelecionado.parcelasAnuidade.length > 0 &&
      this.planoService.planoSelecionado.parcelasAnuidade[0].parcela === 1);
}

detalharParcelas(): boolean {
  return ((this.empresaService.config.detalharParcelaTelaCheckout != null && this.empresaService.config.detalharParcelaTelaCheckout.valueOf() === 'true') ? true : false);
}

apresentarValorAnuidade(): boolean {
  return ((this.empresaService.config.apresentarvaloranuidade != null && this.empresaService.config.apresentarvaloranuidade.valueOf() === 'true') ? true : false);
}

getProdutosPlano(): string[] {
  return this.getPlanoSelecionado().produtos.split('<br/>');
}
getMoeda() :String{
  return this.empresaService.unidadeSelecionada.moeda;
}
validarCobrarPrimeiraParcelaSempre(): boolean {
  var dia = new Date();
  var config = ((this.empresaService.config.cobrarPrimeiraParcelaCompra != null && this.empresaService.config.cobrarPrimeiraParcelaCompra.valueOf() === 'true') ? true : false);
  var diaVencimento = false;

  if (this.formGroup.get('diavencimento') != null && this.formGroup.get('diavencimento').value != null) {
    if (dia.getDate() == this.formGroup.get('diavencimento').value) {
      diaVencimento = true;
    }
  }
  return (config || diaVencimento);
}

cobrarProdutoJuntoAdesaoMatricula(): boolean {
  var dia = new Date();
  var configProdJunto = ((this.empresaService.config.cobrarProdutoJuntoAdesaoMatricula != null && this.empresaService.config.cobrarProdutoJuntoAdesaoMatricula.valueOf() === 'true'));
  var diaVencimento: boolean;

  if (this.formGroup.get('diavencimento') != null && this.formGroup.get('diavencimento').value != null) {
    if (dia.getDate() != this.formGroup.get('diavencimento').value && !configProdJunto) {
      diaVencimento = false;
    } else {
      configProdJunto = true;
      diaVencimento = true;
    }
  } else {
    configProdJunto = true;
    diaVencimento = true;
  }
  return (configProdJunto && diaVencimento);
}

// SUMIR OU APARECER DETALHES DAS PARCELAS DE ACORDO COM A REGRA DE NEGOCIO NA TELA DE CHECKOUT
getClassAdeTextResponsive(): string {
  return this.validarCobrarPrimeiraParcelaSempre() ? 'ade' : 'ade-responsive';
}

getClassAdeValueResponsive(): string {
  return this.getPlanoSelecionado().adesao === 0.0 ? this.validarCobrarPrimeiraParcelaSempre() ? 'parc-ade parc-ade-0' : 'parc-ade-responsive parc-ade-0' :
    this.validarCobrarPrimeiraParcelaSempre() ? 'parc-ade' : 'parc-ade-responsive';
}

getClassProdTextResponsive(): string {
  return this.validarCobrarPrimeiraParcelaSempre() ? 'prod' : 'prod-responsive';
}

getClassProdValueResponsive(): string {
  return this.getPlanoSelecionado().adesao === 0.0 ? this.validarCobrarPrimeiraParcelaSempre() ? 'parc-prod parc-prod-0' : 'parc-prod-responsive parc-prod-0' :
    this.validarCobrarPrimeiraParcelaSempre() ? 'parc-prod' : 'parc-prod-responsive';
}

getClassAnuidTextResponsive(): string {
  if (this.getClassProdTextResponsive() === 'prod-responsive' && this.cobrarProdutoJuntoAdesaoMatricula()) {
    return 'anuid-responsive2';
  } else if (this.validarCobrarPrimeiraParcelaSempre() && (!(this.cobrarProdutoJuntoAdesaoMatricula()))) {
    return 'anuid-responsive2';
  } else {
    return this.cobrarProdutoJuntoAdesaoMatricula() ? 'anuid' : 'anuid-responsive';
  }
}

getClassAnuidValueResponsive(): string {
  if (this.getClassProdTextResponsive() === 'prod-responsive' && this.cobrarProdutoJuntoAdesaoMatricula()) {
    return 'parc-anuid-responsive2';
  } else if (this.validarCobrarPrimeiraParcelaSempre() && (!(this.cobrarProdutoJuntoAdesaoMatricula()))) {
    return 'parc-anuid-responsive2';
  } else {
    return this.getPlanoSelecionado().anuidade === 0.0 ? this.cobrarProdutoJuntoAdesaoMatricula() ? 'parc-anuid parc-anuid-0' : 'parc-anuid-responsive parc-anuid-0' :
      this.cobrarProdutoJuntoAdesaoMatricula() ? 'parc-anuid' : 'parc-anuid-responsive';
  }
}

getClassBarraResponsive(): string {
  if (this.getClassAnuidTextResponsive() === 'anuid-responsive' && this.getClassAdeTextResponsive() === 'ade-responsive') {
    return 'linha-selec-responsive';
  } else if (this.getClassAnuidTextResponsive() === 'anuid-responsive2') {
    return 'linha-selec-responsive3';
  } else if (this.getClassAdeTextResponsive() === 'ade-responsive' && !this.cobrarProdutoJuntoAdesaoMatricula()) {
    return 'linha-selec-responsive2';
  } else {
    return 'linha-selec linha-selec-2';
  }
}

getCupom(): Cupom {
  return this.negociacaoService.cupom;
}

getListaPremios(): PremioCupom[] {
  let PremioCupom = [];
  return this.negociacaoService.cupom == undefined ? PremioCupom : this.negociacaoService.cupom.listaPremios;
}

limparCupom() {
  this.negociacaoService.cupom = null;
}

getPrimeiraParcelaValor(): number {
  if (this.negociacaoService.valorPrimeiraParcela) {
    return this.negociacaoService.valorPrimeiraParcela
  } else {
    return this.planoService.planoSelecionado.mensalidade + this.planoService.planoSelecionado.matricula + this.planoService.planoSelecionado.adesao
  }
}

getValorProRata(): number {
  return this.negociacaoService.valorProRata;
}

getValorPrimeiraParcela(): number {
  this.getDescricaoValorPrimeiraParcela();
  return (this.valorPrimeiraParcela +
    (this.getCupom() ? this.getValorAdesaoDescontoPrimeiraParcela() : this.getPlanoSelecionado().adesao / this.planoService.vezesEscolhidasParcelarTaxaMatricula) +
    (this.cobrarProdutoJuntoAdesaoMatricula() ? this.getPlanoSelecionado().valorProdutos : 0) +
    (this.getPlanoSelecionado().mesAnuidade && this.getPlanoSelecionado().anuidadeAgora ?
      (this.getCupom() ? this.getValorAnuidadeDescontoPrimeiraParcela() : (this.getParcelarAnuidade() ?
        this.getValorAnuidadeParcelaUm() : this.getPlanoSelecionado().anuidade)) : 0)) + this.getValorTotalProdutosSelecionados();
}

getValorTotalProdutosSelecionados(): number {
  return this.produtoService.getValorTotalProdutosSelecionados();
}

getValorPrimeiraParcelaDesconto(): number {
  var parcela1 = this.getValorPrimeiraParcela();
  for (let i = 0; i < this.getListaPremios().length; i++) {
    if (this.getListaPremios()[i].descricaoPremio === 'PARCELA 1') {
      if (this.getListaPremios()[i].percentualDesconto != 0.0) {
        return parcela1 - ((parcela1 * this.getListaPremios()[i].percentualDesconto) / 100);
      } else if (this.getListaPremios()[i].valorDesconto != 0.0) {
        return parcela1 - this.getListaPremios()[i].valorDesconto;
      } else {
        return parcela1;
      }
    }
  }
  return parcela1;
}

getValorAdesaoDesconto(): number {
  var adesao = this.getPlanoSelecionado().adesao;
  for (let i = 0; i < this.getListaPremios().length; i++) {
    if (this.getListaPremios()[i].descricaoPremio === 'ADESÃO PLANO RECORRENTE') {
      if (this.getListaPremios()[i].percentualDesconto != 0.0) {
        return adesao - ((adesao * this.getListaPremios()[i].percentualDesconto) / 100);
      } else if (this.getListaPremios()[i].valorDesconto != 0.0) {
        return adesao - this.getListaPremios()[i].valorDesconto;
      } else {
        return adesao;
      }
    }
  }
  return adesao;
}

getValorAdesaoDescontoPrimeiraParcela(): number {
  var temPremio = false;
  var adesao = this.getPlanoSelecionado().adesao;
  for (let i = 0; i < this.getListaPremios().length; i++) {
    if (this.getListaPremios()[i].descricaoPremio === 'ADESÃO PLANO RECORRENTE') {
      temPremio = true;
      if (this.getListaPremios()[i].percentualDesconto != 0.0) {
        return adesao - ((adesao * this.getListaPremios()[i].percentualDesconto) / 100);
      } else if (this.getListaPremios()[i].valorDesconto != 0.0) {
        return adesao - this.getListaPremios()[i].valorDesconto;
      } else {
        return adesao;
      }
    }
  }
  return temPremio ? adesao : 0;
}

getValorAnuidadeDesconto(): number {
  var anuidade = this.getPlanoSelecionado().anuidade;
  for (let i = 0; i < this.getListaPremios().length; i++) {
    if (this.getListaPremios()[i].descricaoPremio === 'ANUIDADE PLANO RECORRENTE') {
      if (this.getListaPremios()[i].percentualDesconto != 0.0) {
        return ((anuidade / 100) * this.getListaPremios()[i].percentualDesconto);
      } else if (this.getListaPremios()[i].valorDesconto != 0.0) {
        return anuidade - this.getListaPremios()[i].valorDesconto;
      } else {
        return anuidade;
      }
    }
  }
  return anuidade;
}

getValorAnuidadeDescontoPrimeiraParcela(): number {
  var temPremio = false;
  var anuidade = this.getPlanoSelecionado().anuidade;
  for (let i = 0; i < this.getListaPremios().length; i++) {
    if (this.getListaPremios()[i].descricaoPremio === 'ANUIDADE PLANO RECORRENTE') {
      temPremio = true;
      if (this.getListaPremios()[i].percentualDesconto != 0.0) {
        return ((anuidade / 100) * this.getListaPremios()[i].percentualDesconto);
      } else if (this.getListaPremios()[i].valorDesconto != 0.0) {
        return anuidade - this.getListaPremios()[i].valorDesconto;
      } else {
        return anuidade;
      }
    }
  }
  return temPremio ? anuidade : 0;
}

getValorAnuidadeParcelaUm(): number {
  for (let i = 0; i < this.getPlanoSelecionado().parcelasAnuidade.length; i++) {
    if (this.getPlanoSelecionado().parcelasAnuidade[i].parcelaApresentar.toLowerCase() === 'no mesmo dia da parcela 1') {
      return this.getPlanoSelecionado().parcelasAnuidade[i].valor;
    }
  }
  return 0;
}

getParcelaDescricaoPluSing(valor): string {
  if (valor > 1) {
    return 'A anuidade deste plano é dividida em ' + valor + ' parcelas:';
  }
  return 'A anuidade deste plano contém uma unica parcela:';
}

getAnuidadeDescricao(obj, valor): string {
  if (valor > 1) {
    return 'A ' + obj.numero + 'ª parcela será cobrada ' + obj.parcelaApresentar.toLowerCase() + '.';
  }
  return 'Parcela 1, que será cobrada ' + obj.parcelaApresentar.toLowerCase() + '.';
}

mostraParcelas(): void {
  const display = this.parcelasPlanoContainer.nativeElement.style.display;
  if(display !== 'none') {
    this.parcelasPlanoContainer.nativeElement.style.display = 'none';
  } else {
    this.parcelasPlanoContainer.nativeElement.style.display = 'block';
  }
}

getParcelas(): string[] {

  const valores: Array<string> = [];
  let numeroParcela = 1;
  let anoEMes;
  let descricaoParcela: Array<string> = [];
  let valorAnuidade = 0;
  for (let i = 1; i < this.negociacaoService.parcelas.length; i++) {
    numeroParcela++;
    anoEMes = this.negociacaoService.parcelas[i].descricao.split("-");
    descricaoParcela = anoEMes[1];

    for (let x = 0; x <= this.planoService.planoSelecionado.parcelasAnuidade.length - 1; x++) {
      if (this.planoService.planoSelecionado.parcelasAnuidade[x].parcela == numeroParcela && numeroParcela != 1) {
        valorAnuidade = this.planoService.planoSelecionado.parcelasAnuidade[x].valor;
        x = this.planoService.planoSelecionado.parcelasAnuidade.length + 1;
      }
      else {
        valorAnuidade = 0;
      }
    }
    valores.push(((valorAnuidade > 0 || (this.getPlanoSelecionado().matricula > 0 && this.getVezesEscolhidasParcelarMatricula() > i)) ? "true  |" : "false |") + numeroParcela + "ª Parcela -" + descricaoParcela + ' ' + this.getMoeda() + ' '
      + ((this.planoService.vezesEscolhidasParcelarTaxaMatricula > i ? this.getPlanoSelecionado().matricula / this.planoService.vezesEscolhidasParcelarTaxaMatricula : 0) + (this.getValorParcelaDesconto(this.negociacaoService.parcelas[i].valor + valorAnuidade, numeroParcela))).toFixed(2));
  }
  return valores;
}

getDescricaoCobrancaPrimeiraParcela(): string {
  if (this.negociacaoService.descricaoCobrancaPrimeiraParcela) {
    return this.negociacaoService.descricaoCobrancaPrimeiraParcela;
  }
  return this.translateService.instant('planos.plano-recorrente.hoje');
}


getValorParcelaDesconto(valorParcela: number, parcelaSelecionada): number {
  let valorParcelas: number;
  if (this.getListaPremios().length > 1) {
    for (let i = 0; i < this.getListaPremios().length; i++) {
      if (this.getListaPremios()[i].descricaoPremio === 'PARCELA ' + parcelaSelecionada) {
        if (this.getListaPremios()[i].percentualDesconto != 0.0) {
          return valorParcela - ((valorParcela * this.getListaPremios()[i].percentualDesconto) / 100);
        } else if (this.getListaPremios()[i].valorDesconto != 0.0) {
          return valorParcela - this.getListaPremios()[i].valorDesconto;
        }
      }
    }
  }
  else {
    valorParcelas = valorParcelas;
  }
  return valorParcela;
}

getValidarData(): void {
  if(this.formGroup.get('dataInicioContrato') != null && this.formGroup.get('dataInicioContrato').value != null) {
  var partesData = this.formGroup.get('dataInicioContrato').value.split("-");
  var data = new Date(partesData[0], partesData[1] - 1, partesData[2], 23, 59, 59);
  if (data < new Date()) {
    Swal.fire({
      type: 'error',
      title: 'Inicio do Contrato!',
      text: 'A data informada não pode ser menor que hoje.',
      showConfirmButton: true
    });
    this.formGroup.get('dataInicioContrato').setValue(null);
    return;
  }
}
  }
getSomaTodasParcelas(): number {
  if (this.negociacaoService.parcelas.length !== 0 && this.getParcelas()) {
    var somaTotalParcelas = (
      this.valorAdesao +
      this.planoService.planoSelecionado.anuidade +
      this.planoService.planoSelecionado.matricula +
      this.planoService.planoSelecionado.primeiraParcela
    )

    for (let i = 1; i < this.negociacaoService.parcelas.length; i++) {
      somaTotalParcelas += this.negociacaoService.parcelas[i].valor
    }
    return somaTotalParcelas
  }
}

  getVezesEscolhidasParcelarMatricula(): number {
    if (this.vezesEscolhidasParcelarMatricula > 0 && this.vezesEscolhidasParcelarMatricula <= this.getPlanoSelecionado().nrVezesParcelarMatricula) {
      this.planoService.vezesEscolhidasParcelarTaxaMatricula = this.vezesEscolhidasParcelarMatricula
      return this.vezesEscolhidasParcelarMatricula
    } else {
      return 1
    }
  }

  counter(i: number) {
    return new Array(i);
  }

}
