<div class="caixa-plano-selecionado">
  <span class="titulo">{{"planos.plano-credito.plano-selecionado"|translate}}: </span>
  <span class="nome-planoResumo" [style.color]="getConfig().cor">{{getPlanoSelecionado().nome.toLowerCase()}}</span>

  <span class="titulo" *ngIf="isPossuiParcela()">{{"Valor total do Plano"}}: </span>
  <span class="nome-planoResumo" *ngIf="isPossuiParcela() && !getCupom()" [style.color]="getConfig().cor">{{obterValorTotalPlanoSemCupom()| currency: this.getMoeda() :'symbol'}}</span>
  <span class="nome-planoResumo" *ngIf="isPossuiParcela() && getCupom()" [style.color]="getConfig().cor">{{obterValorTotalPlanoComCupom()| currency: this.getMoeda() :'symbol'}}</span>

  <div class="container" [style.border-left-color]="getConfig().cor">

    <div class="arrow-right" [style.border-left-color]="getConfig().cor"></div>

    <!--      CONFIGURAÇÃO DETALHAR PARCELAS MARCADA-->
    <div *ngIf="detalharParcelas()"
      class="span_5_of_5 ta-center pedido ta-center">
      <span class="titulo" *ngIf="getPlanoSelecionado().primeiraParcela > 0.0">{{"planos.plano-credito.mensalidade-1"|translate}}:</span>
      <span class="nome-planoResumo" [style.color]="getConfig().cor">{{(getPlanoSelecionado().primeiraParcela)| currency: this.getMoeda() :'symbol'}}</span>
      <span class="titulo" *ngIf="getPlanoSelecionado().matricula > 0.0">{{"planos.plano-credito.matricula"|translate}}:</span>
      <span class="nome-planoResumo" [style.color]="getConfig().cor"
            *ngIf="getPlanoSelecionado().matricula > 0.0">{{getPlanoSelecionado().matricula| currency: this.getMoeda() :'symbol'}}</span>
      <div>
        <label class="titulo" for="dividirMatricula"
          *ngIf="getPlanoSelecionado().nrVezesParcelarMatricula > 1 && getPlanoSelecionado().matricula">
          {{'divisao-taxa-matricula.dividir-matricula-em-quantas-vezes' | translate}} </label>
        <select name="dividirMatricula" class="custom-select-taxa-matricula" [(ngModel)]="vezesEscolhidasParcelarMatricula"
          (change)='this.getVezesEscolhidasParcelarMatricula()'
          *ngIf="getPlanoSelecionado().nrVezesParcelarMatricula > 1 && getPlanoSelecionado().matricula">
          <option
            *ngFor="let loop of counter(this.getPlanoSelecionado().nrVezesParcelarMatricula) ;let i= index, let f = first"
            [attr.selected]="first" [ngValue]="i+1">
            <p *ngIf="f"> {{'divisao-taxa-matricula.nao-dividir-taxa' | translate}}</p>
            <p *ngIf="!f"> {{'divisao-taxa-matricula.dividir-em' | translate}} {{i+1}}
              {{'divisao-taxa-matricula.parcelas-de' | translate}} {{(getPlanoSelecionado().matricula / [i+1]) |
              currency:this.getMoeda():'symbol' }}</p>
          </option>
        </select>
      </div>

      <span class="titulo" *ngIf="(getValorTotalProdutosSelecionados() > 0.0 || getPlanoSelecionado().valorProdutos > 0.0)">{{"planos.plano-credito.produtos"|translate}}:</span>
      <span class="nome-planoResumo" [style.color]="getConfig().cor"
            *ngIf="(getValorTotalProdutosSelecionados() > 0.0 || getPlanoSelecionado().valorProdutos > 0.0)">{{(getValorTotalProdutosSelecionados() + getPlanoSelecionado().valorProdutos)| currency: this.getMoeda() :'symbol'}}</span>
      <span class="titulo">{{"planos.plano-credito.o-valor-da-sua-primeira-parcela-sera"|translate}}:</span>
      <span class="nome-planoResumo" *ngIf="!getCupom() && detalharParcelas()" [style.color]="getConfig().cor">{{(getValorTotalProdutosSelecionados() + getPlanoSelecionado().primeiraParcela + (this.planoService.vezesEscolhidasParcelarMatricula > i ? this.getPlanoSelecionado().matricula /
        this.planoService.vezesEscolhidasParcelarMatricula : this.getPlanoSelecionado().matricula) + getPlanoSelecionado().valorProdutos + (getPlanoSelecionado().mesAnuidade && getPlanoSelecionado().anuidadeAgora ? getPlanoSelecionado().anuidade : 0))| currency: this.getMoeda() :'symbol'}}</span>
      <span class="nome-planoResumo" *ngIf="getCupom()" [style.color]="getConfig().cor">{{(getValorPrimeiraParcelaDesconto()| currency: this.getMoeda() :'symbol')}}</span>
    </div>
    <!--      FIM CONFIGURAÇÃO MARCADA-->

    <div class="linha-selec-total"></div>
    <div class="linha-selec-total"></div>

    <!--      CONFIGURAÇÃO DETALHAR PARCELAS DESMARCADA-->
    <span class="titulo" *ngIf="!detalharParcelas()">{{"planos.plano-credito.o-valor-da-sua-primeira-parcela-sera"|translate}}:</span>
    <span class="nome-planoResumo" *ngIf="!getCupom() && !detalharParcelas()" [style.color]="getConfig().cor">{{(getValorTotalProdutosSelecionados() + getPlanoSelecionado().primeiraParcela + (this.planoService.vezesEscolhidasParcelarMatricula > i ? this.getPlanoSelecionado().matricula /
      this.planoService.vezesEscolhidasParcelarMatricula : this.getPlanoSelecionado().matricula) + getPlanoSelecionado().valorProdutos + (getPlanoSelecionado().mesAnuidade && getPlanoSelecionado().anuidadeAgora ? getPlanoSelecionado().anuidade : 0))| currency: this.getMoeda() :'symbol'}}</span>
    <span class="nome-planoResumo" *ngIf="getCupom()" [style.color]="getConfig().cor">{{(getValorPrimeiraParcelaDesconto()| currency: this.getMoeda() :'symbol')}}</span>
    <!--      FIM CONFIGURAÇÃO DESMARCADA-->

    <div><span *ngIf="getParcelas().length > 0" class="titulo">{{"demais parcelas"}}:</span></div>
    <div id="parcelasdivinfo" >
      <span *ngFor="let p of getParcelas()" class="detalhe">
        <span [style.color]="getConfig().cor">{{p.split('|')[1]}}</span>
        <p *ngIf="p.split('|')[0] != 'false '" class="detalhamento-parcela">
          {{'divisao-taxa-matricula.taxa-matricula' | translate}}: {{(getPlanoSelecionado().matricula /
          this.planoService.vezesEscolhidasParcelarTaxaMatricula)|currency:this.getMoeda():'symbol'}} </p>
      </span>
    </div>

    <div><span class="titulo">{{""}}:</span></div>

    <div *ngIf="getPlanoSelecionado().descricaoEncantamento">{{getPlanoSelecionado().descricaoEncantamento}}</div>


    <span class="titulo">{{"planos.plano-credito.esse-plano-inclui"|translate}}:</span>
    <span *ngFor="let m of getPlanoSelecionado().modalidades" class="modalidade">
       <i [style.color]="getConfig().cor" class="tem pct-check-circle"></i> <span>{{m}}</span>
    </span>

    <div *ngIf="getPlanoSelecionado().produtos">
      <span class="titulo">{{"planos.plano-credito.produtos-no-plano"|translate}}:</span>
      <span *ngFor="let p of getProdutosPlano()" class="modalidade">
       <i [style.color]="getConfig().cor" class="tem pct-check-circle"></i> <span>{{p}}</span>
    </span>
    </div>

    <div>
      <span class="titulo">{{"planos.plano-credito.quantidade-de-creditos-a-adquirir"|translate}}:</span>
      <span class="primeiraparcela"
            [style.color]="getConfig().cor">{{getPlanoSelecionado().qtdCreditoPlanoCredito}}</span>
    </div>

    <span class="titulo" >{{"Duração"}}: </span>
    <span class="nome-planoResumo" *ngIf="getFidelidadePlano() == 1" [style.color]="getConfig().cor">{{this.planoService.planoSelecionado.fidelidade}} mês</span>
    <span class="nome-planoResumo" *ngIf="getFidelidadePlano() > 1" [style.color]="getConfig().cor">{{this.planoService.planoSelecionado.fidelidade}} meses</span>

  </div>

  <div class="leiacontrato">
    <pacto-visualizar-documento [tipo]="'contrato'" [inside]="true"
                                [plano]="getPlanoSelecionado().codigo"
                                [textoLink]="'global.leiacontrato'|translate"></pacto-visualizar-documento>
  </div>
</div>
