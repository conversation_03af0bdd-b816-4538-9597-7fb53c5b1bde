import { Component, OnInit } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { NegociacaoService } from '@base-core/negociacao/negociacao.service';
import { EmpresaService } from '@base-core/empresa/empresa.service';
import { AlunoService } from "@base-core/aluno/aluno.service";
import { Aluno } from "@base-core/aluno/aluno.model";
import { Empresa } from '@base-core/empresa/empresa.model';
import { Config } from '@base-core/empresa/config.model';
import QRCode from 'qrcode';

@Component({
  selector: 'pacto-pospagamento',
  templateUrl: './pospagamento.component.html',
  styleUrls: ['./pospagamento.component.scss']
})
export class PosPagamentoComponent implements OnInit {
  formGroup: FormGroup = new FormGroup({});

  constructor(private route: ActivatedRoute,
    private negociacaoService: NegociacaoService,
    private empresaService: EmpresaService,
    private alunoService: AlunoService) {
    this.route.queryParams.subscribe(params => {
      if (params['k']) {
        this.negociacaoService.chave = params['k'];
      }
      if (params['un']) {
        this.negociacaoService.codunidade = params['un'];
      }
      if (params['tp']) {
        this.negociacaoService.codigoPix = params['tp'];
      }
    });

    localStorage.removeItem('limparDadosCheckout');
    localStorage.setItem('limparDadosCheckout', 'true');

  }

  ngOnInit() {
    this.loadUnidade();
    this.loadConfigs();
  }
  loadConfigs() {
    this.empresaService
      .obterConfigs(this.negociacaoService.chave, this.negociacaoService.codunidade)
      .subscribe((data) => {
        this.empresaService.config = data;
      });
  }
  loadUnidade(): void {
    if (!this.empresaService.unidadeSelecionada) {
      this.empresaService.obterEmpresa(
        this.negociacaoService.chave,
        this.negociacaoService.codunidade)
        .subscribe(data => this.empresaService.unidadeSelecionada = data);
    }
  }

  alunoSelecionado(): Aluno {
    return this.alunoService.alunoSelecionado ? this.alunoService.alunoSelecionado : new Aluno(null, '', '', '',
      '', 0.0, '', false, 1, 0.0, [], '',
      '', '', '');
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  getUnidadeSelecionada(): Empresa {
    return this.empresaService.unidadeSelecionada;
  }

  getMsgPosPagamento(): string {
    return window.localStorage.getItem('msgFinalOperacao');
  }

  generateQRCode(data: string | undefined): string | null {
    if (!data) {
      return null;
    }
    let qrCodeUrl: string | null = null;
    QRCode.toDataURL(data, { width: 150 }, (err, url) => {
      if (err) {
        console.error('Erro ao gerar QR Code:', err);
        qrCodeUrl = null;
      } else {
        qrCodeUrl = url;
      }
    });
    return qrCodeUrl;
  }

}
