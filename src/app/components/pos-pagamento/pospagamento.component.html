<div class="container">
  <pacto-header [config]="getConfig()" [unidade]="getUnidadeSelecionada()"></pacto-header>

  <h1 class="ola" *ngIf="getMsgPosPagamento() === '1'">{{"Pagamento efetuado com sucesso!"}}</h1>
  <h1 class="ola" *ngIf="getMsgPosPagamento() === '2'">{{"Cartão cadastrado com sucesso!"}}</h1>
  <h1 class="ola" *ngIf="getMsgPosPagamento() === '3'">{{"Venda realizada com sucesso!"}}</h1>
  <h1 class="ola" *ngIf="getMsgPosPagamento() === '4'">{{"Cadastro efetuado e agendamento realizado com sucesso!"}}</h1>
  <h4>{{"pospagamento.obrigado"|translate}}.<img class="feliz" src="assets/images/icon/smile-wink-regular.svg" height="20" width="23"/></h4>

  <div class="row">
    <div class="column col-md-6">
      <span class="resumo">{{"pospagamento.sua-unidade"|translate}}:</span>
      <pacto-unidade-selecionada></pacto-unidade-selecionada>
    </div>
  </div>

  <div *ngIf="getMsgPosPagamento() === '3' && getConfig()?.ativarLinksGooglePlayEAppleStore" class="links-google-apple display-flex-vertical feliz">
    <p style="font-size: 20px;" >{{"pospagamento.links-baixar-app-treino"|translate}}</p>
    <div class="links-container">
      <div *ngIf="getConfig()?.urlLinkGooglePlay" class="display-flex-vertical">
        <img *ngIf="getConfig()?.urlLinkGooglePlay" [src]="generateQRCode(getConfig()?.urlLinkGooglePlay)" alt="QR Code Google Play" width="200" height="200">
        <a [href]="getConfig()?.urlLinkGooglePlay"
           target="_blank"
           style="font-size: 14px; text-decoration: none; color: #2E3133;">
          {{getConfig()?.urlLinkGooglePlay}}
        </a>
      </div>
      <div *ngIf="getConfig()?.urlLinkAppleStore" class="display-flex-vertical">
        <img *ngIf="getConfig()?.urlLinkAppleStore" [src]="generateQRCode(getConfig()?.urlLinkAppleStore)" alt="QR Code Apple Store" width="200" height="200">
        <a [href]="getConfig()?.urlLinkAppleStore"
           target="_blank"
           style="font-size: 14px; text-decoration: none; color: #2E3133;">
          {{getConfig()?.urlLinkAppleStore}}
        </a>
      </div>
    </div>
  </div>

</div>


