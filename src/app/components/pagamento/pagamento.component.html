<div class="container">
  <pacto-header [config]="getConfig()" [unidade]="getUnidadeSelecionada()"></pacto-header>

  <h1 class="ola">Ol<PERSON>, {{alunoSelecionado().nome ? alunoSelecionado().nome.toLowerCase() : ''}}</h1>
  <div *ngIf="formaCartao()">
    <h4>Informe os dados do cartão para facilitar o pagamento de suas parcelas</h4>
  </div>
  <div *ngIf="formaPix()">
    <h4>1. Abra o aplicativo do seu banco no celular <br/>
      2. Selecione a opção de pagar com Pix/ escanear QR code<br/>
      3. Confirme as informações e finalize o pagamento</h4>
  </div>
  <div *ngIf="formaBoleto()">
    <h4>1. Copie a linha digitável ou baixe o seu boleto <br/>
      2. Abra o aplicativo do seu banco, selecione a opção de pagamento e adicione a linha digitável ou insira o arquivo
      do boleto<br/>
      3. <PERSON><PERSON><PERSON> as informações exibidas e finalize o pagamento</h4>
  </div>

  <div class="row space">
    <div class="column col-md-7">
      <div class="column col-md-12" *ngIf="formaNenhuma()" style="padding: 0;">
        <h4>Selecione a forma de pagamento</h4>
      </div>

      <div *ngIf="!isLinkPagamentoPactoPayComunicacao()" style="display: flex">
        <div *ngIf="getConfig()?.apresentarCartao" style="padding-right: 10px">
          <img class="align-icon-cartao" src="assets/images/icon/cvv-icon.png"/>
          <button [ngClass]="formaCartao() ? 'pacto-btn-pagamento-sel' : 'pacto-btn-pagamento'" (click)="cobrarCartao()"
                  style="padding-left: 30px;">Cartão de Crédito
          </button>
        </div>
        <div *ngIf="getConfig()?.apresentarPix && alunoSelecionado().valorCobrar > 0 && cobrarParcelasEmaberto()"
             style="padding-right: 10px">
          <img class="align-icon-pix" src="assets/images/icon/pix-icon.png"/>
          <button [ngClass]="formaPix() ? 'pacto-btn-pagamento-sel' : 'pacto-btn-pagamento'" (click)="cobrarPix()">PIX
          </button>
        </div>
        <div *ngIf="getConfig()?.apresentarBoleto && alunoSelecionado().valorCobrar > 0 && cobrarParcelasEmaberto()">
          <img class="align-icon-boleto" src="assets/images/icon/boleto-icon.png"/>
          <button [ngClass]="formaBoleto() ? 'pacto-btn-pagamento-sel' : 'pacto-btn-pagamento'"
                  (click)="cobrarBoleto()">
            Boleto
          </button>
        </div>
      </div>

      <div *ngIf="isLinkPagamentoPactoPayComunicacao()" style="display: flex">
        <div *ngIf="getConfig()?.apresentarCartaoRegua" style="padding-right: 10px">
          <img class="align-icon-cartao" src="assets/images/icon/cvv-icon.png"/>
          <button [ngClass]="formaCartao() ? 'pacto-btn-pagamento-sel' : 'pacto-btn-pagamento'" (click)="cobrarCartao()"
                  style="padding-left: 30px;">Cartão de Crédito
          </button>
        </div>
        <div *ngIf="getConfig()?.apresentarPixRegua && alunoSelecionado().valorCobrar > 0 && cobrarParcelasEmaberto()"
             style="padding-right: 10px">
          <img class="align-icon-pix" src="assets/images/icon/pix-icon.png"/>
          <button [ngClass]="formaPix() ? 'pacto-btn-pagamento-sel' : 'pacto-btn-pagamento'" (click)="cobrarPix()">PIX
          </button>
        </div>
        <div *ngIf="getConfig()?.apresentarBoletoRegua && alunoSelecionado().valorCobrar > 0 && cobrarParcelasEmaberto()">
          <img class="align-icon-boleto" src="assets/images/icon/boleto-icon.png"/>
          <button [ngClass]="formaBoleto() ? 'pacto-btn-pagamento-sel' : 'pacto-btn-pagamento'"
                  (click)="cobrarBoleto()">
            Boleto
          </button>
        </div>
      </div>

      <div class="column col-md-12" style="padding: 10px 0;">
        <div class="column col-md-12" *ngIf="formaCartao() && getConfig()?.apresentarCartao && valido"
             style="padding: 0">
          <pacto-cartao-credito
            [formGroup]="formGroup"
            [pixelId]="obterPixelMeta()"
            [tokenApiConversao]="obterTokenApiConversaoMeta()"
            [dadosRecebidosLinkPagamento]="dadosBackendLinkPagamento"
          ></pacto-cartao-credito>
        </div>
        <div class="column col-md-12" *ngIf="formaPix() && getConfig()?.apresentarPix && valido"
             style="padding: 0">
          <pacto-debito-pix [url]="url"></pacto-debito-pix>
        </div>
        <div class="column col-md-12" *ngIf="formaBoleto() && getConfig()?.apresentarBoleto && valido"
             style="padding: 0">
          <pacto-boleto [url]="url" [linhaDigitavel]="linhaDigitavel" [valor]="valor" [vencimento]="vencimento"
                        [status]="status" [pixelId]="obterPixelMeta()" [tokenApiConversao]="obterTokenApiConversaoMeta()"></pacto-boleto>
        </div>
      </div>
    </div>
    <div class="column col-md-5">
      <span class="resumo">Sua unidade:</span>
      <pacto-unidade-selecionada></pacto-unidade-selecionada>
      <pacto-info-pagamento *ngIf="cobrarParcelasEmaberto() && valido"></pacto-info-pagamento>
    </div>
  </div>
</div>


