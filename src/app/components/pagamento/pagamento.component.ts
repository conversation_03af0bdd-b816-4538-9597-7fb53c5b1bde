import {Component, OnInit} from '@angular/core';
import {FormGroup, Validators} from '@angular/forms';
import {ActivatedRoute, Router} from '@angular/router';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {AlunoService} from '@base-core/aluno/aluno.service';
import {Aluno, AlunoAdapter} from '@base-core/aluno/aluno.model';
import {PlanoService} from '@base-core/plano/plano.service';
import {Plano} from '@base-core/plano/plano.model';
import {DebitoPixComponent} from '../checkout/debito-pix/debito-pix.component';
import {Config} from '@base-core/empresa/config.model';
import {BoletoComponent} from '../checkout/boleto/boleto.component';
import Swal from 'sweetalert2';
import {FormaPagamento} from '../checkout/shared/enum-forma-pagamento';
import {TranslateService} from '@ngx-translate/core';
import {Empresa} from '@base-core/empresa/empresa.model';
import { FacebookPixelService } from '@base-core/analytics/facebook-pixel.service';

@Component({
  selector: 'pacto-pagamento',
  templateUrl: './pagamento.component.html',
  styleUrls: ['./pagamento.component.scss']
})
export class PagamentoComponent implements OnInit {
  formGroup: FormGroup = new FormGroup({});
  formaPagamento: FormaPagamento = FormaPagamento.NENHUMA;
  DebitoPix: DebitoPixComponent;
  boleto: BoletoComponent;
  url: string;
  linhaDigitavel: string;
  vencimento: string;
  valor: string;
  status: string;
  valido: Boolean = false;
  pixelId = '';
  tokenApiConversao = null;
  dadosBackendLinkPagamento = 0;

  constructor(private route: ActivatedRoute,
              private alunoService: AlunoService,
              private negociacaoService: NegociacaoService,
              private planoService: PlanoService,
              private empresaService: EmpresaService,
              private alunoAdapter: AlunoAdapter,
              private translateService: TranslateService,
              private router: Router,
              private analitycsPixel: FacebookPixelService
  ) {
    
    if (this.route.snapshot.params['chave'] && this.route.snapshot.params['token']) {
      // New URL
      this.negociacaoService.chave = this.route.snapshot.params['chave'];
      this.negociacaoService.token = this.route.snapshot.params['token'];

      this.empresaService.obterDadosToken(this.negociacaoService.chave, this.negociacaoService.token)
        .subscribe(data => {
          console.log(data);
          if (data.hasOwnProperty('return')) {
            this.negociacaoService.chave = data.return['chave'];
            this.negociacaoService.codunidade = data.return['codEmpresa'];
            this.negociacaoService.responsavel = data.return['responsavel'];
            this.negociacaoService.cobrancaAntecipada = data.return['codCobrancaAntecipada'];
            this.negociacaoService.pactoPayComunicacao = data.return['codPactoPayComunicacao'];
            this.negociacaoService.origemCobranca = data.return['origemCobranca'];
            this.negociacaoService.cobrarParcelasEmAberto = (data.return['cobrarParcelasEmAberto']);
            this.negociacaoService.todasEmAberto = (data.return['todasEmAberto']);
            this.negociacaoService.parcelasSelecionadas = (data.return['parcelasSelecionadas']);
            this.clicouPactoPayComunicacao();
            this.loadUnidade();

            //Dados do Link de Pagamento, encaminhados para o componente filho cartao-credito.component.ts
            this.dadosBackendLinkPagamento = (data.return['numeroVezesParcelamentoOperadora']);

            if (data.return['cliente']) {
              this.negociacaoService.matricula = data.return['cliente'];
              this.carregarAluno();
            }
            this.DebitoPix = new DebitoPixComponent(this.negociacaoService, this.empresaService, this.alunoService, this.translateService,this.router,this.analitycsPixel);
            this.boleto = new BoletoComponent(this.negociacaoService, this.empresaService, this.alunoService, this.translateService, this.router,this.analitycsPixel);
          } else {
            this.erroGeral('Erro ao carregar link', data['erro']);
          }
        });
    } else {
      // Old URL
      this.route.queryParams.subscribe(params => {
        this.valido = false;
        if (params['k']) {
          this.negociacaoService.chave = params['k'];
        }
        if (params['un']) {
          this.negociacaoService.codunidade = params['un'];
        }
        if (params['or']) {
          this.negociacaoService.origemCobranca = params['or'];
        }
        if (params['ca']) {
          this.negociacaoService.cobrancaAntecipada = params['ca'];
        }
        this.negociacaoService.cobrarParcelasEmAberto = true;
        if (params['cp']) {
          this.negociacaoService.cobrarParcelasEmAberto = (params['cp'] !== 'f');
        }
        if (params['ppc']) {
          this.negociacaoService.pactoPayComunicacao = params['ppc'];
        }
        this.clicouPactoPayComunicacao();
        this.loadUnidade();
        if (params['cliente']) {
          this.negociacaoService.matricula = params['cliente'];
          this.formaPagamento = FormaPagamento.CARTAO;
          this.carregarAluno();
        }
        this.DebitoPix = new DebitoPixComponent(this.negociacaoService, this.empresaService, this.alunoService, this.translateService,this.router,this.analitycsPixel);
        this.boleto = new BoletoComponent(this.negociacaoService, this.empresaService, this.alunoService, this.translateService, this.router,this.analitycsPixel);
      });
    }
  }

  ngOnInit() {
  }

  loadUnidade(): void {
    if (!this.empresaService.unidadeSelecionada && this.negociacaoService.codunidade) {
      this.empresaService.obterEmpresa(
        this.negociacaoService.chave,
        this.negociacaoService.codunidade)
        .subscribe(data => {
          this.empresaService.unidadeSelecionada = data;
          this.carregarConfigs();
        });
    }
  }

  carregarConfigs(): void {
    if (this.negociacaoService.codunidade) {
      this.empresaService.obterConfigs(this.negociacaoService.chave,
        this.negociacaoService.codunidade).subscribe(data => {
        this.empresaService.config = data;
        this.formaPagamento = FormaPagamento.NENHUMA;
        if (this.empresaService.config && this.empresaService.config.apresentarCartao) {
          this.formaPagamento = FormaPagamento.CARTAO;
        }
        this.pixelId = this.empresaService.config.pixelId;
        this.tokenApiConversao = this.empresaService.config.tokenApiConversao;
      });
    }
  }

  get Validators() {
    return Validators;
  }

  formaNenhuma(): boolean {
    return (this.formaPagamento === FormaPagamento.NENHUMA) ||
      (this.formaCartao() && this.getConfig() != null && !this.getConfig().apresentarCartao) ||
      (this.formaPix() && this.getConfig() != null && !this.getConfig().apresentarPix) ||
      (this.formaBoleto() && this.getConfig() != null && !this.getConfig().apresentarBoleto);
  }

  formaCartao(): boolean {
    return this.formaPagamento === FormaPagamento.CARTAO;
  }

  formaPix(): boolean {
    return this.formaPagamento === FormaPagamento.PIX;
  }

  formaBoleto(): boolean {
    return this.formaPagamento === FormaPagamento.BOLETO;
  }

  alunoSelecionado(): Aluno {
    return this.alunoService.alunoSelecionado ? this.alunoService.alunoSelecionado : new Aluno(null, '', '', '',
      '', 0.0, '', false, 1, 0.0, [], '',
      '', '', '');
  }

  cobrarParcelasEmaberto() {
    return this.negociacaoService.cobrarParcelasEmAberto;
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  getUnidadeSelecionada(): Empresa {
    return this.empresaService.unidadeSelecionada;
  }

  carregarAluno(): void {
    let tipoCobranca = 2;
    if (this.formaPix()) {
      tipoCobranca = 5;
    } else if (this.formaBoleto()) {
      tipoCobranca = 6;
    }
    this.alunoService.aluno(this.negociacaoService.chave, this.negociacaoService.matricula,
      tipoCobranca, this.negociacaoService.cobrancaAntecipada, this.negociacaoService.pactoPayComunicacao,
      this.negociacaoService.todasEmAberto, this.negociacaoService.parcelasSelecionadas)
      .subscribe(data => {
        console.log(data);
        if (data.hasOwnProperty('return')) {
          this.alunoService.alunoSelecionado = this.alunoAdapter.adapt(data['return']);
          this.planoService.planoSelecionado = new Plano(null, null, null, null, null, null, null, null, null, null,
            null, null, null, null, null, null, null, this.alunoService.alunoSelecionado.parcelamentoOperadora,
            this.alunoService.alunoSelecionado.maximoVezesParcelar, null, null, null, null, null,null, null,
            null, null, null, null, null, null, null, false);
          this.valido = true;
          console.log(this.alunoService.alunoSelecionado);
        } else {
          this.valido = false;
          this.erro(data['erro']);
        }
      }, error => {
        this.erro(error);
      });
  }

  cobrarCartao(): void {
    this.formaPagamento = FormaPagamento.CARTAO;
    this.carregarAluno();
  }

  cobrarPix(): void {
    this.formaPagamento = FormaPagamento.PIX;
    this.carregarAluno();

    this.DebitoPix.gerarPix().then(
      (url: string) => {
        this.url = url;
      }
    ).catch((param: any) => {
      console.log(param);
      this.limparFormaPagamento();
    });
    this.valido = true;
  }

  cobrarBoleto(): void {
    this.formaPagamento = FormaPagamento.BOLETO;
    this.status = 'gerando';
    this.carregarAluno();

    this.boleto.gerar().then(
      (json: string) => {
        const jsonRetorno = JSON.parse(json);
        this.url = jsonRetorno.boleto_url.toString();
        this.linhaDigitavel = jsonRetorno.boleto_linha_digitavel.toString();
        this.vencimento = jsonRetorno.boleto_vencimento.toString();
        this.valor = jsonRetorno.boleto_valor.toString();
        this.status = this.linhaDigitavel.length > 0 ? 'sucesso' : 'erro';
      }
    ).catch((param: any) => {
      console.log(param);
      this.limparFormaPagamento();
    });
  }

  erro(msg: string) {
    this.erroGeral('Erro ao consultar informações', msg);
  }

  erroGeral(titulo: string, msg: string) {
    Swal.fire({
      type: 'error',
      title: titulo,
      text: msg,
      showConfirmButton: true
    });
  }

  limparFormaPagamento() {
    if (this.empresaService.config && this.empresaService.config.apresentarCartao) {
      this.formaPagamento = FormaPagamento.CARTAO;
    } else {
      this.formaPagamento = FormaPagamento.NENHUMA;
    }
  }

  obterPixelMeta(): string {
    if (this.empresaService && this.empresaService.config && this.empresaService.config.pixelId) {
      return this.empresaService.config.pixelId;
    }
    return null;
  }

  obterTokenApiConversaoMeta(): string {
    if (this.empresaService && this.empresaService.config && this.empresaService.config.tokenApiConversao) {
      return this.empresaService.config.tokenApiConversao;
    }
    return null;
  }

  clicouPactoPayComunicacao() {
    try {
      if (this.negociacaoService.chave &&
        this.negociacaoService.pactoPayComunicacao &&
        this.negociacaoService.pactoPayComunicacao > 0) {
        this.alunoService.clicouPactoPayComunicacao(this.negociacaoService.chave, this.negociacaoService.pactoPayComunicacao);
      }
    } catch (e) {
      console.log(e);
    }
  }

  isLinkPagamentoPactoPayComunicacao(): boolean {
    if (this.negociacaoService && this.negociacaoService.chave &&
      this.negociacaoService.pactoPayComunicacao &&
      this.negociacaoService.pactoPayComunicacao > 0) {
      return true;
    }
  }
}
