@import "../../../assets/css/variaveis";

.header-consulta-clientes {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 16px 64px;
  gap: 18px;
  position: absolute;
  width: 1920px;
  height: 100px;
  left: 0;
  top: 0;
  background: #FAFAFA;
}

.title-consulta-clientes {
  /* Home */
  width: 181px;
  height: 20px;
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 100%;
  /* identical to box height, or 20px */
  text-align: center;
  letter-spacing: -0.019em;
  color: $preto-pri;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.container-consulta-clientes {
  min-width: 100%;
  display: flex;
  justify-content: center;
}

.box-middle-consulta-clientes {
  margin-top: 167px;
  display: flex;
  flex-direction: column;
  min-width: 700px;
  max-width: 700px;

  .titulo {
    font-weight: 700;
    font-size: 32px;
    color: $preto-pri;
    text-align: center;
  }

  .form-consultar-cliente {
    margin-top: 68px;

    font-weight: 600;
    font-size: 16px;

    color: $cinza05;
  }
}

.btn-consultar {
  width: 100%;
  padding: 5px;
  font-weight: bold;
  border-radius: 4px;
  color: $branco;
  cursor: pointer;
  border: none;
  height: 30px;
  margin-top: 32px;
}

.pnl-validacao {
  margin-top: 64px;
  text-align: center;

  .nao-autorizado {
    display: flex;
    flex-direction: column;
    font-weight: bold;

    i {
      color: #F72500;
      font-size: 144px;
    }
  }

  .autorizado {
    display: flex;
    flex-direction: column;
    font-weight: bold;

    i {
      color: #05E105;
      font-size: 132px;
    }
  }

  .nao-encontrado {
    display: flex;
    flex-direction: column;
    font-weight: bold;

    i {
      color: #B4B7BB;
      font-size: 144px;
    }
  }
}
