import {AfterViewInit, Component, ElementRef, OnInit} from '@angular/core';
import {Title} from '@angular/platform-browser';
import {ActivatedRoute} from '@angular/router';
import {FormGroup} from '@angular/forms';
import {TranslateService} from '@ngx-translate/core';
import {AcessoService} from '@base-core/acesso/acesso.service';
import {AutorizacaoAcesso, TipoAutorizacao} from '@base-core/acesso/autorizacaoAcesso.model';

@Component({
  selector: 'pacto-autorizacoes',
  templateUrl: './autorizacoes.component.html',
  styleUrls: ['./autorizacoes.component.scss']
})
export class AutorizacoesComponent implements AfterViewInit, OnInit {

  chave: string;
  codunidade: string;
  formGroup: FormGroup = new FormGroup({});
  situacaoConsulta: string;
  msgColetor: string;
  autorizacaoAcesso: AutorizacaoAcesso;


  constructor(private route: ActivatedRoute,
              private titleService: Title,
              private elementRef: ElementRef,
              private translateService: TranslateService,
              private acessoService: AcessoService) {

    translateService.addLangs(['ptbr', 'en']);
    translateService.setDefaultLang('ptbr');

    this.titleService.setTitle('Consulta de clientes');

    this.route.queryParams.subscribe(params => {
      if (params['k'] && params['un']) {
        this.chave = params['k'];
        this.codunidade = params['un'];
        window.localStorage.setItem('chave', params['k']);
        window.localStorage.setItem('unidade', params['un']);
      } else {
        this.chave = window.localStorage.getItem('chave');
        this.codunidade = window.localStorage.getItem('unidade');
      }
    });
  }

  ngOnInit() {
  }

  ngAfterViewInit() {
    this.elementRef.nativeElement.ownerDocument.body.style.backgroundColor = '#EFF2F7';
  }

  consultarCliente() {
    this.acessoService
      .findAutorizacaoByCpfOrTelefone(this.chave, this.formGroup.get('nome').value)
      .subscribe(data => {
        if (data == null) {
          this.situacaoConsulta = TipoAutorizacao.NAO_ENCONTRADO;
          return;
        }
        this.autorizacaoAcesso = data;

        this.acessoService
          .accessValidation(this.chave, this.autorizacaoAcesso.codigoAutorizacao)
          .subscribe((accessData: any) => {
            const returnAccess = accessData.content;
            const jsonReturn = JSON.parse(returnAccess);

            const validationReturn = jsonReturn.sucesso;
            const jsonValidation = JSON.parse(validationReturn);
            this.situacaoConsulta = (jsonValidation.situacaoAcesso === 'RV_LIBACESSOAUTORIZADO') ?
              TipoAutorizacao.AUTORIZADO :
              TipoAutorizacao.NAO_AUTORIZADO;
            this.msgColetor = jsonValidation.msgColetor;
          });

      });
  }
}
