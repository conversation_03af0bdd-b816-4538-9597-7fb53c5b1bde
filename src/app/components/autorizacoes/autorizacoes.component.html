<div class="header-consulta-clientes">
  <div class="title-consulta-clientes">Consulta de clientes</div>
</div>

<div class="container-consulta-clientes">
  <div class="box-middle-consulta-clientes">
    <div class="titulo">Consultar Cliente</div>

    <div class="form-consultar-cliente">
      <pacto-input
        idinput="idbusca"
        [label]="'consulta-cliente.consultar-por'|translate"
        [name]="'nome'"
        [pactoFormGroup]="formGroup"
        [mensagem]="'global.campoobrigatorio'|translate"
      ></pacto-input>


      <button class="btn-consultar"
              [style.background-color]="'#0380E3'"
              [style.border-color]="'#0380E3'"
              (click)="consultarCliente()">
        {{"consulta-cliente.consultar"|translate}}
      </button>
    </div>


    <div class="pnl-validacao">
      <div class="nao-autorizado" *ngIf="situacaoConsulta == 'NAO_AUTORIZADO'">
        <i class="pct-slash"></i>
        <span>Cliente sem autorização!</span>
        <span>{{msgColetor}}</span>
      </div>

      <div class="autorizado" *ngIf="situacaoConsulta == 'AUTORIZADO'">
        <i class="tem pct-check-circle"></i>
        <span>Cliente autorizado com sucesso!</span>
      </div>

      <div class="nao-encontrado" *ngIf="situacaoConsulta == 'NAO_ENCONTRADO'">
        <i class="tem pct-user-x"></i>
        <span>Cliente não encontrado!</span>
      </div>
    </div>
  </div>


</div>

