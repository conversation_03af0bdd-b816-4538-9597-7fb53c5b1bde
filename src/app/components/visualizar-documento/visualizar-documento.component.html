<div class="link" >
  <i (click)="marcar()" *ngIf="getConfig() && getMostrarMarcado() && !getMarcado()" class="pct-square checkTermo" id="{{idinput}}" [style.color]="getConfig().cor"></i>
  <i (click)="marcar()" *ngIf="getConfig() && getMostrarMarcado() && getMarcado()" class="pct-check-square checkTermo" [style.color]="getConfig().cor"></i>
  <i *ngIf="getConfig() && !getMostrarMarcado()" (click)="abrir()" class="pct-file-text" [style.color]="getConfig().cor"></i>
  <a *ngIf="getConfig() && !getMostrarMarcado()" (click)="abrir()"> {{textoLink}}</a>
  <a *ngIf="getConfig() && getMostrarMarcado()" (click)="marcar()"> {{textoLink}}</a>
  <a *ngIf="getConfig() && getMostrarMarcado()" (click)="abrir()" id="lnkLerContratoCmp" [style.color]="getConfig().cor"> ({{'visualizar-documento.clique-para-ler'|translate}})</a>
</div>


<div class="layer" *ngIf="aberto">
  <div class="modal" [ngClass]="{'inside' : inside}">
    <div class="fechar">
      <a (click)="fechar()" *ngIf="getConfig()" [style.color]="getConfig().cor"
         [style.border-color]="getConfig().cor">
        <i class="pct-x-circle"></i>
      </a>
    </div>

    <div class="termo" [innerHTML]="documento">

    </div>

    <div class="container-voltar">
      <a (click)="fechar()" id="lnkOkContratoCmp" *ngIf="getConfig()" [style.color]="getConfig().cor" [style.border-color]="getConfig().cor">
        OK
      </a>
    </div>
  </div>
</div>
