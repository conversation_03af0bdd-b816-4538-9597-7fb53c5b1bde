@import "../../../assets/css/variaveis";
.link{
  cursor: pointer;
}
.container-voltar a{
  margin-top: 10px;
  padding: 10px;
  margin-bottom: 5px;
  display: inline-block;
  text-decoration: none;
  border-radius: 50px;
  cursor: pointer;
  border: solid 1px $bordacinza;
}

.modal{
  z-index: 9999;
  color: $textoescuro;
  text-align: center;
  border-radius: 5px;
  border: $bordacinza 1px solid;
  background-color: #ffffff;
  width: 75%;
  font-size: 18px;
  margin: auto;
  max-height: calc(98vh - 20px);;
  height: auto;
  padding: 10px;
  position: relative;
  top: 50%;
  overflow: auto;
  transform: translateY(-50%);
}
.layer{
  z-index: 9999;
  display: block;
  background-color: rgba(0,0,0,.5);
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
}
.fechar{
  text-align: right;
  display: block;
  font-size: 35px;
  height: 25px;
}
.termo{
  text-align: left;
  padding: 40px;
  max-height: 70vh;
  overflow: auto;
  margin-top: 3vh;
}
@media only screen and (max-width: 748px) {
  .termo{
    padding: 20px;
  }
  .layer{
    position: fixed;
    top: 0;
    left: 0;
  }
  .modal{
    width: 98%;
  }
  .modal.inside{
    margin-top: -5vh;
    max-height: 90vh;
    margin-left: -2%;
  }
}
