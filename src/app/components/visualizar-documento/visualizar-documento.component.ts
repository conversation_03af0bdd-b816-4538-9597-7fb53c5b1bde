import {Component, Input, OnInit} from '@angular/core';
import {PlanoService} from '@base-core/plano/plano.service';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {Config} from '@base-core/empresa/config.model';
import Swal from "sweetalert2";
import {TranslateService} from '@ngx-translate/core';

@Component({
  selector: 'pacto-visualizar-documento',
  templateUrl: './visualizar-documento.component.html',
  styleUrls: ['./visualizar-documento.component.scss']
})
export class VisualizarDocumentoComponent implements OnInit {
  @Input() idinput: string;
  @Input() textoLink: string;
  @Input() tipo: string;
  @Input() inside = false;
  @Input() plano: number;
  documento: string;
  aberto = false;
  constructor(private planoService: PlanoService,
                private empresaService: EmpresaService,
              private negociacaoService: NegociacaoService,
              private translateService: TranslateService) { }

  ngOnInit() {
  }
  fechar(): void {
    localStorage.setItem("contratoAberto", 'false');
    this.aberto = false;
  }
  marcar(): void {

    if ( localStorage.getItem("lido") ==  'false'){
      Swal.fire({
        type: 'error',
        title: this.translateService.instant('checkout.leitura-contrato'),
        text: this.translateService.instant('checkout.para-continuar-leia-o-contrato'),
        showConfirmButton: true
      });
      this.negociacaoService.aceito = true;
    }

    if ( this.tipo === 'aceite' && this.negociacaoService.aceito === true) {
      this.negociacaoService.aceito = false;
    } else if ( this.tipo === 'aceite' && this.negociacaoService.aceito === false) {
      this.negociacaoService.aceito = true;
    }
  }
  abrir(): void {
    localStorage.setItem("lido", 'true');
    if ( this.tipo === 'contrato' ) {
      this.aberto = true;
      localStorage.setItem("contratoAberto", 'true');
      this.planoService.obterContrato(this.negociacaoService.chave,
          this.plano,
          this.negociacaoService.codunidade,
          ).subscribe((result) => {
          this.documento = (result['return']).replace('Untitled document', '');
        }
      );
    } else if ( this.tipo === 'aceite' ) {
      this.aberto = true;
      // não consultar novamente se o documento já foi buscado
      if ( !this.documento ) {
        this.planoService.obterTermoAceite(this.negociacaoService.chave,
          this.planoService.planoSelecionado.codigo).subscribe((result) => {
            this.documento = (result['return']).replace('Untitled document', '');
          }
        );
      }
    }
  }
  getConfig(): Config {
    return this.empresaService.config;
  }
  getMostrarMarcado(): boolean {
    return this.tipo === 'aceite';
  }
  getMarcado(): boolean {
    return this.negociacaoService.aceito;
  }

}
