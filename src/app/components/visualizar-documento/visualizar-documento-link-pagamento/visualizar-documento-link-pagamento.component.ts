import {Component, Input, OnInit} from '@angular/core';
import {PlanoService} from '@base-core/plano/plano.service';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {Config} from '@base-core/empresa/config.model';

@Component({
  selector: 'app-visualizar-documento-link-pagamento',
  templateUrl: './visualizar-documento-link-pagamento.component.html',
  styleUrls: ['./visualizar-documento-link-pagamento.component.scss']
})
export class VisualizarDocumentoLinkPagamentoComponent implements OnInit {
  @Input() idinput: string;
  @Input() textoLink: string;
  @Input() tipo: string;
  @Input() inside = false;
  @Input() plano: number;
  documento: string;
  aberto = false;
  constructor(private planoService: PlanoService,
              private empresaService: EmpresaService,
              private negociacaoService: NegociacaoService) { }

  ngOnInit() {
  }
  fechar(): void {
    this.aberto = false;
  }
  marcar(): void {
    if ( this.tipo === 'aceite link' && this.negociacaoService.aceito === true) {
      this.negociacaoService.aceito = false;
    } else if ( this.tipo === 'aceite link' && this.negociacaoService.aceito === false) {
      this.negociacaoService.aceito = true;
    }
  }
  abrir(): void {
    this.aberto = true;
    // não consultar novamente se o documento já foi buscado
    if ( !this.documento ) {
      this.planoService.obterTermoAceiteLinkPag(this.negociacaoService.chave).subscribe((result) => {
          this.documento = (result['return']).replace('Untitled document', '');
        }
      );
    }
  }
  getConfig(): Config {
    return this.empresaService.config;
  }
  getMostrarMarcado(): boolean {
    return this.tipo === 'aceite link';
  }
  getMarcado(): boolean {
    return this.negociacaoService.aceito;
  }

}
