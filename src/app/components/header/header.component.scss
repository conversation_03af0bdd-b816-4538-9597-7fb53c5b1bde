@import "../../../assets/css/variaveis";
.logo-unidade {
  width: 200px;
  height: 140px;
  object-fit: contain;
}

.container-header {
  width: 100%;
  position: relative;
  margin: 0;
  border-bottom: 2px solid rgba(189, 195, 199, 0.3);
  height: auto;
  padding: 10px 0;
  box-sizing: initial;
  margin-bottom: 30px;
}

.safe {
  margin-top: 15px;
  font-size: 12px;
  img, span {
    vertical-align: middle;
    margin-left: 10px;
  }
}

.dadosunidade {
  text-align: right;
  .unidade {
    text-transform: capitalize;
    color: $textoescuro;
    display: block;
    font-size: 18px;
  }
  .info-unidade {
    margin-top: 5px;
    color: #bdc3c7;
    font-size: 16px;
  }

}


@media only screen and (max-width: 748px) {
  .container-header {
    text-align: center;
  }
  .dadosunidade {
    position: relative;
    text-align: center;
    width: 100%;
  }
  .container-header{
    height: auto;
  }
}

