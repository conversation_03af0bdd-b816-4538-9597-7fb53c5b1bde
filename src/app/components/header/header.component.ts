import {Component, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';
import {Empresa} from '@base-core/empresa/empresa.model';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {ActivatedRoute} from '@angular/router';
import {Config} from '@base-core/empresa/config.model';

@Component({
  selector: 'pacto-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent implements OnInit, OnChanges {
  @Input()
  unidade: Empresa;
  @Input()
  config: Config;

  constructor() {
  }

  ngOnChanges(changes: SimpleChanges) {
    if (this.config) {
      this.loadGoogleAnalytics(this.config.analyticsId);
      this.loadFaceBookPixel(this.config.pixelId);
      this.loadGoogleTagManager(this.config.googleTagId);
      this.loadGoogleTagManagerBody(this.config.googleTagId);
    }
  }

  ngOnInit() {
  }

  getEmpresaSelecionada(): Empresa {
    return this.unidade;
  }

  loadGoogleAnalytics(analyticsId: string): any {
    if (analyticsId) {
      if (document.getElementById('googleAnalytics_id_1') ||
        document.getElementById('googleAnalytics_id_2')) {        
        return;
      }
      const gaScript = document.createElement('script');
      gaScript.setAttribute('id', 'googleAnalytics_id_1');
      gaScript.setAttribute('async', 'true');
      gaScript.setAttribute('src', `https://www.googletagmanager.com/gtag/js?id=${ analyticsId }`);

      const gaScript2 = document.createElement('script');
      gaScript2.setAttribute('id', 'googleAnalytics_id_2');
      gaScript2.innerText = `window.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}gtag(\'js\', new Date());gtag(\'config\', \'${ analyticsId }\');`;

      document.documentElement.firstChild.appendChild(gaScript);
      document.documentElement.firstChild.appendChild(gaScript2);
    }
  }

  loadFaceBookPixel(pixelId: string): any {
    if (pixelId) {
      if (document.getElementById('pixel_facebook_id')) {
        console.log('loadFaceBookPixel >> Já carregado');
        return;
      }
      const fpScript = document.createElement('script');
      fpScript.setAttribute('id', 'pixel_facebook_id');
      fpScript.innerText = `!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en_US/fbevents.js');fbq('init', \'${ pixelId }\');fbq('track', 'PageView');`;
      document.documentElement.firstChild.appendChild(fpScript);
    }
  }

  loadGoogleTagManager(googleTagId: string): any {
    if (googleTagId) {
      if (document.getElementById('google_tag_manager_header_id')) {        
        return;
      }
      const fpScript = document.createElement('script');
      fpScript.setAttribute('id', 'google_tag_manager_header_id');
      fpScript.innerText = `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','${googleTagId}')`;
      document.documentElement.firstChild.appendChild(fpScript);
    }
  }

  loadGoogleTagManagerBody(googleTagId: string): any {
    if (googleTagId) {
      if (document.getElementById('google_tag_manager_body_id')) {        
        return;
      }
      const fpScript = document.createElement('noscript');
      fpScript.setAttribute('id', 'google_tag_manager_body_id');
      fpScript.innerText = `<iframe src="https://www.googletagmanager.com/ns.html?id=${googleTagId}" height="0" width="0" style="display:none;visibility:hidden"></iframe>`;
      document.getElementsByTagName('body')[0].appendChild(fpScript);
    }
  }
}
