import {Component, OnInit, Input, ViewChild} from '@angular/core';
import { FormControl, FormGroup, ValidatorFn } from '@angular/forms';

@Component({
  selector: 'pacto-input',
  templateUrl: './input.component.html',
  styleUrls: ['./input.component.scss']
})
export class InputComponent implements OnInit {
  @Input() idinput: string;
  @Input() name: string;
  @Input() maxlength: string;
  @Input() colorLabel: string;
  @Input() default;
  @Input() label: string;
  @Input() disabled = false;
  // https://github.com/text-mask/text-mask/blob/master/componentDocumentation.md#readme
  @Input() textMask;
  @Input() mensagem: string;
  @Input() opcoes: any[];
  @Input() labelOption: string;
  @Input() valueOption: string;
  @Input() placeholder: string;
  @Input() type = 'text';
  @Input() validators: ValidatorFn[] = [];
  @Input() pactoFormGroup: FormGroup;
  @Input() control: FormControl;
  @Input() required = false;
  @Input() showErrorOverride = false;
  @ViewChild('input') input;
  @Input() enableShowError = true;

  constructor() { }

  ngOnInit() {
    if (!this.control) {
      if (!this.pactoFormGroup.contains(this.name)) {
        this.control = new FormControl(this.default ? this.default : null, this.validators);
        this.pactoFormGroup.addControl(this.name, this.control);
      } else {
        this.control = this.pactoFormGroup.get(this.name) as FormControl;
      }
    }
  }

  get showError() {
    if (this.enableShowError) {
      if (this.showErrorOverride) {
        return true;
      } else if (this.control) {
        return !this.control.valid && this.control.touched;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  focus() {
    this.input.nativeElement.focus();
  }

  getValueOption(option: any): string {
    if (this.valueOption) {
      return option[this.valueOption];
    } else {
      return option;
    }
  }

  getLabelOption(option: any): string {
    if (this.labelOption) {
      return option[this.labelOption];
    } else {
      return option;
    }
  }
}
