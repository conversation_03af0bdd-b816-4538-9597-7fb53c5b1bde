
<div
  class="form-group"
  [ngClass]= "{
    'has-danger': showError
  }"
  >


  <label *ngIf="label"
         [style.color]="colorLabel ? colorLabel : '#2c343b'"
         class="control-label"> {{ label }} </label>

  <input *ngIf="!opcoes"
    type="{{type}}"
    #input
    [textMask]="textMask ? textMask : {mask: false}"
    class="form-control form-control-sm"
    name="{{ name ? name : 'name' }}"
    placeholder="{{placeholder}}"
    id="{{idinput}}"
    maxlength="{{maxlength}}"
    [formControl]="control"
    [attr.disabled]="disabled ? '' : null"
    [required]="required"
    [ngClass]="{
      'form-control-danger': showError
    }"
    >

  <select *ngIf="opcoes" [formControl]="control" [required]="required" style="display: inline;">
    <option *ngFor="let o of opcoes" value="{{getValueOption(o)}}">{{getLabelOption(o)}}</option>
  </select>

  <small *ngIf="showError && mensagem" class="form-control-feedback"> {{ mensagem }} </small>
</div>
