<div class="box-produto">
  <div class="box-imagens" *ngIf="produto?.imagens.length > 1">
    <app-slider>
      <div appSliderItem class="imagem"
           [style.background-image]="'url(' + img + ')'"
           *ngFor="let img of produto?.imagens"></div>
    </app-slider>
  </div>

  <div class="box-imagem" *ngIf="produto?.imagens.length == 1">
    <img class="imagemSemImagem" src="{{produto?.imagens[0]}}">
  </div>

  <div class="box-imagem" *ngIf="produto?.imagens.length == 0">
    <img class="imagemSemImagem" src="assets/images/produto-sem-imagem.png">
  </div>

  <div class="box-descricao">
    <div class="titulo">
      {{produto?.descricao}}
    </div>
    <div class="posicaoEstoque">
      {{produto?.getPosicaoAtualEstoque()}}
    </div>
    <div class="valor">
      <span class="valorR">{{this.getMoeda()}}</span>
      <span>{{produto?.valor| currency:false:''}}</span>
    </div>
    <div>
      <button class="btn-sem-estoque" *ngIf="!produto?.getEstoqueDisponivel()"
              (click)="comprar(produto)">
        {{"produtos.itemproduto.produto-sem-estoque" |translate | uppercase }}
      </button>

      <button class="btn-comprar" *ngIf="produto?.getEstoqueDisponivel()"
              [style.background-color]="getConfig()?.cor"
              [style.border-color]="getConfig()?.cor" (click)="comprar(produto)">
        {{"produtos.itemproduto.comprar" |translate | uppercase  }}
      </button>
    </div>
  </div>
</div>

