import {Component, Input, OnInit} from '@angular/core';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {Config} from '@base-core/empresa/config.model';
import {Produto} from '@base-core/produto/produto.model';
import {ProdutoService} from '@base-core/produto/produto.service';
import {FacebookPixelService} from '@base-core/analytics/facebook-pixel.service';

@Component({
  selector: 'pacto-item-produto',
  templateUrl: './itemproduto.component.html',
  styleUrls: ['./itemproduto.component.scss']
})
export class ItemprodutoComponent implements OnInit {
  @Input() produto: Produto;
  constructor(private produtoService: ProdutoService,
              private empresaService: EmpresaService,
              private  analitycsPixel: FacebookPixelService
              ) { }

  ngOnInit() {
  }

  getMoeda(): string{
    return this.empresaService.unidadeSelecionada.moeda;
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  comprar(produto: Produto): void {
    this.produtoService.adicionarProduto(produto, 1);
    this.analitycsPixel.triggerEventFacebookClicouComprarProduto({
      nomeProduto: produto.descricao, valorProduto: produto.valor
    });
  }
}
