@import "../../../../assets/css/variaveis";

:host {
  display: block
}

.box-produto {
  background: $branco;
  border-radius: 4px;
  width: 100%;
  border: 1px solid rgba(189, 195, 199, 0.5);
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
}

.box-descricao {
  text-align: center;
  padding: 15px;
  display: grid;
  grid-template-rows: 1fr 0.5fr 1fr 0.5fr;
  align-items: center;
  min-height: 175px;
}

.box-imagens {
  width: 100%;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  height: 200px;
}

.box-imagem {
  height: 200px;
  width: 100%;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  text-align: center;
  align-items: center;
  display: grid;
  justify-content: center;
}

.imagemSemImagem {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  height: 200px;
  width: 100%;
}

.imagem {
  background-position: center;
  background-size: cover;
  width: 100%;
  height: 200px;
  flex-shrink: 0;
  flex-grow: 0;
}

.titulo {
  font-size: 18px;
  font-weight: bold;
  color: $textoescuro;
}

.valor {
  text-align: center;
  font-weight: 900;
  font-size: 25px;
}

.valorR {
  font-size: 15px;
  text-align: center;
}

.posicaoEstoque {
  color: $textoclaro;
  font-size: 15px;
  text-align: center;
  font-weight: normal;
}

.btn-comprar {
  width: 100%;
  padding: 5px;
  font-weight: bold;
  border-radius: 4px;
  color: $branco;
  cursor: pointer;
  text-transform: uppercase;
  border: none;
  height: 30px;
}

.btn-sem-estoque {
  width: 100%;
  padding: 5px;
  font-weight: bold;
  background-color: $textoclaro;
  border-color: $textoclaro;
  border-radius: 4px;
  color: #ffffff;
  cursor: not-allowed;
  text-transform: uppercase;
  border: none;
}
