import {Component, OnInit} from '@angular/core';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {ActivatedRoute, Router} from '@angular/router';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {ProdutoService} from '@base-core/produto/produto.service';
import {Config} from '@base-core/empresa/config.model';
import {Empresa} from '@base-core/empresa/empresa.model';
import {PaginaVendasOnLine} from '@base-core/negociacao/acesso-pagina';

@Component({
  selector: 'pacto-produtos',
  templateUrl: './produtos.component.html',
  styleUrls: ['./produtos.component.scss']
})
export class ProdutosComponent implements OnInit {

  constructor(private negociacaoService: NegociacaoService,
              private route: ActivatedRoute,
              private produtoService: ProdutoService,
              private empresaService: EmpresaService,
              private router: Router) {

    const currentUrl = window.location.href;
    // Split na URL usando 'utm_data' como referência
    const splitUrl = currentUrl.split('utm_data');
    const urlPossuiUtmDataNaUrl = splitUrl.length > 1;

    if (urlPossuiUtmDataNaUrl) {
      const part2 = 'utm_data' + splitUrl[1];
      this.negociacaoService.utm_data = part2;
    }

    this.route.queryParams.subscribe(params => {
      if (params['k'] && params['un']) {
        this.negociacaoService.chave = params['k'];
        this.negociacaoService.codunidade = params['un'];
        window.localStorage.setItem('chave', params['k']);
        window.localStorage.setItem('unidade', params['un']);
      } else {
        this.negociacaoService.chave = window.localStorage.getItem('chave');
        this.negociacaoService.codunidade = window.localStorage.getItem('unidade');
      }
      if (params['ct']) {
        window.localStorage.setItem('categoriaProdutos', params['ct']);
        this.negociacaoService.categoriaProdutos = Number(params['ct']);
      }
      if (params['evento']) {
        this.negociacaoService.codigoEvento = Number(params['evento']);
      }
      if (params['us']) {
        this.negociacaoService.usuarioResponsavel = params['us'];
        window.localStorage.setItem('usuario', params['us']);
      } else {
        window.localStorage.removeItem('usuario');
      }
    });
  }

  ngOnInit() {
    this.loadUnidade();
    this.negociacaoService.registrarAcessoPagina(PaginaVendasOnLine[PaginaVendasOnLine.PRODUTO], this.router.url);
    localStorage.removeItem('limparDadosCheckout');
  }

  loadUnidade(): void {
    if (!this.empresaService.unidadeSelecionada) {
      this.empresaService.obterEmpresa(
        this.negociacaoService.chave,
        this.negociacaoService.codunidade)
        .subscribe(data => this.empresaService.unidadeSelecionada = data);

      this.empresaService.obterConfigs(this.negociacaoService.chave,
        this.negociacaoService.codunidade).subscribe(data => this.empresaService.config = data);
    }
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  getUnidadeSelecionada(): Empresa {
    return this.empresaService.unidadeSelecionada;
  }
}
