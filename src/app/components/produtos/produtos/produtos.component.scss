@import "../../../../assets/css/variaveis";

.row {
  padding: 0;
  border: 0;
  margin: 0;
}

.col-md-2 {
  padding: 0;
  border: 0;
  margin: 0;
}

.col-md-6 {
  padding: 0;
  border: 0;
  margin: 0;
}

.col-md-8 {
  padding: 0;
  border: 0;
  margin: 0;
}

h1 {
  font-size: 30px;
  color: #2c343b;
  font-weight: normal;
}

h4 {
  font-size: 16px;
  color: $textoclaro;
}

.selectCategoria {
  float: right;
}

label {
  font-size: 16px;
  color: #2c343b;
  display: block;
}
input, select{
  padding-left: 15px;
  padding-right: 5px;
  display: block;
  width: 100%;
  height: 40px;
  border-radius: 4px;
  box-shadow: 0 10px 15px 0 rgba(223, 223, 223, 0.5);
  border: solid 1px #bdc3c7;
  background-color: $branco;
  //margin-top: 8.5px;
  font-size: 14px;
}
select{
  min-width: 100px;
  width: auto;
}
input:focus{
  border-color: #008dfc;
}
::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-size: 16px;
  color: #bdc3c7;
  margin-left: 15px;
}
.form-control-danger{
  border-color: #ff8971;
}
.form-control-feedback{
  width: 100%;
  text-align: right;
  color: #ff8971;
}

.panelLabelProdutos {
  width: 35%;
}

.panelCategoria {
  padding-top: 20px;
  width: 65%;
  float: right;
}
