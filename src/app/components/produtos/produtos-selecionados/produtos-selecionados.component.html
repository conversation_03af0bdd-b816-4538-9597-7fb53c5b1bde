<div class="caixa-produtos" *ngIf="getConfig() && getProdutosSelecionados().length > 0">
  <div>
    <div class="caixa-produtos-titulo">
      <span class="titulo">{{"checkout.produtos-selecionados"|translate}}</span>
    </div>
    <div class="caixa-produtos-interno">
      <div>
        <table class="tableProdutos">
          <thead class="spanProduto">
          <tr>
            <th>
              <span>{{"checkout.produto"|translate}}</span>
            </th>
            <th>
              <span>{{"checkout.preco"|translate}}</span>
            </th>
            <th>
              <span>{{"checkout.qtd"|translate}}</span>
            </th>
            <th>
              <span>{{"checkout.total"|translate}}</span>
            </th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let vendaProduto of getProdutosSelecionados()">
            <td><span>{{vendaProduto.descricao}}</span></td>
            <td><span>{{vendaProduto.valorUnitario| currency:this.getMoeda():'symbol'}}</span></td>
            <td><span>{{vendaProduto.qtd}}</span></td>
            <td><span>{{vendaProduto.getValorTotal()| currency:this.getMoeda():'symbol'}}</span></td>
          </tr>
          </tbody>
        </table>
      </div>
      <div class="divTotalProdutos">
        <span class="totalProdutos">{{"checkout.valor-produtos"|translate}}:</span>
        <span class="totalProdutosValor"
              [style.color]="getConfig().cor">{{getValorTotalProdutosSelecionados()| currency:this.getMoeda():'symbol'}}</span>
      </div>
    </div>
  </div>
</div>
