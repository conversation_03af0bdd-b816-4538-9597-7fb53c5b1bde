@import "../../../../assets/css/variaveis";

.caixa-produtos {
  margin-top: 15px;
  background: $branco;
  border: 1px solid rgba(189, 195, 199, 0.5);
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
}

.caixa-produtos-titulo {
  padding: 0 25px 0 25px;
}

.caixa-produtos-interno {
  padding: 0 25px 25px 10px;
}

.nome-unidade {
  margin-left: -20%;
  display: block;
  font-size: 24px;
}

.endereco {
  width: 88%;
  margin-top: 24px;
  margin-left: 7%;
  padding-top: 15px;
  border-top: solid 2px rgba(189, 195, 199, 0.3);
  font-size: 16px;
  color: rgba(44, 52, 59, 0.5);
}

.selecionou {
  margin-left: -20%;
  color: rgba(44, 52, 59, 0.5);
  font-size: 16px;
}

.tableProdutos {
  width: 100%;
  text-align: right;
}

.titulo {
  text-align: center;
  display: block;
  font-weight: 300;
  color: $textoclaro;
  border-bottom: solid 2px rgba(189, 195, 199, 0.3);
  margin-bottom: 5px;
  padding: 25px 25px 10px 25px;
}

.spanProduto {
  color: $textoclaro;
}

.btn-remover-produto {
  cursor: pointer;
}

.divTotalProdutos {
  font-size: 20px;
  width: 100%;
  text-align: right;
  padding-top: 5px;
}

.totalProdutos {
  color: $textoclaro;
}

.totalProdutosValor {
  font-size: 20px;
  font-weight: bold;
}

.btnAlterarQtd{
  cursor: pointer;
  color: $textoescuro;
}
