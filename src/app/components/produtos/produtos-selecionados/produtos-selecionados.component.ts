import {Component, Input, OnInit} from '@angular/core';
import {FormGroup} from '@angular/forms';
import {Router} from '@angular/router';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {Config} from '@base-core/empresa/config.model';
import {VendaProduto} from '@base-core/produto/produto.model';
import {ProdutoService} from '@base-core/produto/produto.service';

@Component({
  selector: 'pacto-produtos-selecionados',
  templateUrl: './produtos-selecionados.component.html',
  styleUrls: ['./produtos-selecionados.component.scss']
})
export class ProdutosSelecionadosComponent implements OnInit {
  @Input() formGroup: FormGroup;
  router: string;

  constructor(private _router: Router,
              private empresaService: EmpresaService,
              private produtoService: ProdutoService) {
    this.router = _router.url;
  }

  ngOnInit() {
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  getMoeda():string{
    return this.empresaService.unidadeSelecionada.moeda;
  }

  getProdutosSelecionados(): Array<VendaProduto> {
    return this.produtoService.produtosSelecionados;
  }

  getValorTotalProdutosSelecionados(): number {
    return this.produtoService.getValorTotalProdutosSelecionados();
  }

  removerProduto(produto: VendaProduto) {
    this.produtoService.removerProduto(produto);
  }

  adicionarQtdProduto(produto: VendaProduto) {
    produto.qtd = (produto.qtd + 1);
    this.produtoService.alterarQtdProduto(produto);
  }

  removerQtdProduto(produto: VendaProduto) {
    produto.qtd = (produto.qtd - 1);
    if (produto.qtd === 0) {
      this.removerProduto(produto);
    }
    this.produtoService.alterarQtdProduto(produto);
  }
}
