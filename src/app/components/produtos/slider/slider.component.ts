import {AfterContentInit, Component, Content<PERSON>hil<PERSON>n, Element<PERSON><PERSON>, QueryList, ViewChild} from '@angular/core';
import {SliderItemDirective} from './slider-item.directive';
import {Config} from '@base-core/empresa/config.model';
import {EmpresaService} from '@base-core/empresa/empresa.service';

@Component({
  selector: 'app-slider',
  templateUrl: './slider.component.html',
  styleUrls: ['./slider.component.scss']
})
export class SliderComponent implements AfterContentInit {

  constructor(private empresaService: EmpresaService) {
  }

  @ContentChildren(SliderItemDirective, {read: ElementRef}) items
    : QueryList<ElementRef<HTMLDivElement>>;
  @ViewChild('slides') slidesContainer: ElementRef<HTMLDivElement>;

  private slidesIndex = 0;

  get currentItem(): ElementRef<HTMLDivElement> {
    return this.items.find((item, index) => index === this.slidesIndex);
  }

  ngAfterContentInit() {
    // console.log('items', this.items);
  }

  onClickLeft() {
    this.slidesContainer.nativeElement.scrollLeft -= this.currentItem.nativeElement.offsetWidth;
    if (this.slidesIndex > 0) {
      this.slidesIndex--;
    }
  }

  onClickRight() {
    this.slidesContainer.nativeElement.scrollLeft += this.currentItem.nativeElement.offsetWidth;
    if (this.slidesIndex < this.items.length - 1) {
      this.slidesIndex++;
    } else {
      this.slidesIndex = 0;
      this.slidesContainer.nativeElement.scrollLeft = 0;
    }
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  getApresentaMudarImagem() {
    return this.items.length > 1;
  }
}
