<div class="slider-container">
  <div class="slides" #slides>
    <ng-content select="[appSliderItem]" #sliderItem></ng-content>
  </div>
  <div class="controls" *ngIf="getApresentaMudarImagem()">
    <div class="panelImagemLeft">
      <i class="pct-chevron-left btnImagem" [style.color]="getConfig()?.cor"
         (click)="onClickLeft()"></i>
    </div>
    <div class="panelImagemRight">
      <i class="pct-chevron-right btnImagem" [style.color]="getConfig()?.cor"
         (click)="onClickRight()"></i>
    </div>
  </div>
</div>
