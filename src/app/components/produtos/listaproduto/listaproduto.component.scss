@import "../../../../assets/css/variaveis";

.caixa-produtos {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 30px;
  padding-bottom: $alturafooter + 15px;
  width: 100%;
}

@media only screen and (max-width: 748px) {
  .caixa-produtos {
    padding-bottom: 60px;
  }
}


@media(max-width: 1375px) {
  .caixa-produtos {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

@media(max-width: 1010px) {
  .caixa-produtos {
    grid-template-columns: 1fr 1fr;
  }
}

@media(max-width: 600px) {
  .caixa-produtos {
    grid-template-columns: 1fr;
  }
}


.row {
  padding: 0;
  border: 0;
  margin: 0;
}

.col-md-2 {
  padding: 0;
  border: 0;
  margin: 0;
}

.col-md-6 {
  padding: 0;
  border: 0;
  margin: 0;
}

.col-md-8 {
  padding: 0;
  border: 0;
  margin: 0;
}

.col-md-12 {
  padding: 0;
  border: 0;
  margin: 0;
}

.panelProdutos {
}

.panelTopoProdutos {
  display: grid;
  grid-template-columns: 0.5fr 1.5fr;
  align-items: center;
}

.panelLabelProdutos {
  text-align: left;
}

.panelCategoria {
  text-align: right;
}

.selectCategoria {
  float: right;
}

label {
  font-size: 16px;
  color: #2c343b;
  display: block;
}

input, select {
  padding-left: 15px;
  padding-right: 5px;
  display: block;
  width: 100%;
  height: 40px;
  border-radius: 4px;
  box-shadow: 0 10px 15px 0 rgba(223, 223, 223, 0.5);
  border: solid 1px #bdc3c7;
  background-color: $branco;
  //margin-top: 8.5px;
  font-size: 14px;
}

select {
  min-width: 100px;
  width: auto;
}

input:focus {
  border-color: #008dfc;
}

::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-size: 16px;
  color: #bdc3c7;
  margin-left: 15px;
}

.form-control-danger {
  border-color: #ff8971;
}

.form-control-feedback {
  width: 100%;
  text-align: right;
  color: #ff8971;
}

.boxListaProdutos {
  width: 100%;
  padding-bottom: $alturafooter;
}

h1 {
  font-size: 30px;
  color: #2c343b;
  font-weight: normal;
}

h4 {
  font-size: 16px;
  color: #bdc3c7;
}

.resumo {
  font-size: 24px;
  color: #bdc3c7;
}

@media only screen and (max-width: 748px) {
  .container {
    width: 100%;
  }
}

h1 {
  font-size: 30px;
  color: #2c343b;
  font-weight: normal;
}

h4 {
  font-size: 16px;
  color: #bdc3c7;
}
