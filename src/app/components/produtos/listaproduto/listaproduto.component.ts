import {Component, Input, OnInit} from '@angular/core';
import {ProdutoService} from '@base-core/produto/produto.service';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {Categoria, Produto} from '@base-core/produto/produto.model';
import {FormControl, FormGroup} from '@angular/forms';

@Component({
  selector: 'pacto-lista-produto',
  templateUrl: './listaproduto.component.html',
  styleUrls: ['./listaproduto.component.scss']
})
export class ListaprodutoComponent implements OnInit {
  categorias: Array<Categoria> = [];
  formGroup: FormGroup = new FormGroup({
    categoriaSelected: new FormControl(0)
  });

  constructor(private produtoService: ProdutoService,
              private negociacaoService: NegociacaoService) {
  }

  ngOnInit() {
    this.loadCategorias();
    this.loadProdutos();
  }

  getProdutos(): Array<Produto> {
    return this.produtoService.produtos;
  }

  atualizarProdutos(categoria) {
    this.produtoService.obterProdutos(this.negociacaoService.chave,
      this.negociacaoService.codunidade, categoria).subscribe(data => {
      this.produtoService.produtos = data;
      if (categoria === 0) {
        this.produtoService.todosProdutos = data;
      }
    });
  }

  loadCategorias(): void {
    if (this.produtoService.categorias.length > 0) {
      this.categorias = this.produtoService.categorias;
    } else {
      this.produtoService.obterCategorias(this.negociacaoService.chave,
        this.negociacaoService.codunidade).subscribe(data => {
          if (this.negociacaoService.categoriaProdutos != null && this.negociacaoService.categoriaProdutos > 0) {
            this.categorias = [];
            data.forEach(categoria => {
              if (categoria.codigo === this.negociacaoService.categoriaProdutos) {
                this.categorias.push(categoria);
              }
            });
          } else {
            this.categorias = data;
          }
          this.produtoService.categorias = this.categorias;
      });
    }
  }

  loadProdutos(): void {
    this.atualizarProdutos(this.negociacaoService.categoriaProdutos != null ? this.negociacaoService.categoriaProdutos : 0);
  }

  getCategorias(): Array<Categoria> {
    return this.produtoService.categorias;
  }

  changeCategoria() {
    this.produtoService.obterProdutos(this.negociacaoService.chave,
      this.negociacaoService.codunidade, this.formGroup.get('categoriaSelected').value).subscribe(data => {
      this.produtoService.produtos = data;
    });
  }
}
