<div class="panelProdutos">
  <div class="panelTopoProdutos">
    <div class="panelLabelProdutos">
      <h1>{{"produtos.listaproduto.produtos"|translate}}</h1>
    </div>
    <div class="panelCategoria" *ngIf="getCategorias()?.length > 1">
      <select [formControl]="formGroup.get('categoriaSelected')" (change)="changeCategoria()"
              class="selectCategoria">
        <option value="0">({{"produtos.listaproduto.categoria"|translate}})</option>
        <option value="{{ categoria.codigo }}"
                *ngFor="let categoria of getCategorias()">{{ categoria.descricao }}</option>
      </select>
    </div>
  </div>
  <div class="caixa-produtos">
    <pacto-item-produto *ngFor="let produto of getProdutos()" class="item" [produto]="produto"></pacto-item-produto>
  </div>
</div>
