<div class="container">
  <pacto-header [config]="getConfig()" [unidade]="getUnidadeSelecionada()"></pacto-header>
  <pacto-item-plano *ngIf="getPlanoSelecionado()" [plano]="getPlanoSelecionado()" [apenasDetalhar]="true">
  </pacto-item-plano>
  <pacto-produto-diaria *ngIf="getProdutosSelecionados()?.length > 0 && !leitura"></pacto-produto-diaria>
  <div *ngIf="getPlanoSelecionado() &&  getPlanoSelecionado() != null " class="aviso-nr-creditos">
    <span>
      <span>{{"aula.aulas.este-plano-possui"|translate}}</span>
      <span class="qtd-creditos"
        *ngIf=" getPlanoSelecionado() != null && getPlanoSelecionado().qtdCreditoPlanoCredito> 0"
        [style.color]="getConfig().cor">{{getPlanoSelecionado().qtdCreditoPlanoCredito}}
        {{"aula.aulas.aulas"|translate}}</span>
    </span>
    <span [innerHTML]="'aula.aulas.intro'|translate">
    </span>
    <div class="asterisco">*{{"aula.aulas.consulte-a-validade-dos-creditos-com-a-academia"|translate}}.</div>
  </div>
  <h1  *ngIf="!leitura"  [style.border-bottom-color]="getConfig()?.cor" class="reserve-seu-horario">
    {{"aula.aulas.reserve-seu-horario"|translate}}</h1>
  <h2  *ngIf="leitura"  [style.border-bottom-color]="getConfig()?.cor" class="reserve-seu-horario">
    {{"aula.aulas.faca-seu-agendamento"|translate}}</h2>
  <h1  *ngIf="leitura"  [style.border-bottom-color]="getConfig()?.cor" class="reserve-seu-horario">
    {{"aula.aulas.aulas-leitura"|translate}}</h1>

  <div  class="agenda">
    <div class="lateral-calendario">
      <pacto-calendario (change)="loadPeriodos()"></pacto-calendario>
      <div *ngIf="!leitura" class="aulas-selecionadas">
        <span class="content-selecionadas"><span>{{"aula.aulas.aulas-reservadas"|translate}}</span>
          <span class="nr-selecionadas"
            [style.color]="aulasSelecionadas > 0 ? getConfig().cor : ''">{{aulasSelecionadas}}<ng-container
              *ngIf="getPlanoSelecionado()">/{{getPlanoSelecionado().qtdCreditoPlanoCredito}}</ng-container></span>
          <div class="visualize" *ngIf="aulasSelecionadas > 0">
            {{"aula.aulas.visualize-abaixo-a-lista-de-aulas-selecionadas"|translate}}</div>
          <i *ngIf="aulasSelecionadas > 0" class="pct-chevrons-down" (click)="scroll(marcadas)"></i>
        </span>
      </div>
    </div>
    <div>
      <h3>{{dia | date: 'EEEE\',\' dd \'de\' MMMM':'UTC-3'}}</h3>
      <div class="container-periodos">
        <div class="periodos">
          <pacto-periodo-dia [leitura]="leitura" [linkVisitante]="linkVisitante" *ngFor="let p of periodos" [periodo]="p"></pacto-periodo-dia>
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="!leitura" #marcadas class="rodape">
    <pacto-aulas-marcadas></pacto-aulas-marcadas>
    <span class="continuar" [style.background-color]="getConfig()?.cor" routerLink="/checkout"
          *ngIf="!linkVisitante && (getPlanoSelecionado() || aulasSelecionadas > 0)">
        <span class="btn">{{(aulasSelecionadas > 0 ? "aula.aulas.continuar" : "aula.aulas.marcar-depois")|translate
          |uppercase}}</span>
        <span class="detalhes"><i class="pct-arrow-right-circle"></i> </span>
      </span>
    <!--BTN LINK AGENDAMENTO DE UM PRODUTO ESPECÍFICO-->
    <span class="continuar none" *ngIf="!linkVisitante && getProdutosSelecionados().length > 0 && aulasSelecionadas == 0">
        <span class="btn">{{"aula.aulas.continuar" |translate |uppercase}}</span>
        <span class="detalhes"><i class="pct-arrow-right-circle"></i> </span>
      </span>
    <div class="opts-footer">
      <!--BTN LINK AGENDAMENTO DE UM PLANO ESPECÍFICO-->
      <div *ngIf="!linkVisitante && aulasSelecionadas > 0 && getPlanoSelecionado()" class="aulas-selecionadas direita">
        {{"aula.aulas.se-voce-ficou-com-creditos-disponiveis-nao-se-preocupe-voce-podera-marcar-depois-no-aplicativo-da-academia"|translate}}
      </div>
      <div *ngIf="!linkVisitante && aulasSelecionadas > 0 && getProdutosSelecionados().length > 0" class="aulas-selecionadas direita">
        {{"aula.aulas.as-diarias-serao-lancadas-nos-dias-das-aulas-escolhidas"|translate}} </div>
    </div>
  </div>

  <div *ngIf="linkVisitante && aulasSelecionadas > 0">
  <a class="pacto-btn-primary"
     [style.background-color]="getConfig()?.cor"
     (click)="finalizarCadastro()" id="idfinalizarAgendamento">
    {{("aula.aulas.finalizar-agendamento")|translate
    |uppercase}} >>
  </a>
    <span style="margin-top: 30px; display: block;"></span>
  </div>
</div>
