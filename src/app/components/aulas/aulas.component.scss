@import "../../../assets/css/variaveis";
.periodos{
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  pacto-periodo-dia{
    flex-grow: 1;
  }
}
.aulas-selecionadas{
  .pct-chevrons-down{
    margin-top: 10px;
    color: #000000;
    font-size: 18px;
    line-height: 18px;
    display: block;
    cursor: pointer;
  }
  &.direita{
    margin-top: 15px;
    margin-left: 7px;
    font-size: 14px;
    line-height: 17px;
    color: #6F747B;

  }
  padding: 20px;
  margin-top: 40px;
  background: #F3F3F4;
  border: 1px solid rgba(189, 195, 199, 0.5);
  box-sizing: border-box;
  box-shadow: 0px 4px 4px rgba(189, 195, 199, 0.25);
  border-radius: 4px;
  text-align: center;
  span{
    display: block;
    font-size: 18px;
    font-weight: 400;
  }
  .nr-selecionadas{
    font-size: 32px;
    font-weight: 700;
    color: #B4B7BB;
  }
  .visualize{
    font-weight: 400;
    color: #6F747B;
    font-size: 14px;
    line-height: 17px;
  }
}
.aviso-nr-creditos{
  .asterisco{
    color: #6F747B;
    font-size: 12px;
    margin-top: 8px;
  }
  span:first-child{
    span{
      display: block;
    }
    .qtd-creditos{
      font-weight: 700;
      font-size: 40px;
    }
  }
  display: grid;
  grid-template-columns: 0.7fr 3.5fr;
  background: #F3F3F4;
  border: 1px solid rgba(189, 195, 199, 0.5);
  box-sizing: border-box;
  box-shadow: 0px 4px 4px rgba(189, 195, 199, 0.25);
  border-radius: 4px;
  padding: 35px 40px;
  margin: 40px 0;
}
h1{
  border-bottom: 2px solid rgba(189, 195, 199, 0.5);
  display: initial;
  padding-bottom: 8px;
  margin-bottom: 20px;
  font-weight: bold;
  font-size: 40px;
}
.container{
  .agenda{
    margin-top: 46px;
    display: grid;
    grid-template-columns: 1fr 3fr;
    div:first-child{
      padding-right: 40px;
    }
  }
}
h3{
  font-size: 24px;
  margin: 0px 0 24px 0px;
  &:first-letter {
    text-transform: uppercase;
  }
}
.rodape{
  display: grid;
  grid-template-columns: 3fr 1fr;
  .marcadas{
    &.uma-selecionada{
      min-height: 193px;
    }
    background: #FFFFFF;
    border: 1px solid rgba(189, 195, 199, 0.5);
    box-sizing: border-box;
    box-shadow: 0px 4px 4px rgba(189, 195, 199, 0.25);
    border-radius: 4px;
    font-style: normal;
    font-weight: bold;
    font-size: 22px;
    line-height: 28px;
    color: #B4B7BB;
    padding: 20px;
    margin: 40px 0;
  }
  .continuar{
    &.none{
      cursor: not-allowed;
    }
    cursor: pointer;
    height: 68px;
    border-radius: 4px;
    margin: 40px 0px 0px 7px;
    display: grid;
    grid-template-columns: 4fr 1fr;
    color: #FFFFFF;
    background-color: rgba(173, 164, 164, 0.5);
    .btn{
      padding: 20px 0px;
      font-size: 22px;
      font-weight: 700;
      text-align: center;
    }
    .btnAge{
      padding: 7px 0px;
    }
    .detalhes{
      padding: 25px 20px;
      border-left: 1px solid rgba(173, 164, 164, 0.5);
    }
  }
}
.rodape-fixo{
  display: none;
}
@media only screen and (max-width: 748px) {
  .rodape{
    grid-template-columns: 1fr;
  }
  .container-periodos{
    width: calc(100vw - 30px);
    overflow: auto;
  }
  .periodos{
    width: 220vw;
  }
  .aviso-nr-creditos{
    padding: 35px 20px;
    grid-template-columns: 1fr;
    text-align: center;
  }
  .reserve-seu-horario{
    display: none;
  }
  .container{
    .agenda{
      grid-template-columns: 1fr;
      div:first-child{
        padding-right: 0px;
      }
    }
  }
  .lateral-calendario{
    display: grid;
    grid-template-columns: 1fr;
    margin-bottom: 50px;
  }
  .aulas-selecionadas{
    .pct-chevrons-down{
      display: inline-block;
      margin-left: 10px;
      vertical-align: top;
    }
    span{
      display: inline-block;
    }
    .nr-selecionadas{
      font-size: 20px;
      margin-left: 10px;
    }
    .content-selecionadas{
      display: inline-block;
      vertical-align: middle;
      line-height: normal;
    }
    .visualize{
      margin-top: 10px;
      vertical-align: middle;
      font-size: 12px;
      display: inline-block;
    }
  }
  .opts-footer{
    display: none;
  }
  .rodape-fixo{
    display: block;
    padding: 12px 16px 16px;
    background: #FFFFFF;
    box-shadow: 0px -4px 6px #E4E5E6;
    height: 110px;
    position: fixed;
    bottom: 0;
    left: 0;
    font-weight: normal;
    font-size: 12px;
    line-height: 15px;
    text-align: center;
    color: #6F747B;
    z-index: 99;
  }
  .continuar{
    display: block;
    color: #FFFFFF;
    border-radius: 4px;
    text-align: center;
    font-weight: bold;
    font-size: 12px;
    margin-top: 10px;
    line-height: 15px;
    padding: 15px;
    .detalhes{
      display: none;
    }
  }
}
