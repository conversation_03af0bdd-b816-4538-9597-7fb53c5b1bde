@import "../../../../assets/css/variaveis";
.caixa-periodo {
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  i{
    font-size: 30px;
    line-height: 30px;
    color: #000000;
    margin-left: 10px;
    margin-right: 20px;
  }
  .header{
    padding: 15px 15px 0;
    border-bottom: solid 1px rgba(189, 195, 199, 0.3);
    #periodo{
      font-size: 14px;
      color: rgba(44, 52, 59, 0.5);
    }
    #desc{
      font-size: 24px;
      line-height: 20px;
      color: #0380E3;
    }
    img{
      margin-left: 10px;
      margin-right: 20px;
      width: 30px;
      height: 30px;
    }
    height: 50px;
    display: flex;
  }
  box-sizing: initial;
  background-color: #ffffff;
  border: $bordacinza solid 1px;
  margin: 5px;
  padding-bottom: 12px;
  .scrollable{
    height: 52vh;
    overflow: auto;
  }
  ::-webkit-scrollbar {
    width: 4px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #888;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}
