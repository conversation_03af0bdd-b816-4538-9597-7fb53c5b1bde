<div class="caixa-periodo">
  <div class="header">
    <img *ngIf="periodo.periodo.toLowerCase() === 'manhã' || periodo.periodo.toLowerCase() === 'morning';" src="assets/images/icon/pct-morning.svg"/>
    <img *ngIf="periodo.periodo.toLowerCase() === 'tarde' || periodo.periodo.toLowerCase() === 'evening';" src="assets/images/icon/pct-afternoon.svg"/>
    <i *ngIf="periodo.periodo.toLowerCase() === 'noite' || periodo.periodo.toLowerCase() === 'at night';" class="pct-moon"></i>
    <div>
      <div id="periodo">{{ 'aula.slot.periodo-da' | translate }}</div>
      <div id="desc" [style.color]="getConfig()?.cor">{{periodo.periodo}}</div>
    </div>

  </div>
  <div class="scrollable">
    <pacto-slot *ngFor="let a of periodo.aulas" [aula]="a" [leitura]="leitura" [linkVisitante]="linkVisitante" [travar]="planoSelecionado ? travar : false">
    </pacto-slot>
  </div>

</div>

