import {Component, Input, OnInit} from '@angular/core';
import {Periodo} from '@base-core/agenda/periodo.model';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {Config} from '@base-core/empresa/config.model';
import {PlanoService} from '@base-core/plano/plano.service';
import {AulasService} from '@base-core/agenda/aulas.service';

@Component({
  selector: 'pacto-periodo-dia',
  templateUrl: './periodo-dia.component.html',
  styleUrls: ['./periodo-dia.component.scss']
})
export class PeriodoDiaComponent implements OnInit {

  @Input() periodo: Periodo;
  @Input() leitura = false;
  @Input() linkVisitante = false;

  constructor( private empresaService: EmpresaService,
               private planoService: PlanoService,
               private aulasService: AulasService
               ) { }

  ngOnInit() {
  }


  getConfig(): Config {
    return this.empresaService.config;
  }

  get planoSelecionado() {
    return this.planoService.planoSelecionado;
  }
  get travar() {
    return this.planoService.planoSelecionado.qtdCreditoPlanoCredito <= this.aulasService.aulasSelecionadas.length;
  }
}
