<div class="marcadas" [ngClass]="{'uma-selecionada': aulasSelecionadas > 0}">
  <div [style.color]="aulasSelecionadas > 0 ? getConfig()?.cor : ''" [ngClass]="{'titulo': aulasSelecionadas > 0}">
    {{"aula.aulas-marcadas.aulas-reservadas"|translate}} ({{aulasSelecionadas}}<ng-container *ngIf="getPlanoSelecionado()">
      /{{getPlanoSelecionado().qtdCreditoPlanoCredito}}</ng-container>)
    <button *ngIf="!editando" class="btn-limpar" routerLink="/agenda-aulas">
      <i class="pct-arrow-left-circle"></i> {{"aula.aulas-marcadas.voltar"|translate}}
    </button>
  </div>

  <div *ngIf="aulasSelecionadas > 0" class="linha-aulas titulo" [ngClass]="{'checkout': !editando}">
    <span>{{"aula.aulas-marcadas.aula"|translate}}</span>
    <span>{{"aula.aulas-marcadas.dia"|translate}}</span>
    <span>{{"aula.aulas-marcadas.horario"|translate}}</span>
    <span *ngIf="editando">{{"aula.aulas-marcadas.duracao"|translate}}</span>
    <span>{{"aula.aulas-marcadas.descricao"|translate}}</span>
    <span *ngIf="editando">{{"aula.aulas-marcadas.ocupacao"|translate}}</span>
    <span *ngIf="editando">{{"aula.aulas-marcadas.acoes"|translate}}</span>
  </div>

  <div *ngFor="let ai of aulas; let i = index;" class="linha-aulas item" [ngClass]="{'checkout': !editando}">
    <span>{{i + 1}}.</span>
    <span>{{ai.dia}}</span>
    <span>{{ai.inicio}}</span>
    <span *ngIf="editando">{{ai.duracao}} min</span>
    <span>{{ai.descricao.toLowerCase()}}</span>
    <span *ngIf="editando">{{ai.ocupacao}}/{{ai.capacidade}}</span>
    <span *ngIf="editando"><i class="pct-trash-2" (click)="removerAula(ai)"></i></span>
  </div>

  <div *ngFor="let ai of aulas; let i = index;" class="coluna-aulas" [ngClass]="{'checkout': !editando}">
    <div class="r1">
      <span>{{i + 1}}.</span>
      <span>{{ai.descricao.toLowerCase()}}</span>
      <span *ngIf="editando"><i class="pct-trash-2" (click)="removerAula(ai)"></i></span>
    </div>
    <div class="r2">
      <span>{{ai.dia}}</span>
      <span>{{ai.inicio}}</span>
      <span>{{ai.duracao}} min</span>
      <span>{{ai.ocupacao}}/{{ai.capacidade}}</span>
    </div>

  </div>
</div>
