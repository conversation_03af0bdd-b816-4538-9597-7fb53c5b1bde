import {Component, Input, OnInit} from '@angular/core';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {ActivatedRoute} from '@angular/router';
import {AulasService} from '@base-core/agenda/aulas.service';
import {PlanoService} from '@base-core/plano/plano.service';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {Plano} from '@base-core/plano/plano.model';
import {Config} from '@base-core/empresa/config.model';
import {VendaProduto} from '@base-core/produto/produto.model';
import {ProdutoService} from '@base-core/produto/produto.service';

@Component({
  selector: 'pacto-aulas-marcadas',
  templateUrl: './aulas-marcadas.component.html',
  styleUrls: ['./aulas-marcadas.component.scss']
})
export class AulasMarcadasComponent implements OnInit {

  @Input() editando = true;

  constructor(private negociacaoService: NegociacaoService,
              private route: ActivatedRoute,
              private aulasService: AulasService,
              private planoService: PlanoService,
              private produtoService: ProdutoService,
              private empresaService: EmpresaService) { }

  ngOnInit() {
  }
  atualizarProduto() {
    if (this.getProdutosSelecionados() &&  this.getProdutosSelecionados().length > 0) {
      this.getProdutosSelecionados()[0].qtd = this.aulasService.aulasSelecionadas.length;
    }
  }
  getProdutosSelecionados(): Array<VendaProduto> {
    return this.produtoService.produtosSelecionados;
  }
  get aulasSelecionadas() {
    return this.aulasService.aulasSelecionadas.length;
  }
  get aulas() {
    return this.aulasService.aulasSelecionadas;
  }
  getPlanoSelecionado(): Plano {
    return this.planoService.planoSelecionado;
  }

  getConfig(): Config {
    return this.empresaService.config;
  }
  removerAula(aula): void {
    this.aulasService.aulasSelecionadas.forEach( (item, index) => {
      if (item.codigo === aula.codigo) {
        this.aulasService.aulasSelecionadas.splice(index, 1);
      }
    });
    this.atualizarProduto();
  }
}
