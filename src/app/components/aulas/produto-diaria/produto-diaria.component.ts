import { Component, OnInit } from '@angular/core';
import {VendaProduto} from '@base-core/produto/produto.model';
import {ProdutoService} from '@base-core/produto/produto.service';
import {AulasService} from '@base-core/agenda/aulas.service';
import {EmpresaService} from "@base-core/empresa/empresa.service";

@Component({
  selector: 'pacto-produto-diaria',
  templateUrl: './produto-diaria.component.html',
  styleUrls: ['./produto-diaria.component.scss']
})
export class ProdutoDiariaComponent implements OnInit {

  constructor(private produtoService: ProdutoService,
              private aulasService: AulasService,
              private empresaService: EmpresaService) { }

  ngOnInit() {
  }
  getMoeda(){
    return this.empresaService.unidadeSelecionada.moeda;
  }
  getProdutosSelecionados(): Array<VendaProduto> {
    return this.produtoService.produtosSelecionados;
  }
  get aulasSelecionadas() {
    return this.aulasService.aulasSelecionadas.length;
  }
}
