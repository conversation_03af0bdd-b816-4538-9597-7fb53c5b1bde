<div *ngFor="let vendaProduto of getProdutosSelecionados()" class="row caixa">
  <div class="span_2-5_of_7 descplano">
    <i class="pct-package"></i>
    <span class="nome-plano">{{vendaProduto.descricao.toLowerCase()}}</span>
  </div>
  <div class="span_2_of_7">
    <div class="info-plano">
      <span class="titulo">{{"aula.produto-diaria.preco"|translate}}</span>
      <span class="valor">{{vendaProduto.valorUnitario| currency:this.getMoeda():'symbol'}}</span>
    </div>

    <div class="info-plano">
      <span class="titulo">{{"aula.produto-diaria.quantidade"|translate}}</span>
      <span class="valor">{{vendaProduto.qtd}}</span>
    </div>
  </div>

  <div class="span_2-5_of_7">
    <div class="mensalidade">
      <span class="valor">{{vendaProduto.getValorTotal()| currency:this.getMoeda():'symbol'}}</span>
    </div>
  </div>
</div>
