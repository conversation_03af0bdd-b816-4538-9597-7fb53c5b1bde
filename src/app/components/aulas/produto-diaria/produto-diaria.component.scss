@import "../../../../assets/css/variaveis";
.leiacontrato{
  margin-top: 5px;
  text-align: right;
  margin-right: 15px;
}
.caixa-plano.detalhando{
  margin-bottom: 15px;
  box-shadow: 0 2px 3px 0 $bordacinza;
  color: $textoescuro;
  font-size: 14px;
  .detalhesplano{
    display: block;
    width: calc(100% - 30px);
    border-top: solid 1px $bordacinza;
    margin-left: 15px;
    margin-top: 20px;
    padding-top: 20px;
    margin-bottom: 5px;
  }
}
.descplano{
  margin-left: 10px;
}
.caixa-plano {
  width: 100%;
  box-sizing: initial;
  padding: 20px 0px;
  padding-right: 0px;
  border: $bordacinza solid 1px;
  background-color: $branco;
  .fa {
    vertical-align: middle;
    margin-right: 15px;
  }
  margin: 0px;
  .detalhesplano{
    display: none;
  }
}

.caixa-plano:hover {
  background-color: $pale-grey-light;
}
.caixa-plano.selecionado {
  background-color: $pale-grey;
}

.nome-plano {
  text-transform: capitalize;
  font-size: 18px;
  line-height: 1;
  color: $textoescuro;
  vertical-align: middle;
}

.info-plano {
  vertical-align: middle;
  text-align: center;
  display: inline-block;
  margin-right: 15px;
  span {
    display: block;
  }
  .titulo {
    font-size: 14px;
    font-weight: 300;
    color: $textoclaro;
  }
  .valor {
    font-size: 15px;
    color: $textoescuro;
  }
}

.info-plano:last-child {
  margin-right: 0px;
}

.mensalidade {
  width: 100%;
  vertical-align: middle;
  text-align: right;
  display: inline-block;
  .valor {
    font-size: 25px;
    font-weight: 900;
  }
}

.item:first-child {
  .caixa-plano {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }
}

.item:last-child {
  .caixa-plano {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }

}

.pct-package {
  width: 32px;
  height: 32px;
  vertical-align: middle;
  margin-right: 10px;
  font-family: Pacto-Icons-Fonts;
  font-size: 32px;
  color: $textoescuro;
  vertical-align: middle;
}
.pct-chevron-down {
  font-family: Pacto-Icons-Fonts;
  color: #ffffff;
  vertical-align: middle;
}
.selecionar.selecionado {
  box-shadow: inset 0 2px 4px 0 rgba(44, 52, 59, 0.4);
}
.selecionar {
  box-shadow: 0 2px 3px 0 rgba(44, 52, 59, 0.1);
  cursor: pointer;
  width: 130px;
  color: #ffffff;
  height: 30px;
  border-radius: 4px;
  margin-left: 25px;
  display: inline-flex;
  .btn{
    text-align: center;
    font-size: 12px;
    width: 100px;
    line-height: 30px;
    vertical-align: middle;
  }
  .detalhes{
    line-height: 30px;
    width: 30px;
    vertical-align: middle;
    text-align: center;
  }
}

@media only screen and (max-width: 748px) {
  .caixa-plano.detalhando{
    .detalhesplano{
      display: none;
    }
  }

  .nome-plano{
    font-size: 18px;
  }
  .selecionar{
    margin: 0;
    margin-top: 15px;
    width: 100%;
    height: 40px;
    .btn{
      font-size: 14px;
      width: 100%;
      line-height: 40px;
    }
    .detalhes{
      display: none;
    }
  }

  .mensalidade {
    width: 95%;
    text-align: center;
    padding-top: 15px;
    border-top: $bordacinza solid 1px;
  }
  .caixa-plano{
    margin-bottom: 15px;
    padding-top: 5px;
    padding-bottom: 10px;
  }
  .pct-package {
    margin-left: 0px;
  }
  .descplano{
    margin-top: 5px;

  }
}

.inclui{
  font-weight: bold;
  display: block;
  margin: 20px 0 10px 0;
}

.tem{
  font-size: 18px;
  color: $azulclaro;
  margin-right: 5px;
}
.modalidade{
  margin-left: 15px;
  i, span{
    vertical-align: middle;
  }
}
.modalidade:first-child{
  margin-left: 0px;
}

.caixa{
  box-shadow: 0 2px 3px 0 $bordacinza;
  color: $textoescuro;
  font-size: 14px;
  min-height: 40px;
  width: 100%;
  border-top: solid 1px $bordacinza;
  margin: 30px 0;
  padding: 20px;
}
