import {NgbDatepickerI18n, NgbDateStruct} from '@ng-bootstrap/ng-bootstrap';

export class CustomDatepickerI18nPT  extends NgbDatepickerI18n {
  getWeekdayShortName(weekday: number): string {
    const weekdays: string[] = ['Dom', 'Seg', 'Ter', '<PERSON>ua', 'Qui', '<PERSON>', '<PERSON>áb'];
    return weekdays[weekday % 7];
  }

  getMonthShortName(month: number): string {
    const months: string[] = [
      'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
    ];
    return months[month - 1];
  }

  getMonthFullName(month: number): string {
    const months: string[] = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', '<PERSON><PERSON>',
      '<PERSON><PERSON>', 'Agosto', 'Setembro', 'Out<PERSON>ro', 'Novembro', 'Dezembro'
    ];
    return months[month - 1];
  }

  getDayAriaLabel(date: NgbDateStruct): string {
    return '';
  }
}
