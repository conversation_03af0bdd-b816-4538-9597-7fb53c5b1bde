import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import { NG_VALUE_ACCESSOR, FormControl } from '@angular/forms';
import {NgbCalendar, NgbDateStruct} from '@ng-bootstrap/ng-bootstrap';
import {AulasService} from '@base-core/agenda/aulas.service';

declare var moment;

@Component({
  selector: 'pacto-calendario',
  templateUrl: './calendario.component.html',
  styleUrls: ['./calendario.component.scss']
})
export class CalendarioComponent implements OnInit {
  model: NgbDateStruct;
  minDate: NgbDateStruct;
  @Input() formControl: FormControl;
  @Output() change: EventEmitter<any> = new EventEmitter<any>();
  @Input() dateFilter: (date: Date) => boolean;

  constructor(private calendar: NgbCalendar,
              private aulasService: AulasService,
              ) { }

  ngOnInit() {
    this.model = this.calendar.getToday();
    this.minDate = this.calendar.getToday();
    this.aulasService.setDia(new Date(this.model.year, this.model.month - 1, this.model.day));
  }

  calendarSelectHandler() {
    this.aulasService.setDia(new Date(this.model.year, this.model.month - 1, this.model.day));
    this.change.emit();
  }
}
