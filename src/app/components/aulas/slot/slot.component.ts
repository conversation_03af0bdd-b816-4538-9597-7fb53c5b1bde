import {Component, Input, OnInit} from '@angular/core';
import {Aula} from '@base-core/agenda/aula.model';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {Config} from '@base-core/empresa/config.model';
import {AulasService} from '@base-core/agenda/aulas.service';
import {VendaProduto} from '@base-core/produto/produto.model';
import {ProdutoService} from '@base-core/produto/produto.service';

@Component({
  selector: 'pacto-slot',
  templateUrl: './slot.component.html',
  styleUrls: ['./slot.component.scss']
})
export class SlotComponent implements OnInit {

  @Input() aula: Aula;
  @Input() travar = false;
  @Input() leitura = false;
  @Input() linkVisitante = false;

  constructor(private empresaService: EmpresaService,
              private produtoService: ProdutoService,
              private aulasService: AulasService) { }

  ngOnInit() {
  }

  toggle() {
    if (this.linkVisitante) {
      this.aulasService.aulasSelecionadas = [];
      this.aulasService.aulasSelecionadas.push(this.aula);
    } else {
      if (this.selecionado === true) {
        this.aulasService.aulasSelecionadas.forEach((item, index) => {
          if (item.codigo === this.aula.codigo) {
            this.aulasService.aulasSelecionadas.splice(index, 1);
          }
        });
      } else if (this.naoMarcar === false) {
        this.aulasService.aulasSelecionadas.push(this.aula);
      }
    }
    this.atualizarProduto();
  }

  atualizarProduto() {
     if (this.getProdutosSelecionados() &&  this.getProdutosSelecionados().length > 0) {
       this.getProdutosSelecionados()[0].qtd = this.aulasService.aulasSelecionadas.length;
     }
  }

  getProdutosSelecionados(): Array<VendaProduto> {
    return this.produtoService.produtosSelecionados;
  }
  get selecionado() {
    return this.aulasService.aulasSelecionadas.some(a => a.codigo === this.aula.codigo);
  }
  get naoMarcar() {
    return (this.travar === true && this.selecionado === false) || this.aula.ocupacao >= this.aula.capacidade || this.aula.passou;
  }
  getConfig(): Config {
    return this.empresaService.config;
  }
}
