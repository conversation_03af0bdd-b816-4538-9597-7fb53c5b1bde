<div class="slot" [ngClass]="{'selecionado': selecionado, 'travar': naoMarcar, 'leitura' : leitura}" (click)="toggle()">
  <div class="inicio col1" [style.color]="selecionado && !leitura? getConfig()?.cor : ''">{{aula.inicio}}</div>
  <div class="ocupacao col2">
    <i class="pct-bar-chart"></i> {{"aula.slot.ocupacao" |translate}}: {{aula.ocupacao}}/{{aula.capacidade}}
  </div>
  <div class="col3" *ngIf="!leitura">
    <i *ngIf="selecionado === false" class="pct-circle"></i>
    <i [style.color]="getConfig()?.cor"*ngIf="selecionado === true" class="pct-check-circle"></i>
  </div>
  <div class="duracao mtop col1">{{aula.duracao}} min</div>
  <div class="mtop col2">
    <div class="descricao">{{aula.descricao}}</div>
  </div>
</div>

