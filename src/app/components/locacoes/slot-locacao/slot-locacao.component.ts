import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Aula } from '@base-core/agenda/aula.model';
import { AulasService } from '@base-core/agenda/aulas.service';
import { Config } from '@base-core/empresa/config.model';
import { EmpresaService } from '@base-core/empresa/empresa.service';
import { AgendaDisponibilidade } from '@base-core/locacao-ambiente/agenda-disponibilidade.model';
import { LocacaoAmbienteService } from '@base-core/locacao-ambiente/locacao-ambiente.service';
import { VendaProduto } from '@base-core/produto/produto.model';
import { ProdutoService } from '@base-core/produto/produto.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'pacto-slot-locacao',
  templateUrl: './slot-locacao.component.html',
  styleUrls: ['./slot-locacao.component.scss']
})
export class SlotLocacaoComponent implements OnInit {


    @Input() agendaDisponibilidade: AgendaDisponibilidade;
    @Input() travar = false;
    @Input() leitura = false;
    @Input() linkVisitante = false;
    @Output() slotSelecionado = new EventEmitter<AgendaDisponibilidade>();

    constructor(private empresaService: EmpresaService,
                private produtoService: ProdutoService,
                private locacaoAmbienteService: LocacaoAmbienteService) { }

    ngOnInit() {
    }

    toggle() {

      if(this.travar) {
        return;
      }

      if (this.linkVisitante) {
        this.locacaoAmbienteService.locacoesSelecionadas = [];
        this.locacaoAmbienteService.locacoesSelecionadas.push(this.agendaDisponibilidade);
      } else {
        if (this.selecionado === true) {
          this.locacaoAmbienteService.locacoesSelecionadas.forEach((item, index) => {
            if (item.id === this.agendaDisponibilidade.id) {
              this.locacaoAmbienteService.locacoesSelecionadas.splice(index, 1);
            }
          });
        } else if (this.naoMarcar === false) {
          this.locacaoAmbienteService.locacoesSelecionadas.push(this.agendaDisponibilidade);
        }
      }
      this.slotSelecionado.emit(this.agendaDisponibilidade);
      this.atualizarProduto();
    }

    atualizarProduto() {
       if (this.getProdutosSelecionados() &&  this.getProdutosSelecionados().length > 0) {
         this.getProdutosSelecionados()[0].qtd = this.locacaoAmbienteService.locacoesSelecionadas.length;
       }
    }

    getProdutosSelecionados(): Array<VendaProduto> {
      return this.produtoService.produtosSelecionados;
    }
    get selecionado() {
      return this.locacaoAmbienteService.locacoesSelecionadas.some(a => a.id === this.agendaDisponibilidade.id);
    }
    get naoMarcar() {
      return (this.travar === true && this.selecionado === false);
    }
    getConfig(): Config {
      return this.empresaService.config;
    }

    openModalSaibaMais(event: MouseEvent): void {
      event.stopPropagation();
      Swal.fire({
        showCloseButton: true,
        showConfirmButton: false,
        html: `
          <div class="swal-header">
            <h2>${this.agendaDisponibilidade.ambiente}</h2>
          </div>
          <div style="margin:10px 0 10px 0; height:1px; width:100%; border:1px solid #C9CBCF;"></div>
          <div class="swal-body">
            <img src="${this.agendaDisponibilidade.urlFoto}" alt="Imagem" style="width:100%; height: auto; margin: 20px 0;">
            <p>${this.agendaDisponibilidade.descricao}</p>
          </div>
        `
      });
    }
}
