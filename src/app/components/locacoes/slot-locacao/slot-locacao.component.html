<div class="slot" [ngClass]="{'selecionado': selecionado, 'travar': naoMarcar, 'leitura' : leitura}" (click)="toggle()">
  <div class="inicio col1" [style.color]="selecionado && !leitura? getConfig()?.cor : ''">{{agendaDisponibilidade.horarioInicial}}</div>
  <div class="nome col2">
    {{agendaDisponibilidade.nome}}
  </div>
  <div class="col3" *ngIf="!leitura">
    <i *ngIf="selecionado === false" class="pct-circle"></i>
    <i [style.color]="getConfig()?.cor"*ngIf="selecionado === true" class="pct-check-circle"></i>
  </div>
  <div class="duracao col1">{{agendaDisponibilidade.tipoHorario == '0' ? agendaDisponibilidade.tempoMinimoMinutos : agendaDisponibilidade.duracaoMinutos}} min</div>
  <div class="mtop col2">
    <div class="descricao">{{agendaDisponibilidade.ambiente}}</div>
  </div>
  <a class="saiba-mais mtop" (click)="openModalSaibaMais($event)">Saiba Mais</a>
</div>

