<div class="container">
  <pacto-header [config]="getConfig()" [unidade]="getUnidadeSelecionada()"></pacto-header>
  <h1 [style.border-bottom-color]="getConfig()?.cor" class="reserve-seu-horario">{{"locacao-ambiente.selecione-uma-locacao"|translate}}</h1>
  <h2 class="periodo-do-dia-preferencia">{{"locacao-ambiente.qual-periodo-do-dia-de-sua-preferencia"|translate}}</h2>

  <div class="agenda">
    <div class="lateral-calendario">
      <pacto-calendario (change)="loadLocacoesDisponiveis()"></pacto-calendario>
      <div class="aulas-selecionadas">
        <span class="content-selecionadas"><span class="ambientes-selecionados">{{"locacao-ambiente.ambientes-selecionados"|translate}}</span>
          <span class="nr-selecionadas"
            [style.color]="locacoesSelecionadas > 0 ? getConfig().cor : ''">{{locacoesSelecionadas}}</span>
        </span>
      </div>
    </div>
    <div>
      <h3>{{dia | date: 'EEEE\',\' dd \'de\' MMMM':'UTC-3'}}</h3>
      <div class="container-periodos">
        <div class="periodos">
          <pacto-periodo-locacao *ngFor="let p of periodosLocacao" [periodoLocacao]="p" [tipoSelecionado]="tipoSelecionado" (slotSelecionado)="handleSlotSelecionado($event)">
          </pacto-periodo-locacao>
        </div>
      </div>
    </div>
  </div>
  <div #marcadas class="rodape">
    <pacto-locacoes-marcadas (locacaoRemovida)="handleRemoverLocacao($event)"></pacto-locacoes-marcadas>
  </div>

</div>
