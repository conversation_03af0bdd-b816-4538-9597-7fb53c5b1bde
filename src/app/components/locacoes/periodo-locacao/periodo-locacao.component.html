<div class="caixa-periodo">
  <div class="header">
    <img *ngIf="periodoLocacao.periodo.toLowerCase() === 'manhã' || periodoLocacao.periodo.toLowerCase() === 'morning';" src="assets/images/icon/pct-morning.svg"/>
    <img *ngIf="periodoLocacao.periodo.toLowerCase() === 'tarde' || periodoLocacao.periodo.toLowerCase() === 'evening';" src="assets/images/icon/pct-afternoon.svg"/>
    <i *ngIf="periodoLocacao.periodo.toLowerCase() === 'noite' || periodoLocacao.periodo.toLowerCase() === 'at night';" class="pct-moon"></i>
    <div>
      <div id="periodo">{{ 'locacao-ambiente.slot.periodo-da' | translate }}</div>
      <div id="desc" [style.color]="getConfig()?.cor">{{periodoLocacao.periodo}}</div>
    </div>

  </div>
  <div class="scrollable">
    <pacto-slot-locacao *ngFor="let a of periodoLocacao.lstAgendaDisponibilidade" [agendaDisponibilidade]="a" [leitura]="leitura"
      [linkVisitante]="linkVisitante" [travar]="tipoSelecionado && tipoSelecionado !== a.tipoHorario" (slotSelecionado)="onSlotSelecionado($event)">
    </pacto-slot-locacao>
  </div>

</div>

