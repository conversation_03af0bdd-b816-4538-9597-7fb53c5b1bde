import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AulasService } from '@base-core/agenda/aulas.service';
import { Periodo } from '@base-core/agenda/periodo.model';
import { Config } from '@base-core/empresa/config.model';
import { EmpresaService } from '@base-core/empresa/empresa.service';
import { AgendaDisponibilidade } from '@base-core/locacao-ambiente/agenda-disponibilidade.model';
import { PeriodoLocacao } from '@base-core/locacao-ambiente/periodo-locacao.model';
import { PlanoService } from '@base-core/plano/plano.service';

@Component({
  selector: 'pacto-periodo-locacao',
  templateUrl: './periodo-locacao.component.html',
  styleUrls: ['./periodo-locacao.component.scss']
})
export class PeriodoLocacaoComponent implements OnInit {

  @Input() periodoLocacao: PeriodoLocacao;
  @Input() leitura = false;
  @Input() linkVisitante = false;
  @Input() tipoSelecionado: string = '';
  @Output() slotSelecionado = new EventEmitter<AgendaDisponibilidade>();

  constructor(private empresaService: EmpresaService,
              private planoService: PlanoService,
              private aulasService: AulasService
            ) {}

  ngOnInit() {

  }

  getConfig(): Config {
      return this.empresaService.config;
    }

  get planoSelecionado() {
    return this.planoService.planoSelecionado;
  }
  get travar() {
    return this.planoService.planoSelecionado.qtdCreditoPlanoCredito <= this.aulasService.aulasSelecionadas.length;
  }

  onSlotSelecionado(slot: AgendaDisponibilidade) {
    this.slotSelecionado.emit(slot);
  }


}
