import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { EmpresaService } from '@base-core/empresa/empresa.service';
import { AgendaDisponibilidade } from '@base-core/locacao-ambiente/agenda-disponibilidade.model';
import { LocacaoAmbienteService } from '@base-core/locacao-ambiente/locacao-ambiente.service';
import { NegociacaoService } from '@base-core/negociacao/negociacao.service';
import {Config} from '@base-core/empresa/config.model';

@Component({
  selector: 'pacto-locacoes-marcadas',
  templateUrl: './locacoes-marcadas.component.html',
  styleUrls: ['./locacoes-marcadas.component.scss']
})
export class LocacoesMarcadasComponent implements OnInit {

  @Input() editando = true;
  @Output() locacaoRemovida = new EventEmitter<boolean>();

  constructor(private negociacaoService: NegociacaoService,
              private route: ActivatedRoute,
              private locacaoAmbienteService: LocacaoAmbienteService,
              private empresaService: EmpresaService,
              private router: Router) { }

  ngOnInit() {
  }

  get qtdLocacoesSelecionadas() {
    return this.locacaoAmbienteService.locacoesSelecionadas.length;
  }

  get locacoesSelecionadas() {
    return this.locacaoAmbienteService.locacoesSelecionadas;
  }

  diaDaSemanaAbreviado(data: string): string {
    if(!/^\d{8}$/.test(data)) {
      return "";
    }else{
      const ano = parseInt(data.substring(0,4));
      const mes = parseInt(data.substring(4,6)) - 1;
      const dia = parseInt(data.substring(6,8));

      const dataConvertida = new Date(ano, mes, dia);

      let diaSemana = [];

      if(window.localStorage.getItem('usarSistemaInternacional') == 'true'){
        diaSemana = ['Sun.', 'Mon.', 'Tue.', 'Wed.', 'Thu.', 'Fri.', 'Sat.'];
      }else{
        diaSemana = ['Dom.', 'Seg.', 'Ter.', 'Qua.', 'Qui.', 'Sex.', 'Sáb.'];
      }

      return diaSemana[dataConvertida.getDay()];
    }
  }

  removerLocacao(locacao: AgendaDisponibilidade) {
    let removidoComSucesso = false;
    this.locacoesSelecionadas.forEach((item, index) => {
      if(item.id === locacao.id) {
        this.locacoesSelecionadas.splice(index, 1);
        removidoComSucesso = true;
      }
    });
    if(removidoComSucesso) {
      this.locacaoRemovida.emit(false);
    }
  }

  removerTodos() {
    this.locacaoAmbienteService.locacoesSelecionadas = [];
    this.locacaoRemovida.emit(true);
  }

  finalizarLocacao() {
    const queryParams = {
      k: this.negociacaoService.chave,
      un: this.negociacaoService.codunidade,
      loc: 'true'
    };

    this.router.navigate(['/checkout'], { queryParams });
  }

  get moeda(): string {
    return this.empresaService.unidadeSelecionada.moeda
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

}
