@import "../../../../assets/css/variaveis";
.marcadas {
  &.uma-selecionada {
    width: 60vw;
  }
  position: relative;
  background: #FFFFFF;
  border: 1px solid rgba(189, 195, 199, 0.5);
  box-sizing: border-box;
  box-shadow: 0px 4px 4px rgba(189, 195, 199, 0.25);
  border-radius: 4px;
  font-style: normal;
  font-weight: bold;
  font-size: 22px;
  line-height: 28px;
  color: #B4B7BB;
  padding: 20px;
  margin: 40px 0;
}
.btn-limpar {
  position: absolute;
  right: 20px;
  top: 20px;
  line-height: 20px;
  padding: 5px;
  font-weight: bold;
  border-radius: 4px;
  color: $textoclaro;
  background-color: $branco;
  border-color: $branco;
  cursor: pointer;
  border: none;
}
.linha-locacoes {
  text-transform: capitalize;
  display: grid;
  grid-template-columns: 7fr 2fr 4fr 2fr 3fr 3fr 4fr;
  gap: 8px;
  &.checkout{
    grid-template-columns: 1fr 1.5fr 1fr 3fr;
  }
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 26px;

  &.titulo {
    color: #40424C;
    margin-top: 20px;
  }

  &.item {
    color: #40424C;
    padding-top: 12px;
    font-weight: 400;
    font-size: 13px;
  }

  .pct-trash-2 {
    cursor: pointer;
  }

  // Estilos para exibição vertical de produtos e valores
  .produtos-coluna,
  .valores-coluna {
    display: flex;
    flex-direction: column;
    gap: 2px;
    align-items: flex-start;
  }

  .produto-item,
  .valor-item {
    display: block;
    width: 100%;
    line-height: 1.2;
    margin-bottom: 2px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
.coluna-aulas{
  display: none;
}
.acoes-container {
  display: flex;
  flex-direction: column;
}
.btn-continuar {
  background-color: #1E60FA;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
}

.btn-limpar-tudo {
  background-color: white;
  color: #1E60FA;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.acao-wrapper-mobile {
  display: none;
}

// Media query para telas médias (tablets)
@media only screen and (max-width: 1024px) and (min-width: 749px) {
  .linha-locacoes {
    grid-template-columns: 6fr 1.5fr 3fr 1.5fr 2fr 2fr 3fr;
    gap: 4px;
    font-size: 12px;

    &.checkout{
      grid-template-columns: 1fr 1fr 1fr 2fr;
    }
  }

  .produtos-coluna,
  .valores-coluna {
    font-size: 11px;
  }
}
@media only screen and (max-width: 748px) {
  .marcadas{

    &.uma-selecionada {
      width: 95vw;
    }

    text-align: center;
    margin-bottom: 150px;
    .titulo{
      margin-bottom: 15px;
    }
  }
  .linha-locacoes{
    display: none;
  }
  .marcadas.uma-selecionada{
    min-height: auto;
  }
  .coluna-aulas{
    display: block;
    text-transform: capitalize;
    color: #51555A;
    border-top: solid 2px #F3F3F4;
    padding-top: 12px;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
    text-align: left;
    width: 87vw;
    .pct-trash-2 {
      cursor: pointer;
    }
    .r1 {
      width: 100%;
      display: grid;
      grid-template-columns: 1fr 10fr 1fr;
      span:last-child{
        text-align: right;
      }
    }
    .r2 {
      display: grid;
      margin: 5px 0;
      grid-template-columns: 1fr 5fr 2fr 3fr;
      span:last-child{
        text-align: right;
      }
    }

    // Estilos para versão mobile - produtos e valores verticais
    .produtos-coluna-mobile,
    .valores-coluna-mobile {
      display: flex;
      flex-direction: column;
      gap: 2px;
      align-items: flex-start;
    }

    .produto-item-mobile,
    .valor-item-mobile {
      display: block;
      width: 100%;
      line-height: 1.2;
      margin-bottom: 2px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .acao-wrapper-mobile {
    display: flex;
    flex-direction: column;
    margin-top: 15px;
  }

}
