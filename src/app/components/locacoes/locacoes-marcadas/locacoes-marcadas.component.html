<div class="marcadas" [ngClass]="{'uma-selecionada': qtdLocacoesSelecionadas > 0}">
  <div [style.color]="qtdLocacoesSelecionadas > 0 ? getConfig()?.cor : ''" [ngClass]="{'titulo': qtdLocacoesSelecionadas > 0}">
    <button *ngIf="!editando" class="btn-limpar" routerLink="/agenda-locacao-ambiente">
      <i class="pct-arrow-left-circle"></i> {{"locacao-ambiente.locacoes-marcadas.voltar"|translate}}
    </button>
  </div>

  <div *ngIf="qtdLocacoesSelecionadas > 0" class="linha-locacoes titulo" [ngClass]="{'checkout': !editando}">
    <span>{{"locacao-ambiente.locacoes-marcadas.locacao"|translate}}</span>
    <span>{{"locacao-ambiente.locacoes-marcadas.valor"|translate}}</span>
    <span>{{"locacao-ambiente.locacoes-marcadas.produto"|translate}}</span>
    <span>{{"locacao-ambiente.locacoes-marcadas.valor"|translate}}</span>
    <span>{{"locacao-ambiente.locacoes-marcadas.total-produto"|translate}}</span>
    <span>{{"locacao-ambiente.locacoes-marcadas.excluir"|translate}}</span>
    <span></span>
  </div>

  <div *ngFor="let locacao of locacoesSelecionadas; let i = index;" class="linha-locacoes item" [ngClass]="{'checkout': !editando}">
    <span>{{locacao.nome}} - {{locacao.ambiente}} - {{diaDaSemanaAbreviado(locacao.dia)}} {{locacao.horarioInicial}} - {{locacao.horarioFinal}}</span>
    <span>{{locacao.produtoLocacao.valor | currency : moeda : 'symbol'}}</span>

    <span>
      <ng-container *ngIf="locacao.lstProdutos && locacao.lstProdutos.length > 0; else semProdutos">
        <span *ngFor="let pr of locacao.lstProdutos">
          <span *ngIf="pr.obrigatorio">{{pr.produto.descricao}}</span>
        </span>
      </ng-container>
      <ng-template #semProdutos>
        <span>&nbsp;</span>
      </ng-template>
    </span>

    <span>
      <ng-container *ngIf="locacao.lstProdutos && locacao.lstProdutos.length > 0; else semValores">
        <span *ngFor="let pr of locacao.lstProdutos">
          <span *ngIf="pr.obrigatorio">{{pr.valor | currency : moeda : 'symbol'}}</span>
        </span>
      </ng-container>
      <ng-template #semValores>
        <span>{{0 | currency : moeda : 'symbol'}}</span>
      </ng-template>
    </span>

    <span>{{locacao.totalValorProdutosObrigatorios | currency : moeda : 'symbol'}}</span>
    <span *ngIf="editando"><i class="pct-trash-2" (click)="removerLocacao(locacao)"></i></span>
    <span *ngIf="i === 0">
      <div class="acoes-container">
        <button class="btn-continuar"
          (click)="finalizarLocacao()">
          {{ 'locacao-ambiente.locacoes-marcadas.continuar' | translate }} <i class="pct-arrow-right"></i>
        </button>

        <button class="btn-limpar-tudo"
          (click)="removerTodos()">
          {{ 'locacao-ambiente.locacoes-marcadas.limpar' | translate }} <i class="pct-trash-2"></i>
        </button>
      </div>
    </span>
  </div>

  <div *ngFor="let locacao of locacoesSelecionadas; let i = index;" class="coluna-aulas" [ngClass]="{'checkout': !editando}">
    <div class="r1">
      <span>{{i + 1}}.</span>
      <span>{{locacao.nome}} - {{locacao.ambiente}} - {{locacao.horarioInicial}} - {{locacao.produtoLocacao.valor | currency : moeda : 'symbol'}}</span>
      <span *ngIf="editando"><i class="pct-trash-2" (click)="removerLocacao(locacao)"></i></span>
    </div>
    <div class="r2">
      <span></span>
      <span *ngIf="locacao.lstProdutos && locacao.lstProdutos.length > 0">
        <span *ngFor="let pr of locacao.lstProdutos">
          <span *ngIf="pr.obrigatorio">{{pr.produto.descricao}}</span>
        </span>
      </span>

      <span *ngIf="locacao.lstProdutos && locacao.lstProdutos.length > 0">
        <span *ngFor="let pr of locacao.lstProdutos">
          <span *ngIf="pr.obrigatorio">{{pr.valor | currency : moeda : 'symbol'}}</span>
        </span>
      </span>

      <span *ngIf="locacao.lstProdutos && locacao.lstProdutos.length > 0">
        <span>{{locacao.totalValorProdutosObrigatorios | currency : moeda : 'symbol'}}</span>
      </span>
    </div>
  </div>

  <div class="acao-wrapper-mobile" *ngIf="qtdLocacoesSelecionadas > 0">
    <button class="btn-continuar"
          (click)="finalizarLocacao()">
          {{ 'locacao-ambiente.locacoes-marcadas.continuar' | translate }} <i class="pct-arrow-right"></i>
        </button>

      <button class="btn-limpar-tudo"
        (click)="removerTodos()">
        {{ 'locacao-ambiente.locacoes-marcadas.limpar' | translate }} <i class="pct-trash-2"></i>
      </button>
  </div>
</div>
