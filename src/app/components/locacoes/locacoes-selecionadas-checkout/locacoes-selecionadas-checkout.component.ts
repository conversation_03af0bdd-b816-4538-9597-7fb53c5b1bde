import { Component, Input, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { EmpresaService } from '@base-core/empresa/empresa.service';
import { Config } from '@base-core/empresa/config.model';
import { AgendaDisponibilidade } from '@base-core/locacao-ambiente/agenda-disponibilidade.model';
import { LocacaoAmbienteService } from '@base-core/locacao-ambiente/locacao-ambiente.service';

@Component({
  selector: 'pacto-locacoes-selecionadas-checkout',
  templateUrl: './locacoes-selecionadas-checkout.component.html',
  styleUrls: ['./locacoes-selecionadas-checkout.component.scss']
})
export class LocacoesSelecionadasCheckoutComponent implements OnInit {
  @Input() formGroup: FormGroup;

  constructor(
    private empresaService: EmpresaService,
    private locacaoAmbienteService: LocacaoAmbienteService
  ) { }

  ngOnInit() {
    // Carregar locações do localStorage se não estiverem no service
    if (this.locacaoAmbienteService.locacoesSelecionadas.length === 0) {
      const locacoesSalvas = window.localStorage.getItem('locacoesSelecionadas');
      if (locacoesSalvas) {
        this.locacaoAmbienteService.locacoesSelecionadas = JSON.parse(locacoesSalvas);
      }
    }
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  get locacoesSelecionadas(): AgendaDisponibilidade[] {
    return this.locacaoAmbienteService.locacoesSelecionadas;
  }

  get moeda(): string {
    return this.empresaService.unidadeSelecionada.moeda;
  }

  get valorTotalLocacoes(): number {
    return this.locacoesSelecionadas.reduce((total, locacao) => {
      const valorLocacao = locacao.produtoLocacao.valor || 0;
      const valorProdutos = locacao.totalValorProdutosObrigatorios || 0;
      return total + valorLocacao + valorProdutos;
    }, 0);
  }

  diaDaSemanaAbreviado(data: string): string {
    if(!/^\d{8}$/.test(data)) {
      return "";
    } else {
      const ano = parseInt(data.substring(0,4));
      const mes = parseInt(data.substring(4,6)) - 1;
      const dia = parseInt(data.substring(6,8));

      const dataConvertida = new Date(ano, mes, dia);

      let diaSemana = [];

      if(window.localStorage.getItem('usarSistemaInternacional') == 'true'){
        diaSemana = ['Sun.', 'Mon.', 'Tue.', 'Wed.', 'Thu.', 'Fri.', 'Sat.'];
      } else {
        diaSemana = ['Dom.', 'Seg.', 'Ter.', 'Qua.', 'Qui.', 'Sex.', 'Sáb.'];
      }

      return diaSemana[dataConvertida.getDay()];
    }
  }
}
