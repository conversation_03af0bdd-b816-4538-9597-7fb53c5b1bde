<div class="caixa-locacoes-selecionadas">
  <div class="gridLocacaoSel">
    <div class="gridLocacaoSelLeft">
      <img src="assets/images/icon/icon-shopping-bag.png" height="40" width="37" />
    </div>
    <div class="gridLocacaoSelRight">
      <span class="selecionou">{{'locacao-ambiente.checkout.locacoes-selecionadas' | translate}}:</span>
    </div>
  </div>

  <div class="linha-selec"></div>
  <div [style.border-left-color]="getConfig().cor">

    <div class="locacao-item" *ngFor="let locacao of locacoesSelecionadas; let i = index">
      <div class="locacao-info">
        <span class="locacao-numero">{{i + 1}}.</span>
        <span class="locacao-detalhes">
          {{locacao.nome}} - {{locacao.ambiente}}
        </span>
      </div>
      <div class="locacao-horario">
        {{diaDaSemanaAbreviado(locacao.dia)}} {{locacao.horarioInicial}} - {{locacao.horarioFinal}}
      </div>
      <div class="locacao-valores">
        <div class="valor-locacao">
          <span class="label-valor">Valor:</span>
          <span class="valor">{{locacao.produtoLocacao.valor | currency : moeda : 'symbol'}}</span>
        </div>
        <div class="produtos-obrigatorios" *ngIf="locacao.lstProdutos && locacao.lstProdutos.length > 0">
          <span class="label-produtos">Produtos:</span>
          <div class="lista-produtos">
            <span *ngFor="let pr of locacao.lstProdutos" class="produto-item">
              <span *ngIf="pr.obrigatorio">{{pr.produto.descricao}} - {{pr.valor | currency : moeda : 'symbol'}}</span>
            </span>
          </div>
          <div class="valor-produtos" *ngIf="locacao.totalValorProdutosObrigatorios > 0">
            <span class="label-valor">Total produtos:</span>
            <span class="valor">{{locacao.totalValorProdutosObrigatorios | currency : moeda : 'symbol'}}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="total-geral">
      <div class="linha-total"></div>
      <div class="valor-total">
        <span class="label-total">Total:</span>
        <span class="valor-final" [style.color]="getConfig().cor">{{valorTotalLocacoes | currency : moeda : 'symbol'}}</span>
      </div>
    </div>
  </div>
</div>
