@import "../../../../assets/css/variaveis";

.caixa-locacoes-selecionadas {
  text-align: center;
  padding: 20px 20px 20px 20px;
  margin: 10px 0;
  background: $branco;
  border: 1px solid rgba(189, 195, 199, 0.5);
  box-sizing: border-box;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
}

.gridLocacaoSel {
  display: grid;
  grid-template-columns: 1fr 4fr;
  text-align: left;
  margin-bottom: 15px;
}

.gridLocacaoSelLeft {
  display: flex;
  align-items: center;
  justify-content: center;
}

.gridLocacaoSelRight {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.selecionou {
  color: rgba(44, 52, 59, 0.5);
  font-size: 1.45em;
  font-weight: bold;
  margin-bottom: 5px;
  text-transform: capitalize;
}

.linha-selec {
  margin-top: 15px;
  padding-top: 15px;
  border-top: solid 2px rgba(189, 195, 199, 0.3);
  font-size: 12px;
}

.arrow-right {
  width: 0;
  height: 0;
  border-left: 15px solid;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  margin: 0 auto 20px auto;
}

.locacao-item {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
  text-align: left;
}

.locacao-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.locacao-numero {
  font-weight: bold;
  margin-right: 8px;
  color: #40424C;
}

.locacao-detalhes {
  font-weight: 600;
  color: #40424C;
  font-size: 16px;
}

.locacao-horario {
  color: #6c757d;
  font-size: 14px;
  margin-bottom: 10px;
}

.locacao-valores {
  .valor-locacao, .produtos-obrigatorios {
    margin-bottom: 8px;
  }

  .label-valor, .label-produtos {
    font-weight: 500;
    color: #40424C;
    margin-right: 8px;
  }

  .valor {
    font-weight: 600;
    color: #28a745;
  }

  .lista-produtos {
    margin: 5px 0;
    font-size: 14px;
    color: #6c757d;
  }

  .produto-item {
    display: block;
    margin-bottom: 2px;
  }

  .valor-produtos {
    margin-top: 5px;
  }
}

.total-geral {
  margin-top: 20px;
}

.linha-total {
  border-top: solid 2px rgba(189, 195, 199, 0.5);
  margin-bottom: 15px;
}

.valor-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
}

.label-total {
  font-weight: 600;
  color: #40424C;
}

.valor-final {
  font-weight: 900;
  font-size: 22px;
}

@media only screen and (max-width: 768px) {
  .caixa-locacoes-selecionadas {
    padding: 15px;
  }

  .gridLocacaoSel {
    grid-template-columns: 1fr 3fr;
  }

  .locacao-detalhes {
    font-size: 14px;
  }

  .valor-total {
    font-size: 16px;
  }

  .valor-final {
    font-size: 20px;
  }
}
