import {Component, OnInit} from '@angular/core';
import {FormGroup, Validators} from '@angular/forms';
import {ActivatedRoute, Router} from '@angular/router';
import {Title} from '@angular/platform-browser';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {EmailService} from '@base-core/email/email.service';
import { ReCaptchaV3Service } from 'ng-recaptcha';
import {Email} from '@base-core/email/email.model';
import Swal from 'sweetalert2';

@Component({
  selector: 'pacto-unsubscribe',
  templateUrl: './unsubscribe.component.html',
  styleUrls: ['./unsubscribe.component.scss']
})
export class UnsubscribeComponent implements OnInit {
  formGroup: FormGroup = new FormGroup({});
  exibeCampoEmail: boolean;
  private urlCrypt = this.getUrlCrypt();
  private ipCliente: string;
  private origemEnvio: number;
  private dadosOptin: any;
  private emails: Object;
  logoEmpresa: string;


  constructor(private title: Title,
              private route: ActivatedRoute,
              private router: Router,
              private empresaService: EmpresaService,
              private emailService: EmailService,
              private recaptchaV3Service: ReCaptchaV3Service) {
    this.loadUnidade();
  }

  ngOnInit() {
    this.getDescryt(this.urlCrypt);
    this.title.setTitle('Descadastrar e-mail');
    this.getIP();
  }

  get Validators() {
    return Validators;
  }

  gravar(descadastrarEmail) {
    const dto = this.dadosEmailOptin(descadastrarEmail);
    this.recaptchaV3Service.execute('importantAction')
      .subscribe((token) => {
        this.emails = [dto.email, dto.emailEmpresa];
        const dados = JSON.stringify(this.emails);
        window.localStorage.setItem('dados', dados);
          this.emailService.atualizarEmail(dto, token).subscribe(
            success => {
              this.router.navigate(['/descadastrado-optin']);
            },
            error => console.log(error)
          );
        }
      );
  }

  dadosEmailOptin(descadastrarEmail: boolean) {
    if (this.exibeCampoEmail) {
      this.origemEnvio = 1;
      const email = this.getEmail().email;
      if (email === null || email === '') {
        Swal.fire({
          type: 'error',
          title: 'Erro!',
          text: 'Preencha corretamente o campo e-mail.',
          showConfirmButton: true,
        });
        return;
      }
      return this.preecheDados(descadastrarEmail, email);
    } else {
      this.origemEnvio = 0;
      return this.preecheDados(descadastrarEmail, this.dadosOptin[4]);
    }
  }

  preecheDados(descadastrarEmail, email) {
    const dados = {
      'bloqueadoBounce': descadastrarEmail,
      'chave': this.dadosOptin[0],
      'empresa': Number(this.dadosOptin[1]),
      'emailEmpresa': this.dadosOptin[2],
      'cliente': this.dadosOptin[3],
      'email': email,
      'ipCliente': this.ipCliente,
      'origemEnvio': this.origemEnvio
    };
    return dados;
  }

  getIP() {
    this.emailService.getIpCliente().subscribe((res: any) => {
      this.ipCliente = res.ip;
    });
  }

  getEmail(): Email {
    return new Email(
      this.formGroup.get('email') ? this.formGroup.get('email').value : null
    );
  }

  getUrlCrypt(): string {
    let data: string;
    this.route.queryParams.subscribe(params => {
      data = params.key;
    });
    return data;
  }

  getDescryt(urlCrypt) {
    this.emailService.decrypt(urlCrypt).subscribe((data: any) => {
      this.dadosOptin = data.return;
      if (this.dadosOptin[4] === 'TAG_EMAIL_CLIENTE' || this.dadosOptin[5] === 'TAG_EMAIL_CLIENTE' || this.dadosOptin.length === 4) {
        this.exibeCampoEmail = true;
        this.dadosOptin.splice(3, 1);
      } else {
        this.exibeCampoEmail = false;
      }
    });
  }

  async loadUnidade() {
    try {
      this.tryLoad();
    } catch (e) {
      console.log('Erro ao tentar obter logo da empresa');
      try {
        this.tryLoad();
      } catch (ex) {
        console.log('O erro est persistindo');
        console.log(e);
      }
    }
  }

  private tryLoad() {
    if (!this.empresaService.unidadeSelecionada) {
      this.emailService.obterLogoEmail(
        this.urlCrypt)
        .subscribe(data => {
          this.logoEmpresa = data;
          window.localStorage.setItem('logoEmpresa', this.logoEmpresa);
        });
    }
  }

}
