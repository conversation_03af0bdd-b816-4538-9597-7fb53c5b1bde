import {Component, OnInit} from '@angular/core';
import {Title} from '@angular/platform-browser';

@Component({
  selector: 'pacto-descadastrado-optin',
  templateUrl: './descadastrado-optin.component.html',
  styleUrls: ['./descadastrado-optin.component.scss']
})
export class DescadastradoOptinComponent implements OnInit {
  logoEmpresa: string;

  constructor(private title: Title) { }

  ngOnInit() {
    this.getLogoEmpresa();
    this.title.setTitle('E-mail descadastrado com sucesso!');
    this.dadosDescadastro();
  }

  getLogoEmpresa() {
    this.logoEmpresa = window.localStorage.getItem('logoEmpresa');
  }

  dadosDescadastro() {
    const data = window.localStorage.getItem('dados');
    const emails = JSON.parse(data);
    const dados = {
      'emailCliente': emails[0],
      'emailEmpresa': emails[1]
    };
    return dados;
  }

}
