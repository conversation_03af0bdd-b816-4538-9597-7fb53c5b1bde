import { Component, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {EmailService} from '@base-core/email/email.service';
import {ActivatedRoute, Router} from '@angular/router';
import { ReCaptchaV3Service } from 'ng-recaptcha';
import Swal from 'sweetalert2';

@Component({
  selector: 'pacto-email-optin',
  templateUrl: './email-optin.component.html',
  styleUrls: ['./email-optin.component.scss']
})
export class EmailOptinComponent implements OnInit {
  dados = this.dadosEmailOptin();
  encrypt = this.getCrypt();
  logoEmpresa: string;

  constructor(private title: Title,
              private route: ActivatedRoute,
              private router: Router,
              private empresaService: EmpresaService,
              private emailService: EmailService,
              private recaptchaV3Service: ReCaptchaV3Service) { }
  ngOnInit() {
    this.title.setTitle('Confirmar o recebimento de comunicações e marketing');
    this.loadUnidade();
  }

  getIP() {
    this.emailService.getIpCliente().subscribe((res: any) => {
      this.dados.ipCliente = res.ip;
    });
  }

  gravar() {
    this.recaptchaV3Service.execute('importantAction')
      .subscribe((token) => {
        this.emailService.gravarEmail(this.encrypt, this.dados, token).subscribe(
          (data) => {
            this.emailService.nomeEmpresa = JSON.parse(data).dados.empresaNome;
            this.emailService.emailEmpresa = JSON.parse(data).dados.empresaEmail;
            this.router.navigate(['/agradecimento-optin']);
          },
          (error) => {
            Swal.fire({
              type: 'error',
              title: 'Erro',
              text: error.statusText,
              showConfirmButton: true
            });
          }
        );
      });
  }

  dadosEmailOptin() {
    const dados = {
      'ipCliente': this.getIP(),
      'bloqueadoBounce': false
    };
    return dados;
  }

  loadUnidade(): void {
    try {
      this.tryLoad();
    } catch (e) {
      console.log('Erro ao tentar obter logo da empresa');
      try {
        this.tryLoad();
      } catch (ex) {
        console.log('O erro está persistindo');
        console.log(e);
      }
    }
  }

  private tryLoad() {
    if (!this.empresaService.unidadeSelecionada) {
      this.emailService.obterLogoEmail(
        this.encrypt)
        .subscribe(data => {
          this.logoEmpresa = data;
          window.localStorage.setItem('logoEmpresa', this.logoEmpresa);
        });
    }
  }

  getCrypt() {
    let crypt;
    this.route.queryParams.subscribe(params => {
      crypt = params.key;
    });
    return crypt;
  }

}
