import {Component, OnInit} from '@angular/core';
import {EmailService} from '@base-core/email/email.service';

@Component({
  selector: 'pacto-agradecimento',
  templateUrl: './agradecimento.component.html',
  styleUrls: ['./agradecimento.component.scss']
})
export class AgradecimentoComponent implements OnInit {
  logoEmpresa: string;
  nomeEmpresa: string = this.emailservice.nomeEmpresa;
  emailEmpresa: string = this.emailservice.emailEmpresa;

  constructor(private emailservice: EmailService) { }

  ngOnInit() {
    this.getLogoEmpresa();
  }

  getLogoEmpresa() {
    this.logoEmpresa = window.localStorage.getItem('logoEmpresa');
  }

}
