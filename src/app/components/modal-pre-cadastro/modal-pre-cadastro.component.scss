@import "../../../assets/css/variaveis";

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.84);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-content {
  position: relative;
  background: white;
  padding: 20px;
  border-radius: 8px;
  max-width: 100%;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  min-width: 25vw;
  animation: fadeUp 0.4s ease-out;
}

.close-button {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  margin: 0;
}

.input-container {
  margin: 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.btn-salvar-pre-cadastro {
  width: 50%;
  float: right;
  line-height: 20px;
  padding: 5px;
  font-weight: bold;
  border-radius: 4px;
  color: $branco;
  cursor: pointer;
  text-transform: uppercase;
  margin-top: 30px;
  border: none;
}

@media(max-width: 900px) {
  .modal-content {
    min-width: 90vw;
  }
}
