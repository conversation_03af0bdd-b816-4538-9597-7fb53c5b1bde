<div *ngIf="getConfig()?.habilitarPreCadastro" class="modal-overlay">
  <div class="modal-content">
    <div class="modal-header">
      <span style="color: #242424">Pré-cadastro</span>
    </div>
    <div class="input-container">
      <pacto-input
        idinput="idNomePreCadastro"
        [label]="'Nome completo'"
        [name]="'nome'"
        [pactoFormGroup]="formGroup"
        [placeholder]="'Digite seu nome completo'"
      ></pacto-input>
      <pacto-input
        idinput="idTelefonePreCadastro"
        [label]="'Telefone'"
        [textMask]="mascaraTelefone ? { mask: mascaraTelefone, guide: false } : { mask: false }"
        [name]="'telefone'"
        [pactoFormGroup]="formGroup"
        [placeholder]="'Digite seu telefone'"
      ></pacto-input>
      <pacto-input
        idinput="idEmailPreCadastro"
        [label]="'E-mail'"
        [name]="'email'"
        [pactoFormGroup]="formGroup"
        [placeholder]="'<EMAIL>'"
      ></pacto-input>
    </div>
    <button id="btn-prossegue-para-checkout" class="btn-salvar-pre-cadastro"
            (click)="finalizarCadastro()"
            [style.background-color]="getConfig()?.cor">
      Salvar <i class="pct-arrow-right"></i>
    </button>
  </div>
</div>
