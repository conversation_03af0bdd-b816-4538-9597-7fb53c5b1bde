import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { EmpresaService } from '@base-core/empresa/empresa.service';
import { Config } from '@base-core/empresa/config.model';
import Swal from 'sweetalert2';
import { ReCaptchaV3Service } from 'ng-recaptcha';
import { Lead } from '@base-core/lead/lead.model';
import { LeadService } from '@base-core/lead/lead.service';
import {LocalizationService} from '@base-core/service/localization.service';
import {FacebookPixelService} from '@base-core/analytics/facebook-pixel.service';

@Component({
  selector: 'pacto-modal-pre-cadastro',
  templateUrl: './modal-pre-cadastro.component.html',
  styleUrls: ['./modal-pre-cadastro.component.scss']
})
export class ModalPreCadastroComponent implements OnInit {

  @Output() fechar = new EventEmitter<void>();
  public formGroup: FormGroup = new FormGroup({});
  private processando = false;
  private codunidade: any;
  private chave: any;

  public mascaraTelefone;

  constructor(
    private empresaService: EmpresaService,
    private recaptchaV3Service: ReCaptchaV3Service,
    private leadService: LeadService,
    private localizationService: LocalizationService,
    private  analitycsPixel: FacebookPixelService
  ) { }

  ngOnInit() {
    this.mascaraTelefone = this.localizationService.getPhoneLocaleMask();
    this.chave = window.localStorage.getItem('chave');
    this.codunidade = window.localStorage.getItem('unidade');
    this.leadService.chave = this.chave;
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  private getPreCadastroClienteJson(): Lead {
    return new Lead(
      this.formGroup.get('nome').value,
      this.formGroup.get('email').value,
      this.formGroup.get('telefone').value
    );
  }

  private exibirErro(msg: string): void {
    this.processando = false;
    const title = 'Algo deu errado.';
    const text = 'O pré-cadastro não foi realizado: ' + msg;
    Swal.fire({
      type: 'error',
      title: title,
      text: text,
      showConfirmButton: true,
      scrollbarPadding: false,
      confirmButtonColor: this.getConfig().cor,
    });
  }

  finalizarCadastro() {
    const validadoMsg = this.leadService.validarLead(this.getPreCadastroClienteJson(), this.empresaService.config);
    if (validadoMsg !== 'Sucesso') {
      Swal.fire({
        type: 'error',
        text: validadoMsg,
        showConfirmButton: true,
        scrollbarPadding: false,
        confirmButtonColor: this.getConfig().cor
      });
      return;
    }
    Swal.fire({
      type: 'info',
      title: 'Aguarde...',
      showConfirmButton: false,
      confirmButtonColor: this.getConfig().cor,
      scrollbarPadding: false
    });

      this.processando = true;
      const dto = this.getPreCadastroClienteJson();
      this.leadService.gravarCadastroLead(dto, this.codunidade).subscribe((result: any) => {
        if (result.success) {
          this.leadService.setNome(dto.nome);
          this.leadService.setTelefone(dto.telefone);
          this.leadService.setEmail(dto.email);
          this.closeModal();
          this.formGroup.reset();
          this.analitycsPixel.triggerEventRealizouPreCadastro(dto);
          Swal.fire({
            type: 'success',
            title: 'Cadastro realizado',
            showConfirmButton: false,
            timer: 1000
          });
        } else {
          this.exibirErro(result.erro);
        }
      });

  }

  closeModal() {
    this.fechar.emit();
  }
}
