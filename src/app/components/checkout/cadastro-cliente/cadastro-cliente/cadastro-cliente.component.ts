import {Component, OnInit} from '@angular/core';
import {FormGroup, Validators} from '@angular/forms';
import {ActivatedRoute, Router} from '@angular/router';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {PlanoService} from '@base-core/plano/plano.service';
import {Plano} from '@base-core/plano/plano.model';
import {Config} from '@base-core/empresa/config.model';
import {AlunoService} from '@base-core/aluno/aluno.service';
import Swal from 'sweetalert2';
import {ProdutoService} from '@base-core/produto/produto.service';
import {Venda} from '@base-core/negociacao/venda.model';
import {Cliente} from '@base-core/cliente/cliente.model';
import {Aluno, AlunoAdapter} from '@base-core/aluno/aluno.model';
import {ReCaptchaV3Service} from 'ng-recaptcha';
import {ClienteService} from '@base-core/cliente/cliente.service';
import {TranslateService} from '@ngx-translate/core';
import {LocalizationService} from '@base-core/service/localization.service';
import {Empresa} from '@base-core/empresa/empresa.model';
import {PaginaVendasOnLine} from '@base-core/negociacao/acesso-pagina';
import { OrigemSistemaEnum } from '@base-core/negociacao/enum-origem-sistema';

@Component({
  selector: 'app-cadastro-cliente',
  templateUrl: './cadastro-cliente.component.html',
  styleUrls: ['./cadastro-cliente.component.sass']
})
export class CadastroClienteComponent implements OnInit {
  formGroup: FormGroup = new FormGroup({});
  mascaracpf = [/\d|\*/, /\d|\*/, /\d|\*/, '.', /\d|\*/, /\d|\*/, /\d|\*/, '.', /\d|\*/, /\d|\*/, /\d|\*/, '-', /\d|\*/, /\d|\*/];
  mascaraTelefone;
  cpfValidado = false;
  emailValidado = false;
  processando = false;
  menorIdade = false;
  remetenteConviteMatricula = '';
  remetenteConviteNome = '';
  freepass = '';
  dadosCompletosDesmascarados: any;

  constructor(private route: ActivatedRoute,
              private planoService: PlanoService,
              private produtoService: ProdutoService,
              private negociacaoService: NegociacaoService,
              private alunoService: AlunoService,
              private alunoAdapter: AlunoAdapter,
              private recaptchaV3Service: ReCaptchaV3Service,
              private empresaService: EmpresaService,
              private clienteService: ClienteService,
              private translateService: TranslateService,
              private localizationService: LocalizationService,
              private router: Router
  ) {

    this.route.queryParams.subscribe(params => {
        if ( params['k'] && params['un'] ) {
        this.negociacaoService.chave = params['k'];
        this.clienteService.chave = params['k'];
        this.negociacaoService.codunidade = params['un'];
        this.remetenteConviteMatricula = params['rcvm'];
        this.remetenteConviteNome = params['rcv'];
        this.freepass = params['fp'];
        window.localStorage.setItem('chave', params['k']);
        window.localStorage.setItem('unidade', params['un']);
        window.localStorage.setItem('remetenteConviteMatricula', params['rcvm']);
        window.localStorage.setItem('remetenteConviteNome', params['rcv']);
        window.localStorage.setItem('fp', params['fp']);
      } else {
        this.negociacaoService.chave = window.localStorage.getItem('chave');
        this.negociacaoService.codunidade = window.localStorage.getItem('unidade');
      }
      if (params['evento']) {
        this.negociacaoService.codigoEvento = Number(params['evento']);
      }
      if (params['us']) {
        this.negociacaoService.usuarioResponsavel = params['us'];
      }
      this.loadUnidade();
    });
  }

  ngOnInit() {
    this.mascaraTelefone = this.getPhoneLocaleMask();
    this.negociacaoService.registrarAcessoPagina(PaginaVendasOnLine[PaginaVendasOnLine.VISITANTE], this.router.url);
    this.dadosCompletosDesmascarados = null;
  }

  getVendaJson(): Venda {
    return new Venda(this.empresaService.unidadeSelecionada.codigo,
      (this.planoService.planoSelecionado ? this.planoService.planoSelecionado.codigo : 0),
      (this.formGroup.get('nome') ? this.formGroup.get('nome').value : null),
      (this.formGroup.get('cpf') ? this.formGroup.get('cpf').value : null),
      (this.formGroup.get('dataNascimento') ? this.formGroup.get('dataNascimento').value : ''),
      (this.formGroup.get('email') ? this.formGroup.get('email').value : null),
      (this.formGroup.get('nomecartao') ? this.formGroup.get('nomecartao').value : null),
      (this.formGroup.get('nrcartao') ? this.formGroup.get('nrcartao').value : null),
      (this.formGroup.get('validade') ? this.formGroup.get('validade').value : null),
      (this.formGroup.get('cvv') ? this.formGroup.get('cvv').value : null),
      (this.formGroup.get('telefone') ? this.formGroup.get('telefone').value : null),
      (this.formGroup.get('endereco') ? this.formGroup.get('endereco').value : null),
      (this.formGroup.get('numero') ? this.formGroup.get('numero').value : null),
      (this.formGroup.get('bairro') ? this.formGroup.get('bairro').value : null),
      (this.formGroup.get('complemento') ? this.formGroup.get('complemento').value : null),
      (this.formGroup.get('cep') ? this.formGroup.get('cep').value : null),
      (this.formGroup.get('diavencimento') ? this.formGroup.get('diavencimento').value : null),
      (this.formGroup.get('parcelasCartao') ? this.formGroup.get('parcelasCartao').value : null),
      (this.formGroup.get('cupomdesconto') ? this.formGroup.get('cupomdesconto').value : ''),
      (this.formGroup.get('cpftitularcard') ? this.formGroup.get('cpftitularcard').value : null),
      (this.formGroup.get('vencimentoFatura') ? this.formGroup.get('vencimentoFatura').value : 0),
      (this.formGroup.get('vencimentoFatura') ? this.formGroup.get('vencimentoFatura').value : 0),
      (this.formGroup.get('vencimentoFatura') ? this.formGroup.get('vencimentoFatura').value : 0),
      (this.formGroup.get('vencimentoFatura') ? this.formGroup.get('vencimentoFatura').value : 0),
      (this.formGroup.get('vencimentoFatura') ? this.formGroup.get('vencimentoFatura').value : 0),
      (this.formGroup.get('responsavelPai') ? this.formGroup.get('responsavelPai').value : null),
      (this.formGroup.get('responsavelMae') ? this.formGroup.get('responsavelMae').value : null),
      (this.formGroup.get('cpfMae') ? this.formGroup.get('cpfMae').value : null),
      (this.formGroup.get('cpfPai') ? this.formGroup.get('cpfPai').value : null),
      [], false, null, this.negociacaoService.origemCobranca, this.negociacaoService.cobrancaAntecipada,
      this.negociacaoService.responsavel, this.negociacaoService.token, this.planoService.vezesEscolhidasParcelarTaxaMatricula,
      (this.formGroup.get('rg') ? this.formGroup.get('rg').value : null), this.negociacaoService.codigoEvento,
      this.negociacaoService.usuarioResponsavel, this.negociacaoService.codigoRegistroAcessoPagina,
      '',
      (this.formGroup.get('tipoCredito') ? this.formGroup.get('tipoCredito').value : null), '',
      (this.formGroup.get('cnpj') ? this.formGroup.get('cnpj').value : null),
      (this.formGroup.get('nomeResponsavelEmpresa') ? this.formGroup.get('nomeResponsavelEmpresa').value : null),
      (this.formGroup.get('cpfResponsavelEmpresa') ? this.formGroup.get('cpfResponsavelEmpresa').value : null),
      this.negociacaoService.lancarContratoConcomitanteAoInvesDeRenovar,
      this.negociacaoService.pactoPayComunicacao,
      '', null, null, null, null, false,
      OrigemSistemaEnum.VENDAS_ONLINE, null, false,
      false,
      []
    );
  }

  loadUnidade(): void {
    try {
      this.tryLoad();
    } catch (e) {
      console.log('Erro ao tentar obter a empresa, vou tentar novamente');
      try {
        this.tryLoad();
      } catch (ex) {
        console.log('O erro persistiu');
        console.log(this.negociacaoService.chave);
        console.log(this.negociacaoService.codunidade);
        console.log(e);
      }
    }
  }

  getPhoneLocaleMask() {
    return this.localizationService.getPhoneLocaleMask();
  }

  consultarPorCpf(): void {
    this.cpfValidado = false;
    if (!this.formGroup.get('cpf')) {
      return;
    }
    this.recaptchaV3Service.execute('importantAction')
      .subscribe((token) => {
        this.alunoService.cpfTelaVisitante(this.negociacaoService.chave, this.getUnidadeSelecionada().codigo, this.formGroup.get('cpf').value, token,
          this.getUnidadeSelecionada().utilizaGestaoClientesComRestricoes)
          .subscribe((data: any) => {
            if (data.erro && !data.erro.toString().toUpperCase().includes('NENHUM CLIENTE ENCONTRADO')) {
              if (data.erro.includes('Este CPF informado já está cadastrado no sistema.')) {
                Swal.fire({
                  type: 'warning',
                  title: 'Atenção!',
                  text: data.erro,
                  showConfirmButton: true,
                });
              } else {
              Swal.fire({
                type: 'error',
                title: 'Ops!',
                text: data.erro,
                showConfirmButton: true,
              });
            }
              return;
            }
            if (data.unidadesRestricoes && data.unidadesRestricoes.length > 0) {
              this.exibirMensagemRestricao(data.unidadesRestricoes);
              return;
            }
            if (data.return) {
              if (data.return.length > 1) {
                Swal.fire({
                  type: 'error',
                  title: 'Verificação',
                  text: 'Mais de um cadastro encontrado para este CPF, não será possível prosseguir!',
                  showConfirmButton: true,
                });
                this.formGroup.get('cpf').setValue('');
                return;
              } else if (this.cpfAtivoEDifUnidade(data.return[0])) {
                Swal.fire({
                  type: 'warning',
                  title: 'Atenção!',
                  text: 'Este CPF está ativo em outra unidade, não será possível prosseguir!',
                  showConfirmButton: true,
                });
                this.formGroup.get('cpf').setValue('');
              } else {
                this.validarUso('cpf', 'CPF foi encontrado na base', data.return[0], 'cpf');
              }
            }
          });
      });
  }

  consultarCep(): void {
    if (!this.formGroup.get('cep')) {
      return;
    }
    this.alunoService.cep(this.cepSemMascara(this.formGroup.get('cep').value)).subscribe(data => {
      this.preencherCEP(data);
    });
  }

  cepSemMascara(cep): string {
    return cep.replace('.', '').replace('-', '');
  }

  preencherCEP(data): void {
    if (data.return) {
      this.formGroup.get('endereco').setValue(data.return.enderecoLogradouro);
      this.formGroup.get('bairro').setValue(data.return.bairroDescricao);
    }
  }

  consultarPorEmail(): void {
    this.emailValidado = false;
    if (!this.formGroup.get('email')) {
      return;
    }
    this.recaptchaV3Service.execute('importantAction')
      .subscribe((token) => {
        this.alunoService.emailTelaVisitante(this.negociacaoService.chave, this.formGroup.get('email').value, token, this.getUnidadeSelecionada().codigo)
          .subscribe((data: any) => {
            if (data.erro && !data.erro.toString().toUpperCase().includes('NENHUM CLIENTE ENCONTRADO')) {
              Swal.fire({
                type: 'error',
                title: 'Erro',
                text: data.erro,
                showConfirmButton: true,
              });
              return;
            }
            if (data.return) {
              if (data.return.length > 1) {
                Swal.fire({
                  type: 'error',
                  title: this.translateService.instant('checkout.verificacao'),
                  text: this.translateService.instant('checkout.mais-de-um-email-encontrado'),
                  showConfirmButton: true,
                });
                this.formGroup.get('email').setValue('');
                return;
              }
              const mensagemModal = this.translateService.instant('checkout.email-encontrado-na-base');
              this.validarUso(
                'email',
                mensagemModal,
                data.return[0],
                'email',
              );
            }
          });
      });
  }

  private validarUso(operacao, msg, data, campolimpar): void {
    if (data.matricula && ((operacao === 'email' && !this.cpfValidado) || (operacao === 'cpf' && !this.emailValidado))) {
      Swal.fire({
        title: msg,
        text: this.translateService.instant('checkout.seu-nome-e') + this.mascararNome(data.nome) + '?',
        type: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: this.translateService.instant('checkout.sim-sou-eu'),
        cancelButtonText: this.translateService.instant('checkout.nao-sou-eu')
      }).then((result) => {
        if (result.value) {
          if (operacao === 'cpf') {
            this.cpfValidado = true;
          } else if (operacao === 'email') {
            this.emailValidado = true;
          }

          // preencher objeto aluno
          this.alunoService.alunoSelecionado = this.alunoAdapter.adapt(data);
          console.log(this.alunoService.alunoSelecionado);

          // campos obrigatórios
          // Preencher os dados mascarados no formulário
          this.preencherFormularioComDadosMascarados(data);

          if (this.formGroup.get('responsavelMae')) {
            this.formGroup.get('responsavelMae').setValue(data.nomeResponsavelMae);
          }
          if (this.formGroup.get('cpfMae')) {
            this.formGroup.get('cpfMae').setValue(data.cpfResponsavelMae);
          }
          if (this.formGroup.get('responsavelPai')) {
            this.formGroup.get('responsavelPai').setValue(data.nomeResponsavelPai);
          }
          if (this.formGroup.get('cpfPai')) {
            this.formGroup.get('cpfPai').setValue(data.cpfResponsavelPai);
          }

          if (this.getConfig().habilitarAgendamentoAulaExperimentalLinkVisitante &&
            (data.situacao !== 'Visitante' || (data.situacao === 'Visitante' && data.existeFreePassHistorico))) {
            Swal.fire({
              type: 'warning',
              title: 'Aula Experimental',
              text: 'Recurso permitido apenas para cadastros novos, uma vez utilizado,' +
                ' o fluxo de agendamento de aula experimental não é mais permitido.' +
                ' Seu cadastro será apenas atualizado ao clicar em \"Finalizar Cadastro\".',
              showConfirmButton: true
            });
            this.getConfig().habilitarAgendamentoAulaExperimentalLinkVisitante = false;
          }
          const cpfSemMascara = data.cpf ? data.cpf.replace(/\D/g, '') : '';
          if (this.getUnidadeSelecionada().utilizaGestaoClientesComRestricoes && cpfSemMascara !== '') {
            this.recaptchaV3Service.execute('importantAction').subscribe((token) => {
              this.alunoService.consultarUnidadesRestricoes(this.negociacaoService.chave, token, cpfSemMascara)
                .subscribe((response: any) => {
                  if (response && response.return && response.return.length > 0) {
                    this.exibirMensagemRestricao(response.return);
                  }
                });
            });
          }
          this.verificaMenorDeIdade();
        } else {
          if (this.formGroup.get(campolimpar)) {
            this.formGroup.get(campolimpar).setValue('');
            const tituloModal = this.translateService.instant('checkout.atencao');
            const mensagemModal = this.translateService.instant('checkout.seus-dados-podem-estar-sendo-utilizados-em-outro-cadastro');
            Swal.fire(
              tituloModal,
              mensagemModal,
              'warning'
            );
          }
        }
      });
    }
  }

  private preencherFormularioComDadosMascarados(dados: any): void {
    this.formGroup.patchValue({
      nome: this.mascararNome(dados.nome),
      cpf: this.mascararCpf(dados.cpf),
      email: this.mascararEmail(dados.email)
    });
    if (this.formGroup.get('dataNascimento')) {
      this.formGroup.patchValue({
        dataNascimento: dados.dataNascimento ? '**/**/****' : '',
      });
    }
    if (this.formGroup.get('telefone')) {
      if (dados.telCelular) {
        this.formGroup.patchValue({
          telefone: this.mascararTelefone(dados.telCelular),
        });
      } else if (dados.telResidencial) {
        this.formGroup.patchValue({
          telefone: this.mascararTelefone(dados.telResidencial),
        });
      }
    }
    if (this.formGroup.get('sexo')) {
      this.formGroup.patchValue({
        sexo: dados.sexo === 'M' ? 'Masculino' : 'Feminino'
      });
    }
    if (this.formGroup.get('rg')) {
      this.formGroup.patchValue({
        rg: dados.rg ? this.mascararRG(dados.rg) : '',
      });
    }
    if (dados.enderecoCompleto != null) {
      const endereco = JSON.parse(dados['enderecoCompleto'].toString());
      if (endereco) {
        if (this.formGroup.get('cep') && endereco.cep) {
          this.formGroup.patchValue({
            cep: this.mascararCep(endereco.cep)
          });
        }
        if (this.formGroup.get('endereco') && endereco.endereco) {
          this.formGroup.patchValue({
            endereco: this.mascararString(endereco.endereco)
          });
        }
        if (this.formGroup.get('numero') && endereco.numero) {
          this.formGroup.patchValue({
            numero: this.mascararNumeroEndereco(endereco.numero)
          });
        }
        if (this.formGroup.get('bairro') && endereco.bairro) {
          this.formGroup.patchValue({
            bairro: this.mascararString(endereco.bairro)
          });
        }
        if (this.formGroup.get('complemento') && endereco.complemento) {
          this.formGroup.patchValue({
            complemento: this.mascararString(endereco.complemento)
          });
        }
      }
    }

    // Armazene os dados completos sem mascaras em uma variável para envio posterior
    this.dadosCompletosDesmascarados = dados;
  }

  private mascararNome(nome: string): string {
    // Separar o nome completo em partes (palavras)
    const partesNome = nome.split(' ');

    // Garantir que haja pelo menos um nome
    if (partesNome.length === 0) {
      return nome;
    }

    // Primeiro nome, deve ser exibido completamente
    let resultado = partesNome[0];

    // Para todos os nomes intermediários, devem ser mascarados completamente
    for (let i = 1; i < partesNome.length - 1; i++) {
      resultado += ' ' + partesNome[i][0] + '*'.repeat(partesNome[i].length - 1);
    }

    // Se houver sobrenome, exibe as duas primeiras letras e mascara o resto
    if (partesNome.length > 1) {
      const sobrenome = partesNome[partesNome.length - 1];
      resultado += ' ' + sobrenome.slice(0, 2) + '*'.repeat(sobrenome.length - 2);
    }

    return resultado;
  }

  private mascararCpf(cpf: string): string {
    // Remover todos os caracteres que não sejam números
    const cpfLimpo = cpf.replace(/\D/g, '');

    // Verificar se o CPF limpo possui 11 dígitos e aplicar a máscara
    if (cpfLimpo.length === 11) {
      return cpfLimpo.replace(/^(\d{3})(\d{3})(\d{3})(\d{2})$/, '$1.***.***-**');
    }

    // Se o CPF não tiver 11 dígitos, retornar o valor original
    return cpf;
  }

  private mascararEmail(email: string): string {
    const [localPart, domain] = email.split('@');

    // Máscara da parte local, mantendo os 4 primeiros caracteres e substituindo o restante por "****"
    const localPartMascarado = `${localPart.slice(0, 4)}****@`;

    // Máscara do domínio inteiro
    const dominioMascarado = domain.replace(/./g, '*');

    return `${localPartMascarado}${dominioMascarado}`;
  }

  private mascararRG(rg) {
    // Verifica se o RG tem comprimento suficiente para aplicar a máscara
    if (rg.length > 2) {
      return rg.substring(0, 2) + rg.substring(2, rg.length - 1).replace(/\d/g, '*') + rg.charAt(rg.length - 1);
    }
    return rg;  // Caso o RG tenha apenas 2 caracteres, não há o que mascarar
  }

  private mascararString(endereco) {
    const palavras = endereco.split(' '); // Divide o endereço em palavras
    const palavrasMascaradas = palavras.map((palavra, index) => {
      if (index === 0) {
        // Primeira palavra: mantém os 3 primeiros caracteres abertos e mascara o restante
        return palavra.slice(0, 3) + palavra.slice(3).replace(/\S/g, '*');
      } else {
        // Outras palavras: mantém o primeiro caractere aberto e mascara o restante
        return palavra.slice(0, 1) + palavra.slice(1).replace(/\S/g, '*');
      }
    });

    return palavrasMascaradas.join(' '); // Junta as palavras mascaradas novamente em uma string
  }

  private mascararNumeroEndereco(numero) {
    // Mantém o primeiro caractere e mascara o restante
    return numero.charAt(0) + numero.slice(1).replace(/\S/g, '*');
  }

  private mascararTelefone(telefone: string): string {
    if (!telefone) {
      return ''; // Retorna vazio se o telefone for nulo ou indefinido
    }

    // Remove quaisquer caracteres que não sejam números
    const telefoneLimpo = telefone.replace(/\D/g, '');

    // Aplica a máscara com base no comprimento do número
    if (telefoneLimpo.length === 10) {
      // Telefone fixo (8 dígitos)
      return telefoneLimpo.replace(/(\d{2})(\d{4})(\d{4})/, '($1) ****-$3');
    } else if (telefoneLimpo.length === 11) {
      // Telefone celular (9 dígitos)
      return telefoneLimpo.replace(/(\d{2})(\d{5})(\d{4})/, '($1) *****-$3');
    } else {
      // Retorna o número original caso não seja um telefone válido
      return telefone;
    }
  }

  private mascararCep(cep: string): string {
    return cep.replace(/^(\d{2})\.(\d{3})-(\d{3})$/, '**.***-$3');
  }

  private tryLoad() {
    if (!this.empresaService.unidadeSelecionada) {
      console.log('Try to load company');
      this.empresaService.obterEmpresa(
        this.negociacaoService.chave,
        this.negociacaoService.codunidade)
        .subscribe(data => this.empresaService.unidadeSelecionada = data);
    }
    this.empresaService.obterConfigs(this.negociacaoService.chave,
      this.negociacaoService.codunidade).subscribe(data => {
      this.empresaService.config = data;
    });
  }

  get Validators() {
    return Validators;
  }

  getPlanoSelecionado(): Plano {
    return this.planoService.planoSelecionado;
  }

  getValorTotalProdutosSelecionados(): number {
    return this.produtoService.getValorTotalProdutosSelecionados();
  }

  getProdutosPlano(): string[] {
    return this.getPlanoSelecionado().produtos.split('<br/>');
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  getUnidadeSelecionada(): Empresa {
    return this.empresaService.unidadeSelecionada;
  }

  getValorTotal(): number {
    return this.negociacaoService.valorFinalContrato;
  }

  finalizarCadastro() {
    // validar
    const validado = this.clienteService.validar(this.getCadastroClienteJsonDecrypt(), this.empresaService.config);
    if (validado) {
      Swal.fire({
        type: 'warning',
        text: validado,
        showConfirmButton: true,
      });
      return;
    }
    Swal.fire({
      title: 'Processando...',
      onOpen: function () {
        Swal.showLoading();
      }
    });
    this.recaptchaV3Service.execute('importantAction')
      .subscribe((token) => {
          this.processando = true;
          const dto = this.getCadastroClienteJsonDecrypt();
        this.clienteService.gravarCadastro(dto, token).subscribe((result) => {
            console.log('Resposta tentativa gravar cadastro: ' + result['return']);
            if (result['return'] === 'ALUNO CADASTRADO COM SUCESSO' || result['return'] === 'FREEPASS LANÇADO COM SUCESSO' ||
              result['return'] === 'ALUNO ATUALIZADO COM SUCESSO') {
              const freePass = result['return'];
              console.log('Entrou if de FreePass: ' + result['return']);
              this.concluir(freePass, result['voucher']);
            } else if (result['return'] === undefined && result['erro'] !== undefined) {
              this.RealizaodAprovada(result['erro']);
            } else {
              this.RealizaodAprovada(result['return']);
            }
          });
        }
      );
  }

  opcaoSexo(): any[] {
    const opcao = [
      {id: '', label: ''},
      {id: 'Masculino', label: this.translateService.instant('masculino')},
      {id: 'Feminino', label: this.translateService.instant('feminino')}
    ];
    return opcao;
  }


  concluir(freePass: String, voucher?: any): void {
    let title  = '';
    if (freePass === 'FREEPASS LANÇADO COM SUCESSO' || freePass === 'ALUNO ATUALIZADO COM SUCESSO') {
      console.log('Entrou if de concluir com FreePass: ' + freePass);
      title = this.translateService.instant('checkout.sucesso');
    } else {
      console.log('Entrou else de concluir com FreePass: ' + freePass);
      title = this.translateService.instant('checkout.cadastro-realizado-com-sucesso');
    }
    let htmlDescription = '';
    if (voucher && voucher !== '') {
      htmlDescription = `${this.translateService.instant('checkout.seu-voucher')} ${voucher} </br>`;
    }
    if (freePass === 'FREEPASS LANÇADO COM SUCESSO') {
      htmlDescription += `${this.translateService.instant('checkout.free-pass-lancado-com-sucesso')}`;
    } else if (freePass === 'ALUNO ATUALIZADO COM SUCESSO') {
      htmlDescription += `${this.translateService.instant('checkout.cadastro-aluno-atualizado-sucesso')}`;
      htmlDescription += ` `;
      htmlDescription += `${this.translateService.instant('checkout.free-pass-ja-existe-nao-lancar')}`;
    } else {
      htmlDescription += `${this.translateService.instant('checkout.parabens-bom-treino')}`;
    }
    Swal.fire({
      type: 'success',
      title: title,
      html: htmlDescription,
      showConfirmButton: true,
      onClose: () => {
        this.redirectPosVenda();
      }
    });
  }
  alunoSelecionado(): Aluno {
    return this.alunoService.alunoSelecionado ? this.alunoService.alunoSelecionado : new Aluno(null, '', '', '',
      '', 0.0, '', false, 1, 0.0, [], '',
      '', '', '');
  }
  redirectPosVenda(): void {
    Swal.fire({
      onOpen: function () {
        Swal.showLoading();
      }
    });
    window.location.href = this.urlRedirect();
  }
  urlRedirect(): string {
    if (this.empresaService.config === undefined ||
      ((!(this.empresaService.config === undefined)) &&
        this.empresaService.config != null &&
        this.empresaService.config.url != null &&
        this.empresaService.config.url === '')) {
      var url = window.location.href;
      if (window.location.href.includes('pagamento')) {
        url = window.location.href.replace('pagamento', 'pospagamento');
        url = url.substring(0, url.indexOf('&cliente'));
      } else if (window.location.href.includes('checkout')) {
        url = url.substring(0, url.indexOf('checkout'));
        url = url + 'pospagamento?un=' + this.negociacaoService.codunidade + '&k=' + this.negociacaoService.chave;
      }
      return url;
    }
    return this.empresaService.config.url;
  }
  RealizaodAprovada(msg: string): void {
    this.processando = false;
    const title = this.translateService.instant('checkout.aluno-nao-foi-adicionado');
    const text =  this.translateService.instant('checkout.cadastro-nao-realizado-revisar-informacoes') + msg;
    Swal.fire({
      type: 'error',
      title: title,
      text: text,
      showConfirmButton: true
    });
  }

  getCadastroClienteJson(): Cliente {
    return new Cliente(
      this.empresaService.unidadeSelecionada.codigo,
      this.formGroup.get('nome').value,
      (this.formGroup.get('cpf') ? this.formGroup.get('cpf').value : null),
      (this.formGroup.get('responsavelPai') ? this.formGroup.get('responsavelPai').value : null),
      (this.formGroup.get('responsavelMae') ? this.formGroup.get('responsavelMae').value : null),
      (this.formGroup.get('sexo') ? this.formGroup.get('sexo').value : ''),
      (this.formGroup.get('dataNascimento') ? this.localizationService.getDataFormatoPtbr(this.formGroup.get('dataNascimento').value) : ''),
      this.formGroup.get('email').value,
      (this.formGroup.get('telefone') ? this.formGroup.get('telefone').value : null),
      (this.formGroup.get('cep') ? this.formGroup.get('cep').value : null),
      (this.formGroup.get('endereco') ? this.formGroup.get('endereco').value : null),
      (this.formGroup.get('numero') ? this.formGroup.get('numero').value : null),
      (this.formGroup.get('bairro') ? this.formGroup.get('bairro').value : null),
      (this.formGroup.get('complemento') ? this.formGroup.get('complemento').value : null),
      (this.formGroup.get('cpfMae') ? this.formGroup.get('cpfMae').value : null),
      (this.formGroup.get('cpfPai') ? this.formGroup.get('cpfPai').value : null),
      this.remetenteConviteMatricula,
      (this.formGroup.get('rg') ? this.formGroup.get('rg').value : null), this.negociacaoService.codigoEvento,
      this.negociacaoService.usuarioResponsavel, this.negociacaoService.codigoRegistroAcessoPagina,
      this.freepass, null, false, null
    );
  }

  getCadastroClienteJsonDecrypt(): Cliente {
    if (this.dadosCompletosDesmascarados) {
      const endereco = JSON.parse(this.dadosCompletosDesmascarados['enderecoCompleto'].toString());
      let telefoneDesmascarado = '';
        if (this.dadosCompletosDesmascarados.telCelular) {
          telefoneDesmascarado = this.dadosCompletosDesmascarados.telCelular;
        } else if (this.dadosCompletosDesmascarados.telResidencial) {
          telefoneDesmascarado = this.dadosCompletosDesmascarados.telResidencial;
        }
      return new Cliente(
        this.empresaService.unidadeSelecionada.codigo,
        (this.formGroup.get('nome') && this.formGroup.get('nome').value.includes('*') ? this.dadosCompletosDesmascarados.nome : this.formGroup.get('nome').value),
        (this.formGroup.get('cpf') && this.formGroup.get('cpf').value.includes('*') ? this.dadosCompletosDesmascarados.cpf : (this.formGroup.get('cpf') ? this.formGroup.get('cpf').value : null)),
        (this.formGroup.get('responsavelPai') && this.formGroup.get('responsavelPai') ? this.formGroup.get('responsavelPai').value : null),
        (this.formGroup.get('responsavelMae') && this.formGroup.get('responsavelMae') ? this.formGroup.get('responsavelMae').value : null),
        (this.formGroup.get('sexo') && this.formGroup.get('sexo') ? this.formGroup.get('sexo').value : ''),
        (this.formGroup.get('dataNascimento') && this.formGroup.get('dataNascimento').value.includes('*') ? this.dadosCompletosDesmascarados.dataNascimento : (this.formGroup.get('dataNascimento') ? this.localizationService.getDataFormatoPtbr(this.formGroup.get('dataNascimento').value) : '')),
        (this.formGroup.get('email') && this.formGroup.get('email').value.includes('*') ? this.dadosCompletosDesmascarados.email : this.formGroup.get('email').value),
        (this.formGroup.get('telefone') && this.formGroup.get('telefone').value.includes('*') ? telefoneDesmascarado : (this.formGroup.get('telefone') ? this.formGroup.get('telefone').value : null)),
        (this.formGroup.get('cep') && this.formGroup.get('cep').value.includes('*') ? endereco.cep : (this.formGroup.get('cep') ? this.formGroup.get('cep').value : null)),
        (this.formGroup.get('endereco') && this.formGroup.get('endereco').value.includes('*') ? endereco.endereco : (this.formGroup.get('endereco') ? this.formGroup.get('endereco').value : null)),
        (this.formGroup.get('numero') && this.formGroup.get('numero').value.includes('*') ? endereco.numero : (this.formGroup.get('numero') ? this.formGroup.get('numero').value : null)),
        (this.formGroup.get('bairro') && this.formGroup.get('bairro').value.includes('*') ? endereco.bairro : (this.formGroup.get('bairro') ? this.formGroup.get('bairro').value : null)),
        (this.formGroup.get('complemento') && this.formGroup.get('complemento').value.includes('*') ? endereco.complemento : (this.formGroup.get('complemento') ? this.formGroup.get('complemento').value : null)),
        (this.formGroup.get('cpfMae') && this.formGroup.get('cpfMae') ? this.formGroup.get('cpfMae').value : null),
        (this.formGroup.get('cpfPai') && this.formGroup.get('cpfPai') ? this.formGroup.get('cpfPai').value : null),
        this.remetenteConviteMatricula,
        (this.formGroup.get('rg') && this.formGroup.get('rg').value.includes('*') ? this.dadosCompletosDesmascarados.rg : (this.formGroup.get('rg') ? this.formGroup.get('rg').value : null)),
        this.negociacaoService.codigoEvento,
        this.negociacaoService.usuarioResponsavel, this.negociacaoService.codigoRegistroAcessoPagina,
        this.freepass, null, false, null
      );
    } else {
      return this.getCadastroClienteJson();
    }
  }

  naoUsaSistemaInternacional(): boolean {
    try {
      return !(window.localStorage.getItem('usarSistemaInternacional') != undefined &&   window.localStorage.getItem('usarSistemaInternacional').toUpperCase() === 'TRUE');
    } catch (e) {
      return true;
    }
  }

  usaSistemaInternacional(): boolean {
    try {
      return (window.localStorage.getItem('usarSistemaInternacional') !== undefined
        &&   window.localStorage.getItem('usarSistemaInternacional').toUpperCase() === 'TRUE');
    } catch (e) {
      return true;
    }
  }

  public getLabelCampoObrigatorioOuOpcional(): string {
    try {
      if (this.menorIdade) {
        return 'global.campoopcional';
      } else {
        return 'global.campoobrigatorio';
      }
    } catch (e) {
      return 'global.campoobrigatorio';
    }
  }

  verificaMenorDeIdade(): void {
    if (this.formGroup.get('dataNascimento').value) {
      this.clienteService.menorIdade = false;
      const dataAtual = new Date();
      const [dia, mes, ano] = this.formGroup.get('dataNascimento').value.split('/').map(a => parseInt(a, 10));
      const dataNascimento = new Date(ano, mes - 1, dia);

      let idade = dataAtual.getFullYear() - dataNascimento.getFullYear();
      const m = dataAtual.getMonth() - dataNascimento.getMonth();

      if (m < 0 || (m === 0 && dataAtual.getDate() < dataNascimento.getDate())) {
        idade--;
      }

      if (idade < 18) {
        this.menorIdade = true;
        this.clienteService.menorIdade = true;
        localStorage.setItem('menor', 'true');
        Swal.fire({
          type: 'info',
          title: this.translateService.instant('checkout.aluno-menor-idade'),
          text: this.translateService.instant('checkout.por-favor-incluir-ao-menos-um-responsavel-continuar'),
          showConfirmButton: true,
        }).then(() => {
          //preenche os campos de responsável no front
          if (this.formGroup.get('responsavelMae')) {
            this.formGroup.get('responsavelMae').setValue(this.alunoService.alunoSelecionado.nomeResponsavelMae);
          }
          if (this.formGroup.get('cpfMae')) {
            this.formGroup.get('cpfMae').setValue(this.alunoService.alunoSelecionado.cpfResponsavelMae);
          }
          if (this.formGroup.get('responsavelPai')) {
            this.formGroup.get('responsavelPai').setValue(this.alunoService.alunoSelecionado.nomeResponsavelPai);
          }
          if (this.formGroup.get('cpfPai')) {
            this.formGroup.get('cpfPai').setValue(this.alunoService.alunoSelecionado.cpfResponsavelPai);
          }
        });
      } else {
        this.menorIdade = false;
        localStorage.setItem('menor', 'false');
      }
    }
  }

  verificaCpfs(campo): void {
    const cpf = this.formGroup.get('cpf');
    const cpfMae = this.formGroup.get('cpfMae');
    const cpfPai = this.formGroup.get('cpfPai');

    if (cpf.value !== null) {
      if (cpf.value === cpfMae.value) {
        this.alertSemelhancaCpfs(campo, cpfMae);
      } else if (cpf.value === cpfPai.value) {
        this.alertSemelhancaCpfs(campo, cpfPai);
      } else {
        if (campo === 1) {
          this.requiredCpf(cpfMae, cpfPai);
        } else {
          this.requiredCpf(cpfPai, cpfMae);
        }
      }
    }
  }

  alertSemelhancaCpfs(campo, cpf) {
    let responsavel: string;
    campo === 1 ? (responsavel = 'a Mãe', cpf.setValue('')) : (responsavel = 'o Pai', cpf.setValue(''));
    Swal.fire({
      type: 'error',
      title: 'CPF inválido!',
      text: 'O CPF d' + responsavel + ' deve ser diferente do aluno!',
      showConfirmButton: true,
    });
  }

  requiredCpf(cpf1, cpf2) {
    if ((cpf1.value === null || cpf1.value === '')) {
      if (cpf1.value === '') {
        cpf2.setValidators(Validators.required);
        cpf2.updateValueAndValidity();
      }
    } else {
      cpf2.clearValidators();
      cpf2.updateValueAndValidity();
    }
  }

  verificaResponsavel(campo) {
    const responsavelMae = this.formGroup.get('responsavelMae');
    const responsavelPai = this.formGroup.get('responsavelPai');

    if (campo === 1) {
      this.requiredResponsavel(responsavelMae, responsavelPai);
    } else {
      this.requiredResponsavel(responsavelPai, responsavelMae);
    }
  }

  requiredResponsavel(responsavel1, responsavel2) {
    if ((responsavel1.value === null || responsavel1.value === '')) {
      if (responsavel1.value === '') {
        responsavel2.setValidators(Validators.required);
        responsavel2.updateValueAndValidity();
      }
    } else {
      responsavel2.clearValidators();
      responsavel2.updateValueAndValidity();
    }
  }

  cpfAtivoEDifUnidade(dadosCpf): boolean {
    if (dadosCpf.empresa !== Number(this.negociacaoService.codunidade) && dadosCpf.situacao === 'Ativo') {
      return true;
    }
  }

  getMascaraCep() {
    return this.localizationService.getMascaraCep();
  }

  getMascaraDataNascimento() {
    return this.localizationService.getMascaraData();
  }

  isExibirCampo(campo: string): boolean {
    return this.getConfig() && this.getConfig().camposAdicionaisProduto.indexOf(campo) > -1;
  }

  prosseguirAgendamento() {
    const validado = this.clienteService.validar(this.getCadastroClienteJson(), this.empresaService.config);
    if (validado) {
      Swal.fire({
        type: 'warning',
        text: validado,
        showConfirmButton: true,
      });
      return;
    } else {
      window.localStorage.setItem('cliente', JSON.stringify(this.getCadastroClienteJson()));
      window.location.href = window.location.href.replace('cadastro?', 'agenda-aulas?linkVisitante=true&');
    }
  }

  private exibirMensagemRestricao(unidadesRestricoes: []) {
    this.formGroup.get('cpf').setValue('');

    let mensagem = `Seu cadastro está bloqueado devido a pendências na(s) unidade(s): #unidades.
                    Para resolver esta situação e reativar seus serviços, solicitamos que entre em contato com
                     a(s) referida(s) unidade(s) para regularizar suas pendências.`;

    let unidades = '';
    unidadesRestricoes.forEach((unidade) => {
      unidades += unidades === '' ? unidade : ', ' + unidade;
    });
    mensagem = mensagem.replace('#unidades', unidades);

    Swal.fire({
      type: 'warning',
      title: 'Aviso',
      text: mensagem,
      showConfirmButton: true
    });
  }
}
