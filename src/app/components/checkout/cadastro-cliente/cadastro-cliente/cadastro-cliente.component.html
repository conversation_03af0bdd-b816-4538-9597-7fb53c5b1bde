<div class="container" >
  <pacto-header [config]="getConfig()" [unidade]="getUnidadeSelecionada()"></pacto-header>

  <h1>{{"checkout.cadastre-agora"|translate}}</h1>

  <h4 *ngIf="!remetenteConviteMatricula">{{"checkout.informe-seus-dados-para-continuar"|translate}}:</h4>

  <h4 *ngIf="remetenteConviteMatricula">{{remetenteConviteNome}}{{"checkout.nome-remetente-convidado"|translate}}.</h4>
  <h4 *ngIf="remetenteConviteMatricula">{{"checkout.nome-remetente-convidado-msg-impacto"|translate}}!</h4>

  <div class="row">
    <div class="column col-md-7">
      <div class="row pacto-mg-top-medio">
        <div class="col-md-12">
          <pacto-input
            idinput="idnome"
            [label]="'checkout.nome-completo'|translate"
            [name]="'nome'"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio'|translate"
            [placeholder]="'checkout.digite-seu-nome-completo'|translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio" *ngIf="naoUsaSistemaInternacional()">
        <div class="col-md-12">
          <pacto-input
            idinput="idcpf"
            [label]="'checkout.cpf'|translate"
            [name]="'cpf'" [type]="'tel'"
            (focusout)="consultarPorCpf()"
            [textMask]="{mask: mascaracpf, guide: true}"
            [pactoFormGroup]="formGroup"
            [mensagem]="getLabelCampoObrigatorioOuOpcional() | translate"
            [placeholder]="'checkout.digite-seu-cpf'|translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>
      <div class="row pacto-mg-top-medio"
           *ngIf="isExibirCampo('RG')">
        <div class="col-md-12">
          <pacto-input
            idinput="idRG"
            [label]="'checkout.rg'|translate"
            [type]="'tel'"
            [name]="'rg'"
            [pactoFormGroup]="formGroup"
            [placeholder]="'checkout.digite-seu-rg'|translate"
            [mensagem]="getLabelCampoObrigatorioOuOpcional() | translate"
            [validators]="[Validators.required]"></pacto-input>
        </div>
      </div>
      <div class="row pacto-mg-top-medio"
           *ngIf="isExibirCampo('DT_NASCIMENTO')">
        <div class="col-md-12">
          <pacto-input
            idinput="idnascimento"
            [label]="'checkout.data-de-nascimento'|translate"
            [type]="'tel'"
            [name]="'dataNascimento'" (focusout)="verificaMenorDeIdade()"
            [textMask]="getMascaraDataNascimento()"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio'|translate"
            [placeholder]="'global.place-holder-data'|translate"
            [validators]="[Validators.required]"></pacto-input>
        </div>
      </div>
      <div class="row pacto-mg-top-medio"
           *ngIf="this.isExibirCampo('RESPONSAVEL_MAE') || this.menorIdade">
        <div class="col-md-12">
          <pacto-input idinput="idnomemae" [label]="'checkout.responsavel-mae'|translate" [name]="'responsavelMae'"
                       (focusout)="verificaResponsavel(1)" [pactoFormGroup]="formGroup" [mensagem]="'global.campoobrigatorio'|translate"
                       [placeholder]="'checkout.digite-o-nome-da-mae-ou-responsavel'|translate"
                       [validators]="[Validators.required]"></pacto-input>
        </div>
      </div>
      <div class="row pacto-mg-top-medio"
           *ngIf="this.isExibirCampo('CPF_RESPONSAVEL_MAE') || this.menorIdade">
        <div class="col-md-12">
          <pacto-input idinput="idcpfmae" [label]="'checkout.cpf-responsavel-mae'|translate" [name]="'cpfMae'" [type]="'tel'"
                       (focusout)="verificaCpfs(1)" [textMask]="{mask: mascaracpf, guide: true}" [pactoFormGroup]="formGroup"
                       [mensagem]="'global.campoobrigatorio'|translate" [placeholder]="'checkout.digite-o-cpf-responsavel-mae'|translate"
                       [validators]="[Validators.required]"></pacto-input>
        </div>
      </div>
      <div class="row pacto-mg-top-medio"
           *ngIf="isExibirCampo('RESPONSAVEL_PAI') || this.menorIdade">
        <div class="col-md-12">
          <pacto-input idinput="idnomepai" [label]="'checkout.responsavel-pai'|translate" [name]="'responsavelPai'"
                       (focusout)="verificaResponsavel(2)" [pactoFormGroup]="formGroup" [mensagem]="'global.campoobrigatorio'|translate"
                       [placeholder]="'checkout.digite-o-nome-do-pai-ou-responsavel'|translate"
                       [validators]="[Validators.required]"></pacto-input>
        </div>
      </div>
      <div class="row pacto-mg-top-medio"
           *ngIf="isExibirCampo('CPF_RESPONSAVEL_PAI') || this.menorIdade">
        <div class="col-md-12">
          <pacto-input idinput="idcpfpai" [label]="'checkout.cpf-responsavel-pai'|translate" [name]="'cpfPai'" [type]="'tel'"
                       (focusout)="verificaCpfs(2)" [textMask]="{mask: mascaracpf, guide: true}"
                       [pactoFormGroup]="formGroup" [mensagem]="'global.campoobrigatorio'|translate"
                       [placeholder]="'checkout.digite-o-cpf-responsavel-pai'|translate"
                       [validators]="[Validators.required]"></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio"
           *ngIf="isExibirCampo('SEXO')">
        <div class="col-md-12">
          <pacto-input
            [opcoes]="opcaoSexo()"
            [labelOption]="'label'"
            [valueOption]="'id'"
            [type]="'text'"
            [label]="'checkout.sexo'|translate"
            [name]="'sexo'"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio'|translate"
            [placeholder]="'checkout.digite-seu-sexo'|translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio">
        <div class="col-md-12">
          <pacto-input
            idinput="idemail"
            [label]="'checkout.e-mail'|translate"
            [name]="'email'"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio'|translate"
            [placeholder]="'checkout.digite-seu-melhor-e-mail'|translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio"
           *ngIf="isExibirCampo('TELEFONE')">
        <div class="col-md-12">
          <pacto-input
            idinput="idtelefone"
            [label]="'checkout.telefone'|translate" [type]="'tel'"
            [name]="'telefone'"
            [textMask]="mascaraTelefone ? {mask: mascaraTelefone, guide: false} : { mask: false }"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio'|translate"
            [placeholder]="'checkout.digite-seu-telefone'|translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio" *ngIf="isExibirCampo('CEP')">
        <div class="col-md-12" (keyup.enter)="consultarCep()">
          <pacto-input
            [label]="'checkout.cep'|translate" [type]="'tel'"
            [name]="'cep'"
            (focusout)="consultarCep()"
            [textMask]="getMascaraCep()"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio'|translate"
            [placeholder]="'checkout.digite-seu-cep'|translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio"
           *ngIf="isExibirCampo('ENDERECO')">
        <div class="col-md-12">
          <pacto-input
            [label]="'checkout.endereco'|translate"
            [name]="'endereco'"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio'|translate"
            [placeholder]="'checkout.digite-seu-endereco'|translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio" *ngIf="isExibirCampo('NUMERO')">
        <div class="col-md-12">
          <pacto-input
            [label]="'checkout.numero-endereco'|translate"
            [name]="'numero'"
            [pactoFormGroup]="formGroup"
            [maxlength]="10"
            [mensagem]="'global.campoobrigatorio'|translate"
            [placeholder]="'checkout.digite-o-numero-da-sua-residencia'|translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio" *ngIf="isExibirCampo('BAIRRO')">
        <div class="col-md-12">
          <pacto-input
            [label]="'checkout.bairro'|translate"
            [name]="'bairro'"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio'|translate"
            [placeholder]="'checkout.digite-o-bairro-da-sua-residencia'|translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio"
           *ngIf="isExibirCampo('COMPLEMENTO')">
        <div class="col-md-12">
          <pacto-input
            [label]="'checkout.complemento'|translate"
            [name]="'complemento'"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio'|translate"
            [placeholder]="'checkout.digite-o-complemento-do-seu-endereco'|translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>
    </div>

    <div class="column col-md-5 colunadados">
      <div class="colunafixa">
        <span class="resumo">{{"checkout.empresa-a-ser-cadastrada"|translate}}</span>
        <pacto-unidade-selecionada></pacto-unidade-selecionada>
      </div>
    </div>

    <div class="column col-md-7 colunadados">
      <a class="pacto-btn-primary"
         *ngIf="!getConfig().habilitarAgendamentoAulaExperimentalLinkVisitante"
         [style.background-color]="'#25d366'"
         (click)="finalizarCadastro()" id="idfinalizar">
        {{"checkout.finalizar-cadastro"|translate}} >>
      </a>
      <a class="pacto-btn-primary"
         *ngIf="getConfig().habilitarAgendamentoAulaExperimentalLinkVisitante"
         [style.background-color]="'#25d366'"
         (click)="prosseguirAgendamento()" id="idfinalizar">
        {{"checkout.agendar-aula-experimental"|translate}} >>
      </a>
    </div>

    <div class="column col-md-7 colunadados" style="padding-top: 20px;">
    </div>

  </div>
</div>


