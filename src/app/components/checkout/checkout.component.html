<div class="container" *ngIf="config && (planoSelecionado?.codigo > 0 || produtosSelecionados.length > 0 || existeLocacaoSelecionada)">
  <pacto-header [config]="config" [unidade]="unidadeSelecionada"></pacto-header>
  <div class="row">
    <h1 class="col-md-7 order-2 order-md-1" *ngIf="estaNaTelaDePagamentoPixAposFluxoDeAdicionarCartaoSucesso" >{{ 'checkout.cartao-adicionado-agora-pagar-pix' | translate }}</h1>
    <h1 class="col-md-7 order-2 order-md-1" *ngIf="!estaNaTelaDePagamentoPixAposFluxoDeAdicionarCartaoSucesso" >{{ 'checkout.cadastre-se-para-comprar' | translate }}</h1>
    <h4 class="col-md-7 order-3 order-md-2" *ngIf="!estaNaTelaDePagamentoPixAposFluxoDeAdicionarCartaoSucesso" >{{ 'checkout.informe-seus-dados-para-continuar' | translate }}:</h4>
    <div class="col-md-7 order-4 order-md-3" >
      <div class="row pacto-mg-top-medio">
        <div class="col-md-12" *ngIf="!estaNaTelaDePagamentoPixAposFluxoDeAdicionarCartaoSucesso">
          <pacto-input
            idinput="idnome"
            [label]="labelCampoNome | translate"
            [name]="'nome'"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio' | translate"
            [placeholder]="placeholderCampoNome | translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio" *ngIf="planoSelecionado && apresentarTipoDocumento">
        <div class="col-md-12">
          <pacto-input
            [opcoes]="opcoesTipoDeDocumento"
            labelOption="label"
            valueOption="id"
            [default]="tipoDocumento"
            [label]="'checkout.tipo-de-documento' | translate"
            [name]="'tipoDeDocumento'"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio' | translate"
            [validators]="[Validators.required]"
            (change)="alterouTipoDocumento()"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio" *ngIf="!usaSistemaInternacional() && tipoDocumento === 'CPF' && !estaNaTelaDePagamentoPixAposFluxoDeAdicionarCartaoSucesso">
        <div class="col-md-12">
          <pacto-input
            idinput="idcpf"
            [label]="'checkout.cpf' | translate"
            [name]="'cpf'"
            [type]="'tel'"
            [default]="formGroup.get('cpf')?.value"
            (focusout)="consultarPorCpf(false, 0)"
            [textMask]="{ mask: mascaracpf, guide: true }"
            [pactoFormGroup]="formGroup"
            [mensagem]="getLabelCampoObrigatorioOuOpcional() | translate"
            [placeholder]="'checkout.digite-seu-cpf' | translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio" *ngIf="!usaSistemaInternacional() && tipoDocumento === 'CNPJ'">
        <div class="col-md-12">
          <pacto-input
            idinput="idCnpj"
            [label]="'CNPJ'"
            [name]="'cnpj'"
            [type]="'tel'"
            [default]="formGroup.get('cnpj')?.value"
            (focusout)="consultarPorCnpj()"
            [textMask]="{ mask: mascaraCnpj, guide: true }"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio' | translate"
            [placeholder]="'checkout.digite-seu-cnpj' | translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio" *ngIf="!usaSistemaInternacional() && tipoDocumento === 'CNPJ'">
        <div class="col-md-12">
          <pacto-input
            idinput="idInputResponsavelEmpresa"
            [label]="'checkout.nome-responsavel-empresa' | translate"
            [name]="'nomeResponsavelEmpresa'"
            [pactoFormGroup]="formGroup"
            [default]="formGroup.get('nomeResponsavelEmpresa')?.value"
            [placeholder]="'checkout.digite-seu-nome-completo' | translate"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio" *ngIf="!usaSistemaInternacional() && tipoDocumento === 'CNPJ'">
        <div class="col-md-12">
          <pacto-input
            idinput="idInputCpfResponsavelEmpresa"
            [label]="'checkout.cpf-responsavel-empresa' | translate"
            [name]="'cpfResponsavelEmpresa'"
            [textMask]="{ mask: mascaracpf, guide: true }"
            [pactoFormGroup]="formGroup"
            [default]="formGroup.get('cpfResponsavelEmpresa')?.value"
            [placeholder]="'checkout.digite-seu-cpf' | translate"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio"*ngIf="isExibirCampo('RG') && !usaSistemaInternacional()">
        <div class="col-md-12">
          <pacto-input
            idinput="idrg"
            [label]="'checkout.rg' | translate"
            [type]="'tel'"
            [name]="'rg'"
            [pactoFormGroup]="formGroup"
            [mensagem]="getLabelCampoObrigatorioOuOpcional() | translate"
            [placeholder]="'checkout.digite-o-seu-rg' | translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio" *ngIf="isExibirCampo('DT_NASCIMENTO')">
        <div class="col-md-12">
          <pacto-input
            #dataNascimento
            idinput="idnascimento"
            [label]="'checkout.data-de-nascimento' | translate"
            [type]="'tel'"
            [name]="'dataNascimento'"
            (focusout)="preencherNascimentoTurmaVerificandoIdade()"
            [textMask]="mascaraData"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio' | translate"
            [placeholder]="'global.place-holder-data' | translate"
            [validators]="[Validators.required]"
            [enableShowError]="enableShowErrorInput"
            [disabled]="naoPermiteAlterarDataMenorDeIdade"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio" *ngIf="isExibirCampo('RESPONSAVEL_MAE') || menorIdade">
        <div class="col-md-12">
          <pacto-input
            idinput="idnomemae"
            [label]="'checkout.responsavel-mae' | translate"
            [name]="'responsavelMae'"
            (focusout)="verificaResponsavel(1)"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio' | translate"
            [placeholder]="'checkout.digite-o-nome-da-mae-ou-responsavel' | translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio" *ngIf="isExibirCampo('CPF_RESPONSAVEL_MAE') || menorIdade">
        <div class="col-md-12">
          <pacto-input
            idinput="idcpfmae"
            [label]="'checkout.cpf-responsavel-mae' | translate"
            [name]="'cpfMae'"
            [type]="'tel'"
            (focusout)="verificaCpfs(1)"
            [textMask]="{ mask: mascaracpf, guide: true }"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio' | translate"
            [placeholder]="'checkout.digite-o-cpf-responsavel-mae' | translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio" *ngIf="isExibirCampo('RESPONSAVEL_PAI') || menorIdade">
        <div class="col-md-12">
          <pacto-input
            idinput="idnomepai"
            [label]="'checkout.responsavel-pai' | translate"
            [name]="'responsavelPai'"
            (focusout)="verificaResponsavel(2)"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio' | translate"
            [placeholder]="'checkout.digite-o-nome-do-pai-ou-responsavel' | translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio" *ngIf="isExibirCampo('CPF_RESPONSAVEL_PAI') || menorIdade">
        <div class="col-md-12">
          <pacto-input
            idinput="idcpfpai"
            [label]="'checkout.cpf-responsavel-pai' | translate"
            [name]="'cpfPai'"
            [type]="'tel'"
            (focusout)="verificaCpfs(2)"
            [textMask]="{ mask: mascaracpf, guide: true }"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio' | translate"
            [placeholder]="'checkout.digite-o-cpf-responsavel-pai' | translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio" *ngIf="isExibirCampo('SEXO')">
        <div class="col-md-12">
          <pacto-input
            idinput="idsexo"
            [opcoes]="opcaoSexo()"
            [labelOption]="'label'"
            [valueOption]="'id'"
            [type]="'text'"
            [label]="'checkout.sexo' | translate"
            [name]="'sexo'"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio' | translate"
            [placeholder]="'checkout.digite-seu-sexo' | translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-mg-top-medio" *ngIf="!estaNaTelaDePagamentoPixAposFluxoDeAdicionarCartaoSucesso">
        <div class="col-md-12">
          <pacto-input
            idinput="idemail"
            [label]="'checkout.e-mail' | translate"
            [name]="'email'"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio' | translate"
            [placeholder]="'checkout.digite-seu-melhor-e-mail' | translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>
      <div class="row pacto-mg-top-medio" *ngIf="isExibirCampo('TELEFONE')">
        <div class="col-md-12">
          <pacto-input
            idinput="idtelefone"
            [label]="labelCampoTelefone | translate"
            [type]="'tel'"
            [name]="'telefone'"
            [textMask]="mascaraTelefone ? { mask: mascaraTelefone, guide: false } : { mask: false }"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio' | translate"
            [placeholder]="'checkout.digite-seu-telefone' | translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>
      <div class="row pacto-mg-top-medio" *ngIf="isExibirCampo('CEP')">
        <div class="col-md-12" (keyup.enter)="consultarCep()">
          <pacto-input
            [label]="'checkout.cep' | translate"
            [type]="'tel'"
            [name]="'cep'"
            (focusout)="consultarCep()"
            [textMask]="mascaraCep"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio' | translate"
            [placeholder]="'checkout.digite-seu-cep' | translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>
      <div class="row pacto-mg-top-medio" *ngIf="isExibirCampo('ENDERECO')">
        <div class="col-md-12">
          <pacto-input
            [label]="'checkout.endereco' | translate"
            [name]="'endereco'"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio' | translate"
            [placeholder]="'checkout.digite-seu-endereco' | translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>
      <div class="row pacto-mg-top-medio" *ngIf="isExibirCampo('NUMERO')">
        <div class="col-md-12">
          <pacto-input
            [label]="'checkout.numero-endereco' | translate"
            [name]="'numero'"
            [pactoFormGroup]="formGroup"
            [maxlength]="10"
            [mensagem]="'global.campoobrigatorio' | translate"
            [placeholder]="'checkout.digite-o-numero-da-sua-residencia' | translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>
      <div class="row pacto-mg-top-medio" *ngIf="isExibirCampo('BAIRRO')">
        <div class="col-md-12">
          <pacto-input
            [label]="'checkout.bairro' | translate"
            [name]="'bairro'"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio' | translate"
            [placeholder]="'checkout.digite-o-bairro-da-sua-residencia' | translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>
      <div class="row pacto-mg-top-medio" *ngIf="isExibirCampo('COMPLEMENTO')">
        <div class="col-md-12">
          <pacto-input
            [label]="'checkout.complemento' | translate"
            [name]="'complemento'"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio' | translate"
            [placeholder]="'checkout.digite-o-complemento-do-seu-endereco' | translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>
      <div
        class="row pacto-mg-top-medio"
        *ngIf="isExibirCampo('DIA_VENCIMENTO') && planoSelecionado"
      >
        <div class="col-md-12">
          <i
            *ngIf="widthNavegador"
            class="tem pct-info align-icon-info-dia-vencimento"
            title="Informe o dia de vencimento das suas próximas parcelas. Lembrando que a primeira parcela será cobrada agora no ato da compra e as demais terão o vencimento no dia informado aqui."
          ></i>
          <pacto-input
            [opcoes]="planoSelecionado.diasVencimento"
            [label]="'checkout.dia-de-vencimento-da-mensalidade' | translate"
            [name]="'diavencimento'"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio' | translate"
            [validators]="[Validators.required]"
            (change)="setarPlano(getVendaJsonDecrypt())"
          ></pacto-input>
        </div>
      </div>
      <div class="row pacto-mg-top-medio" *ngIf="exibeCampoDataUtilizacao && existeProdutoDiariaSelecionado()">
          <div class="col-md-12">
            <pacto-input
            #dataUtilizacao
            idinput="idDataUtilizacao"
            [label]="'checkout.data-de-utilizacao' | translate"
            [name]="'dataUtilizacao'"
            (focusout)="onFocusOutDataUtilizacao()"
            [textMask]="mascaraData"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio' | translate"
            [placeholder]="'global.place-holder-data' | translate"
            [validators]="[Validators.required]"
            [enableShowError]="enableShowErrorInput"
            [disabled]="naoPermiteAlterarDataMenorDeIdade"
          ></pacto-input>
          </div>
      </div>
      <div class="row pacto-mg-top-medio" *ngIf="!cobrancaGeradaComSucesso">
        <div *ngIf="isExibirCampo('CUPOM_DESCONTO')" class="col-md-5" (keyup.enter)="addCupomEnter()">
          <pacto-input
            idinput="numeroCupomDesconto"
            [label]="'checkout.cupom-desconto' | translate"
            [name]="'cupomdesconto'"
            [default]="cupomParam"
            [pactoFormGroup]="formGroup"
            autocomplete="off"
            mensagem="Este campo é opcional"
            [placeholder]="'checkout.informe-seu-cupom-desconto' | translate"
          ></pacto-input>
        </div>
        <span class="validar" style="margin-top: 32px;" *ngIf="widthNavegador">
          <span *ngIf="isExibirCampo('CUPOM_DESCONTO')" class="btn" style="margin-left: 10px; margin-right: 4px; text-align: center; font-size: 20px; line-height: 5vh; width: auto !important;">
            <a (click)="validarCupom(formGroup.get('cupomdesconto').value)">
              {{ 'checkout.adicionar-cupom' | translate }}
              <span class="detalhes"><i class="pct-tag"></i> </span>
            </a>
          </span>
        </span>
        <span [className]="!cupom ? 'validar' : 'validar-red'" *ngIf="!widthNavegador">
          <span *ngIf="isExibirCampo('CUPOM_DESCONTO')" class="btn">
            <a *ngIf="!cupom" (click)="validarCupom(formGroup.get('cupomdesconto').value)">
              {{ 'checkout.adicionar-cupom' | translate }}
              <span class="detalhes"><i class="pct-tag"></i> </span>
            </a>
            <a *ngIf="cupom" (click)="limparCupom()">{{ 'checkout.remover' | translate }}
              <span class="detalhes"><i class="pct-tag"></i> </span>
            </a>
          </span>
        </span>
      </div>
      <!--      CUPOM DESCONTO-->

      <!-- COMPARTILHAMENTO DE PLANO --->
      <div *ngIf="planoSelecionado && planoSelecionado.permitirCompartilharPLanoNoSite && planoSelecionado.quantidadeCompartilhamentos >= 1">
        <div style="display: flex; align-items: center; gap: 10px">
          <pacto-input [type]="'checkbox'"
                       [name]="'compartilharPlano'"
                       [disabled]="clienteTitularJaPossuiDependentes"
                       (change)="toggleCompartilharPlanoCheckbox();"
                       style="accent-color: dodgerblue; border-color: dodgerblue; box-shadow: none; padding: 0; margin: 0"
          >
          </pacto-input>
          <span style="margin-top: 5px">{{ 'compartilhar-plano.checkbox-text' | translate }}</span>
        </div>
        <div *ngIf="!clienteTitularJaPossuiDependentes" style="color: #797d86; display: flex; align-items: center; gap: 10px; padding: 10px; background-color: #f4f4f4; border-radius: 5px">
          <i class="pct-info"></i>
          <p style="line-height: 1.5"></p>
          <span>O plano escolhido permite que você traga amigos ou familiares para treinar junto com você. Você pode compartilhar com <span style="font-weight: 700">{{planoSelecionado.quantidadeCompartilhamentos}} {{planoSelecionado.quantidadeCompartilhamentos > 1 ? 'pessoas' : 'pessoa' }}</span>. Basta permitir o compartilhamento e fornecer os dados dessa pessoa para que ela receba o benefício.</span>
        </div>
        <div *ngIf="clienteTitularJaPossuiDependentes" style="color: #672B2A; display: flex; align-items: center; gap: 10px; padding: 10px; background-color: #FDEDED; border-radius: 5px">
          <i class="pct-info" style="color: #D74141"></i>
          <p style="line-height: 1.5"></p>
          <span>Desculpe, não é possível compartilhar o plano porque já existe um compartilhamento com esse aluno.</span>
        </div>

        <div *ngIf="exibirCamposCompartilhamentoPlano && !clienteTitularJaPossuiDependentes">
          <div *ngFor="let c of dadosPessoasCompartilhamentos; let i = index" class="pessoas-compartilhamento-plano-container">
            <div id="campos-compartilhamento-{{i + 1}}" class="pessoas-compartilhamento-plano-container">
              <div style="display: flex; flex-direction: column; gap: 5px;">
                <span style="color:#2E3133; font-weight: 700;">Parceiro de compartilhamento {{i + 1}}</span>
                <div style="border: 1px solid darkgray"></div>
              </div>
              <pacto-input
                idinput="idnome_comp_{{i}}"
                [label]="labelCampoNome | translate"
                [name]="'nome_comp' + i"
                [pactoFormGroup]="formGroup"
                [mensagem]="'global.campoobrigatorio' | translate"
                [placeholder]="'compartilhar-plano.campo-nome-placeholder' | translate"
                [validators]="[Validators.required]"
              ></pacto-input>
              <pacto-input
                idinput="idcpf_comp_{{i}}"
                (focusout)="consultarPorCpf(true, i)"
                [label]="'checkout.cpf' | translate"
                [name]="'cpf_comp' + i"
                [type]="'tel'"
                [textMask]="{ mask: mascaracpf, guide: true }"
                [pactoFormGroup]="formGroup"
                [mensagem]="'global.campoobrigatorio' | translate"
                [placeholder]="'Digite o CPF da pessoa que receberá o plano'"
                [validators]="[Validators.required]"
              ></pacto-input>
              <pacto-input
                idinput="idemail_comp_{{i}}"
                [label]="'checkout.e-mail' | translate"
                [name]="'email_comp' + i"
                [pactoFormGroup]="formGroup"
                [mensagem]="'global.campoobrigatorio' | translate"
                [placeholder]="'compartilhar-plano.campo-email-placeholder' | translate"
                [validators]="[Validators.required]"
              ></pacto-input>
              <div *ngIf="isExibirCampoPessoaCompartilhamento('RG') && !usaSistemaInternacional()">
                <pacto-input
                  idinput="idrg_comp_{{i}}"
                  [label]="'checkout.rg' | translate"
                  [type]="'tel'"
                  [name]="'rg_comp' + i"
                  [pactoFormGroup]="formGroup"
                  [mensagem]="'global.campoobrigatorio' | translate"
                  [placeholder]="'Digite o RG da pessoa que terá acesso ao plano'"
                  [validators]="[Validators.required]"
                ></pacto-input>
              </div>
              <div *ngIf="isExibirCampoPessoaCompartilhamento('RESPONSAVEL_PAI')">
                <pacto-input
                  idinput="idnomepai_comp_{{i}}"
                  [label]="'checkout.responsavel-pai' | translate"
                  [name]="'responsavelPai_comp'  + i"
                  (focusout)="verificaResponsavel(2)"
                  [pactoFormGroup]="formGroup"
                  [mensagem]="'global.campoobrigatorio' | translate"
                  [placeholder]="'Nome do pai ou responsável da pessoa que receberá o plano'"
                  [validators]="[Validators.required]"
                ></pacto-input>
              </div>
              <div *ngIf="isExibirCampoPessoaCompartilhamento('CPF_RESPONSAVEL_PAI')">
                <pacto-input
                  idinput="idcpfpai_comp_{{i}}"
                  [label]="'checkout.cpf-responsavel-pai' | translate"
                  [name]="'cpfPai_comp' + i"
                  [type]="'tel'"
                  (focusout)="verificaCpfs(2)"
                  [textMask]="{ mask: mascaracpf, guide: true }"
                  [pactoFormGroup]="formGroup"
                  [mensagem]="'global.campoobrigatorio' | translate"
                  [placeholder]="'checkout.digite-o-cpf-responsavel-pai' | translate"
                  [validators]="[Validators.required]"
                ></pacto-input>
              </div>
              <div *ngIf="isExibirCampoPessoaCompartilhamento('RESPONSAVEL_MAE')">
                <pacto-input
                  idinput="idnomemae_comp_{{i}}"
                  [label]="'checkout.responsavel-mae' | translate"
                  [name]="'responsavelMae_comp' + i"
                  (focusout)="verificaResponsavel(1)"
                  [pactoFormGroup]="formGroup"
                  [mensagem]="'global.campoobrigatorio' | translate"
                  [placeholder]="'Nome da mãe ou responsável da pessoa que receberá o plano'"
                  [validators]="[Validators.required]"
                ></pacto-input>
              </div>
              <div *ngIf="isExibirCampoPessoaCompartilhamento('CPF_RESPONSAVEL_MAE') || menorIdade">
                <pacto-input
                  idinput="idcpfmae_comp_{{i}}"
                  [label]="'checkout.cpf-responsavel-mae' | translate"
                  [name]="'cpfMae_comp' + i"
                  [type]="'tel'"
                  (focusout)="verificaCpfs(1)"
                  [textMask]="{ mask: mascaracpf, guide: true }"
                  [pactoFormGroup]="formGroup"
                  [mensagem]="'global.campoobrigatorio' | translate"
                  [placeholder]="'checkout.digite-o-cpf-responsavel-mae' | translate"
                  [validators]="[Validators.required]"
                ></pacto-input>
              </div>
              <div *ngIf="isExibirCampoPessoaCompartilhamento('DT_NASCIMENTO')">
                <pacto-input
                  idinput="idnascimento_comp_{{i}}"
                  [label]="'checkout.data-de-nascimento' | translate"
                  [type]="'tel'"
                  [name]="'dataNascimento_comp' + i"
                  [textMask]="mascaraData"
                  [pactoFormGroup]="formGroup"
                  [mensagem]="'global.campoobrigatorio' | translate"
                  [placeholder]="'global.place-holder-data' | translate"
                  [validators]="[Validators.required]"
                ></pacto-input>
              </div>
              <div *ngIf="isExibirCampoPessoaCompartilhamento('TELEFONE')">
                <pacto-input
                  idinput="idtelefone_comp_{{i}}"
                  [label]="labelCampoTelefone | translate"
                  [type]="'tel'"
                  [name]="'telefone_comp' + i"
                  [textMask]="mascaraTelefone ? { mask: mascaraTelefone, guide: false } : { mask: false }"
                  [pactoFormGroup]="formGroup"
                  [mensagem]="'global.campoobrigatorio' | translate"
                  [placeholder]="'checkout.digite-seu-telefone' | translate"
                  [validators]="[Validators.required]"
                ></pacto-input>
              </div>
              <div *ngIf="isExibirCampoPessoaCompartilhamento('CEP')">
                <div (keyup.enter)="consultarCep()">
                  <pacto-input
                    [label]="'checkout.cep' | translate"
                    [type]="'tel'"
                    [name]="'cep_comp' + i"
                    (focusout)="consultarCep()"
                    [textMask]="mascaraCep"
                    [pactoFormGroup]="formGroup"
                    [mensagem]="'global.campoobrigatorio' | translate"
                    [placeholder]="'checkout.digite-seu-cep' | translate"
                    [validators]="[Validators.required]"
                  ></pacto-input>
                </div>
              </div>
              <div *ngIf="isExibirCampoPessoaCompartilhamento('ENDERECO')">
                <pacto-input
                  [label]="'checkout.endereco' | translate"
                  [name]="'endereco_comp' + i"
                  [pactoFormGroup]="formGroup"
                  [mensagem]="'global.campoobrigatorio' | translate"
                  [placeholder]="'checkout.digite-seu-endereco' | translate"
                  [validators]="[Validators.required]"
                ></pacto-input>
              </div>
              <div *ngIf="isExibirCampoPessoaCompartilhamento('NUMERO')">
                <pacto-input
                  [label]="'checkout.numero-endereco' | translate"
                  [name]="'numero_comp' + i"
                  [pactoFormGroup]="formGroup"
                  [maxlength]="10"
                  [mensagem]="'global.campoobrigatorio' | translate"
                  [placeholder]="'checkout.digite-o-numero-da-sua-residencia' | translate"
                  [validators]="[Validators.required]"
                ></pacto-input>
              </div>
              <div *ngIf="isExibirCampoPessoaCompartilhamento('BAIRRO')">
                <pacto-input
                  [label]="'checkout.bairro' | translate"
                  [name]="'bairro_comp' + i"
                  [pactoFormGroup]="formGroup"
                  [mensagem]="'global.campoobrigatorio' | translate"
                  [placeholder]="'checkout.digite-o-bairro-da-sua-residencia' | translate"
                  [validators]="[Validators.required]"
                ></pacto-input>
              </div>
              <div *ngIf="isExibirCampoPessoaCompartilhamento('COMPLEMENTO')">
                <pacto-input
                  [label]="'checkout.complemento' | translate"
                  [name]="'complemento_comp' + i"
                  [pactoFormGroup]="formGroup"
                  [mensagem]="'global.campoobrigatorio' | translate"
                  [placeholder]="'checkout.digite-o-complemento-do-seu-endereco' | translate"
                  [validators]="[Validators.required]"
                ></pacto-input>
              </div>
                <div *ngIf="isExibirCampoPessoaCompartilhamento('SEXO')">
                  <pacto-input
                    idinput="idsexo_comp_{{i}}"
                    [opcoes]="opcaoSexo()"
                    [labelOption]="'label'"
                    [valueOption]="'id'"
                    [type]="'text'"
                    [label]="'checkout.sexo' | translate"
                    [name]="'sexo_comp' + i"
                    [pactoFormGroup]="formGroup"
                    [mensagem]="'global.campoobrigatorio' | translate"
                    [placeholder]="'checkout.digite-seu-sexo' | translate"
                    [validators]="[Validators.required]"
                  ></pacto-input>
                </div>
            </div>
          </div>
        </div>

      </div>
      <!-- --->

      <div *ngIf="cupom && !widthNavegador" class="sub-plano-produto-inclui">
        <span class="desconto">{{ 'checkout.desconto-aplicado' | translate }}:</span>
        <span *ngFor="let premios of cupom.listaPremios">
          <div *ngIf="planoSelecionado.nome.toUpperCase() == premios.descricaoPlano.toUpperCase()">
            <span class="modalidade">{{ premios.descricaoPremio }}:</span>
            <span *ngIf="premios.percentualDesconto != 0.0" class="valor-desc">{{ premios.percentualDesconto }}%</span>
            <span *ngIf="premios.valorDesconto != 0.0" class="valor-desc">
              {{ premios.valorDesconto | currency : moeda : 'symbol' }}
            </span>
          </div>
        </span>
      </div>
      <div *ngIf="isExibirCampo('ParQ') && !estaNaTelaDePagamentoPixAposFluxoDeAdicionarCartaoSucesso" style="margin-left: 10px">
        <h4 class="center">Par-Q</h4>
        <span class="sucesso-parq" *ngIf="parqValidado">Formulario Par-Q preenchido e validado com sucesso.</span>
        <div *ngIf="!parqValidado">
        <span *ngFor="let pergunta of parq?.perguntas">
          <div class="pergunta">
            <div class="row mtPequeno"><label class="lblDestaque">{{pergunta.descricao}}</label></div>
            <div class="row" *ngIf="pergunta.tipoPergunta === 'SN' || pergunta.tipoPergunta === 'SIM_NAO'">
              <div>
                <span *ngFor="let resposta of pergunta.respostas">
                  <input
                    [formControl]="fcResposta"
                    class="form-check-input" name="{{ '_' + pergunta.codigo }}"
                    type="radio" id="{{ pergunta.codigo + '_' + resposta.codigo }}" value="{{ resposta.codigo }}"
                    (click)="check($event)">
                <label class="form-check-label"
                       for="{{ pergunta.codigo + '_' + resposta.codigo }}"> {{ resposta.descricao }} </label>
                </span>
              </div>
            </div>
          </div>
        </span>
          <div *ngIf="isApresentarLeiParqRJ() && !parqValidado" style="margin-left: 10px; font-size: 10px">
            <h4 class="center">LEI Nº 6765 DE 05 DE MAIO DE 2014.</h4>
            <div>DISPÕE SOBRE A PRÁTICA DE ATIVIDADES FÍSICAS E ESPORTIVAS EM CLUBES, ACADEMIAS E ESTABELECIMENTOS SIMILARES, E DÁ OUTRAS PROVIDÊNCIAS. </div>
            <div> O GOVERNADOR DO ESTADO DO RIO DE JANEIRO </div>
            <div> Faço saber que a Assembleia Legislativa do Estado do Rio de Janeiro decreta e eu sanciono a seguinte Lei: </div>
            <div style="margin-top: 10px"> Art. 1º Considera-se obrigatório e imprescindível, para a prática de qualquer atividade física e esportiva, em clubes, academias e estabelecimentos similares, o preenchimento, pelo interessado, do Questionário de Prontidão para Atividade Física constante do Anexo I e do Termo de Responsabilidade para a Prática de Atividade Física constante do Anexo II desta Lei. </div>
            <div>  Parágrafo único. Se o interessado for menor de idade, o Questionário e o Termo de Responsabilidade deverão ser preenchidos e assinados pelo responsável legal, juntamente com sua autorização por escrito. </div>
            <div style="margin-top: 10px">  Art. 2º Fica dispensada a apresentação de atestado médico ou a obrigatoriedade de qualquer outro exame de aptidão física aos interessados que responderem negativamente a todas as perguntas do Questionário de Prontidão para Atividade Física. </div>
            <div>  Parágrafo único. Aos que responderem positivamente a qualquer uma das perguntas do Questionário, será exigida a apresentação de atestado médico de aptidão física, na forma das Leis Estaduais nº 2.014, de 15 de julho de 1992, e 2.835, de 17 de novembro de 1997, o qual deverá ser anotado e arquivado junto ao prontuário do interessado. </div>
            <div style="margin-top: 10px">  Art. 3º Esta lei entra em vigor na data de sua publicação. </div>
            <div style="margin-top: 15px">  Rio de Janeiro, 05 de maio de 2014. </div>
            <div>   LUIZ FERNANDO DE SOUZA </div>
            <div style="margin-bottom: 20px">   Governador </div>
          </div>
          <div *ngIf="isApresentarLeiParqGO() && !parqValidado" style="margin-left: 10px; font-size: 10px">
            <h4 class="center">LEI N 20.630, DE 08 DE NOVEMBRO DE 2019.</h4>
            <div>
              Obriga, para a prática de qualquer atividade física e esportiva, o preenchimento do
              documento que especifica e dá outras providências.
            </div>
            <div>O GOVERNADOR DO ESTADO DE GOIÁS</div>
            <div>
              A ASSEMBLEIA LEGISLATIVA DO ESTADO DE GOIÁS, nos termos do art. 10 da Constituição Estadual, decreta e eu
              sanciono a seguinte Lei:
            </div>
            <div style="margin-top: 10px">
              Art. 1º É obrigatório, para a prática de qualquer atividade física e esportiva, em clubes, academias e estabelecimentos
              similares, o preenchimento, pelo interessado, do Questionário de Prontidão para Atividade Física constante do Anexo Único desta Lei.
            </div>
            <div>
              Parágrafo único. Se o interessado for menor de idade, o Questionário de Prontidão para Atividade Física deverá ser
              preenchido e assinado pelo responsável legal, juntamente com sua autorização por escrito.
            </div>
            <div style="margin-top: 10px">
              Art. 2° Somente aos que responderem positivamente a qualquer uma das perguntas do Questionário será exigida a
              apresentação de atestado médico de aptidão física.
            </div>
            <div style="margin-top: 10px">
              Art. 3° Fica revogada a Lei nº 12.881, de 03 de junho de 1996.
            </div>
            <div style="margin-top: 10px">
              Art. 4º Esta Lei entra em vigor na data de sua publicação
            </div>
            <div style="margin-top: 15px">
              Goiânia, 08 de novembro de 2019.
            </div>
            <div>
              RONALDO RAMOS CAIADO
            </div>
            <div style="margin-bottom: 20px">
              Governador
            </div>
          </div>
          <div class="rowValidarParq">
        <span class="validar-parq" *ngIf="widthNavegador">
          <span class="btn">
            <a (click)="validarParq()">
              {{ 'checkout.validar-parq' | translate }}
            </a>
          </span>
          <span class="detalhes"><i class="pct-arrow-right"></i> </span>
        </span>
            <span [className]="'validar-parq'" *ngIf="!widthNavegador">
          <span class="btn">
            <a (click)="validarParq()">
              {{ 'checkout.validar-parq' | translate }}
            </a>
          </span>
          <span class="detalhes"><i class="pct-arrow-right"></i> </span>
        </span>
          </div>
        </div>
      </div>
      <div class="row pacto-mg-top-grande" *ngIf="!isExibirCampo('ParQ') || parqValidado">
        <div class="col-md-7" *ngIf="formaNenhuma()">
          <h4>Selecione a forma de pagamento</h4>
        </div>
      </div>
      <div class="row" *ngIf="!isExibirCampo('ParQ') || parqValidado">
        <div class="col-12" style="padding-bottom: 10px">
          <div class="row space" style="padding-left: 15px">
            <div *ngIf="apresentarCartaoVenda && !cobrancaGeradaComSucesso" style="padding-right: 20px">
              <img class="align-icon-cartao" src="assets/images/icon/cvv-icon.png" />
              <button
                [ngClass]="formaCartao() ? 'pacto-btn-pagamento-sel' : 'pacto-btn-pagamento'"
                (click)="cobrarCartao()"
                style="padding-left: 30px"
              >
                {{ 'checkout.cartao-de-credito' | translate }}
              </button>
            </div>
            <div *ngIf="apresentarPixVenda && !cobrancaGeradaComSucesso" style="padding-right: 20px">
              <img class="align-icon-pix" src="assets/images/icon/pix-icon.png" />
              <button [ngClass]="formaPix() ? 'pacto-btn-pagamento-sel' : 'pacto-btn-pagamento'" (click)="cobrarPix()">
                PIX
              </button>
            </div>
            <div *ngIf="apresentarBoletoVenda && !cobrancaGeradaComSucesso" style="padding-right: 20px">
              <img class="align-icon-boleto" src="assets/images/icon/boleto-icon.png" />
              <button
                [ngClass]="formaBoleto() ? 'pacto-btn-pagamento-sel' : 'pacto-btn-pagamento'"
                (click)="cobrarBoleto()"
              >
                Boleto
              </button>
            </div>
          </div>
        </div>
        <div class="col-12" *ngIf="formaCartao() && config?.apresentarCartaoVenda">
          <pacto-cartao-credito
            [formGroup]="formGroup"
            [pixelId]="obterPixelMeta()"
            [tokenApiConversao]="obterTokenApiConversaoMeta()"
            (aprovouCadastroCartaoPrimeiroPagamentoPix)="obteveCartaoAgoraCobrarPix($event)"></pacto-cartao-credito>
        </div>
        <div class="col-12" *ngIf="formaPix() && config?.apresentarPixVenda">
          <pacto-debito-pix [url]="url"
          [chave]="negociacaoService.chave"
            [unidade]="negociacaoService.codunidade"
            ></pacto-debito-pix>
        </div>
        <div class="col-12" *ngIf="formaBoleto() && config?.apresentarBoletoVenda">
          <pacto-boleto
            [url]="url"
            [linhaDigitavel]="linhaDigitavel"
            [valor]="valor"
            [vencimento]="vencimento"
            [status]="status"
            [variosBoletos]="variosBoletos"
            [chave]="negociacaoService.chave"
            [unidade]="negociacaoService.codunidade"
            [pixelId]="obterPixelMeta()"
            [tokenApiConversao]="obterTokenApiConversaoMeta()"
          ></pacto-boleto>
        </div>
        <div class="col-12">
          <pacto-unidade-selecionada class="dispositivo-pequeno"></pacto-unidade-selecionada>
        </div>
      </div>
      <div class="row pacto-mg-top-grande" *ngIf="!isExibirCampo('ParQ') && formaNenhuma() || parqValidado && formaNenhuma()"></div>
    </div>
    <!-- colunadados -->
    <div class="col-md-5 col-sm-12 order-1 order-md-4">
      <br />
      <div class="colunafixa">
        <span class="resumo">{{ 'checkout.resumo-do-seu-pedido' | translate }}</span>
        <pacto-plano-selecionado [formGroup]="formGroup" *ngIf="!isCheckoutLocacoes"></pacto-plano-selecionado>
        <pacto-locacoes-selecionadas-checkout [formGroup]="formGroup" *ngIf="isCheckoutLocacoes && existeLocacaoSelecionada"></pacto-locacoes-selecionadas-checkout>
        <pacto-produtos-selecionados [formGroup]="formGroup"></pacto-produtos-selecionados>
        <pacto-aulas-marcadas *ngIf="aulasSelecionadas > 0" [editando]="false"></pacto-aulas-marcadas>
        <pacto-unidade-selecionada class="dispositivo-grande"></pacto-unidade-selecionada>
      </div>
    </div>
  </div>
</div>
<!-- "MODAL PRE-CADASTRO" -->
<pacto-modal-pre-cadastro *ngIf="mostrarModalPreCadastro" (fechar)="fecharModalPreCadastro()"></pacto-modal-pre-cadastro>
<!-- --->
