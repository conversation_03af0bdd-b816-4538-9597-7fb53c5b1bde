import {After<PERSON><PERSON>w<PERSON><PERSON><PERSON>, Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Aula } from '@base-core/agenda/aula.model';
import { AulasService } from '@base-core/agenda/aulas.service';
import { AlunoService } from '@base-core/aluno/aluno.service';
import { Cupom } from '@base-core/cupom-desconto/cupom.model';
import { Config } from '@base-core/empresa/config.model';
import { Empresa } from '@base-core/empresa/empresa.model';
import { EmpresaService } from '@base-core/empresa/empresa.service';
import { PaginaVendasOnLine } from '@base-core/negociacao/acesso-pagina';
import { NegociacaoService } from '@base-core/negociacao/negociacao.service';
import { Venda } from '@base-core/negociacao/venda.model';
import { Contrato, ContratoAdapter } from '@base-core/plano/contrato.model';
import { Plano } from '@base-core/plano/plano.model';
import { PlanoService } from '@base-core/plano/plano.service';
import { VendaProduto } from '@base-core/produto/produto.model';
import { ProdutoService } from '@base-core/produto/produto.service';
import { LocalizationService } from '@base-core/service/localization.service';
import { TranslateService } from '@ngx-translate/core';
import { ReCaptchaV3Service } from 'ng-recaptcha';
import Swal from 'sweetalert2';
import { BoletoComponent } from './boleto/boleto.component';
import { DebitoPixComponent } from './debito-pix/debito-pix.component';
import { FormaPagamento } from './shared/enum-forma-pagamento';
import { InfoPagamentos } from './shared/info-pagamentos';
import { FacebookPixelService } from '@base-core/analytics/facebook-pixel.service';
import {Parq} from '@base-core/parq/parq.model';
import {DomSanitizer, SafeResourceUrl} from '@angular/platform-browser';
import {LeadService} from '@base-core/lead/lead.service';
import {ModalidadeService} from '@base-core/modalidade/modalidade-service';
import {TurmaService} from '@base-core/turma/turma.service';
import {NascimentoService} from '@base-core/nascimento/nascimento.service';
import { OrigemSistemaEnum } from '@base-core/negociacao/enum-origem-sistema';
import { LocacaoAmbienteService } from '@base-core/locacao-ambiente/locacao-ambiente.service';
import {Location} from '@angular/common';

@Component({
  selector: 'pacto-checkout',
  templateUrl: './checkout.component.html',
  styleUrls: ['./checkout.component.scss'],
})
export class CheckoutComponent implements OnInit, AfterViewChecked {
  @ViewChild('parcelasdiv')
  public parcelasContainer: ElementRef<HTMLDivElement>;

  @ViewChild('dataNascimento') dataNascimento;

  public mostrarModalPreCadastro = false;

  public clienteTitularJaPossuiDependentes: boolean;

  public formGroup: FormGroup = new FormGroup({});
  mascaracpf = [/\d|\*/, /\d|\*/, /\d|\*/, '.', /\d|\*/, /\d|\*/, /\d|\*/, '.', /\d|\*/, /\d|\*/, /\d|\*/, '-', /\d|\*/, /\d|\*/];
  public readonly mascaraCnpj = [/\d/, /\d/, '.', /\d/, /\d/, /\d/, '.', /\d/, /\d/, /\d/, '/', /\d/, /\d/, /\d/, /\d/, '-', /\d/, /\d/];
  public mascaraTelefone;
  public cpfValidado = '';
  public emailValidado = '';
  public vendas: VendaProduto[];
  public aulas: Array<Aula> = [];
  public menorIdade = false;
  public url: string;
  public formaPagamento: FormaPagamento = FormaPagamento.CARTAO;
  private debitoPix: DebitoPixComponent;
  private boleto: BoletoComponent;
  private infoPagamento: InfoPagamentos;
  private pagina: string;
  public linhaDigitavel: string;
  public vencimento: string;
  public valor: string;
  public status: string;
  public estaNaTelaDePagamentoPixAposFluxoDeAdicionarCartaoSucesso = false;
  public cobrancaGeradaComSucesso = false;
  public variosBoletos = false;
  public parqValidado = false;
  public cupomParam = '';
  public tipoDocumento = 'CPF';
  public labelCampoNome = 'checkout.nome-completo';
  public labelCampoTelefone = 'checkout.telefone';
  public placeholderCampoNome = 'checkout.digite-seu-nome-completo';
  public opcoesTipoDeDocumento = [
    { id: 'CPF', label: this.translateService.instant('CPF') },
    { id: 'CNPJ', label: this.translateService.instant('CNPJ') },
  ];
  pixelId = '';
  tokenApiConversao = null;
  fcResposta = new FormControl();
  public exibirCamposCompartilhamentoPlano = false;
  public dadosPessoasCompartilhamentos: number[] = [];
  public atualizarArrayQtdPessoasCompartilhamentos(): void {
      this.dadosPessoasCompartilhamentos = Array(this.planoSelecionado.quantidadeCompartilhamentos).fill(0).map((x, i) => i);
  }
  public vendasConfigsFormaPagamentoPlanoProduto = [];
  public enableShowErrorInput = true;

  public naoPermiteAlterarDataMenorDeIdade: boolean = false;
  public verificaMenorDeIdadeNoModalRenovarContrato: boolean = false;

  public exibeCampoDataUtilizacao: boolean = false;
  public isCheckoutLocacoes: boolean = false;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly planoService: PlanoService,
    private readonly produtoService: ProdutoService,
    private readonly negociacaoService: NegociacaoService,
    private readonly alunoService: AlunoService,
    private readonly aulasService: AulasService,
    private readonly contratoAdapter: ContratoAdapter,
    private readonly empresaService: EmpresaService,
    private readonly recaptchaV3Service: ReCaptchaV3Service,
    private readonly translateService: TranslateService,
    private readonly localizationService: LocalizationService,
    private readonly router: Router,
    private readonly analitycsPixel: FacebookPixelService,
    private readonly leadService: LeadService,
    private readonly modalidadeService: ModalidadeService,
    private readonly turmaService: TurmaService,
    private readonly nascimentoService: NascimentoService,
    private readonly locacaoAmbienteService: LocacaoAmbienteService,
    private location: Location,
  ) {
    this.pagina = PaginaVendasOnLine[PaginaVendasOnLine.PLANO];
    this.infoPagamento = new InfoPagamentos(FormaPagamento.CARTAO, this.translateService);
    localStorage.setItem('lido', 'false');
    localStorage.setItem('menor', 'false');

    // Pode chegar aqui através de link direto de produto ou plano.
    // Também pode chegar aqui através da navegação de escolha de planos ou produtos nos links de loja, todos os planos ou todos os produtos

    const currentUrl = window.location.href;

    if (!currentUrl.includes('utm_data') && this.negociacaoService.utm_data) { // origem link de todos os planos ou link da loja, ou de todos os produtos
      // Atualizar a URL sem recarregar a página
      window.history.pushState({}, '', currentUrl + '?' + this.negociacaoService.utm_data);
    } else if (currentUrl.includes('utm_data')) {
      // Split na URL usando 'utm_data' como referência
      const splitUrl = currentUrl.split('utm_data');
      const urlPossuiUtmDataNaUrl = splitUrl.length > 1;

      if (urlPossuiUtmDataNaUrl) {
        const part2 = 'utm_data' + splitUrl[1];
        this.negociacaoService.utm_data = part2;
      }
    }

    this.route.queryParams.subscribe((params) => {
      if (params['prh']) {
        window.localStorage.setItem('prh', params['prh']);
        window.localStorage.setItem('aula', params['aula']);
        window.localStorage.removeItem('produtos');
      } else {
        window.localStorage.removeItem('prh');
        window.localStorage.removeItem('aula');
      }
      if (params['evento']) {
        this.negociacaoService.codigoEvento = Number(params['evento']);
      }

      // Verificar se vem de locações
      if (params['loc'] === 'true') {
        this.isCheckoutLocacoes = true;
      }
      // verificar se veio da tela de escolha de planos
      const selecionouPlanoNaTelaAnterior = this.planoService.planoSelecionado;
      const selecionouProdutoNaTelaAnterior = this.produtoService.produtosSelecionados.length > 0;
      negociacaoService.temPlano = selecionouPlanoNaTelaAnterior;
      negociacaoService.temProduto = selecionouProdutoNaTelaAnterior;
      if (this.negociacaoService.chave && this.negociacaoService.codunidade && (selecionouPlanoNaTelaAnterior || selecionouProdutoNaTelaAnterior)) {
        window.localStorage.setItem('chave', this.negociacaoService.chave);
        window.localStorage.setItem('unidade', this.negociacaoService.codunidade);
        if (selecionouPlanoNaTelaAnterior) {
          this.planoService
            .obterPlano(this.negociacaoService.chave, this.negociacaoService.codunidade, selecionouPlanoNaTelaAnterior.codigo)
            .subscribe((data) => {
              this.planoService.planoSelecionado = data;
              this.setarPlano(this.getVendaJsonDecrypt());
            });
          window.localStorage.setItem('plano', this.planoService.planoSelecionado.codigo.toString());
        } else {
          window.localStorage.removeItem('plano');
        }
        if (selecionouProdutoNaTelaAnterior) {
          window.localStorage.setItem('produtos', JSON.stringify(this.produtoService.produtosSelecionados));
        } else {
          window.localStorage.removeItem('produtos');
        }
      } else {
        // senão veio da escolha de planos, buscar na url ou local storage
        if (params['k']) {
          this.negociacaoService.chave = params['k'];
          window.localStorage.setItem('chave', params['k']);
        } else {
          this.negociacaoService.chave = window.localStorage.getItem('chave');
        }
        if (params['un']) {
          this.negociacaoService.codunidade = params['un'];
          window.localStorage.setItem('unidade', params['un']);
        } else {
          this.negociacaoService.codunidade = window.localStorage.getItem('unidade');
        }
        if (params['us']) {
          this.negociacaoService.usuarioResponsavel = params['us'];
          window.localStorage.setItem('usuario', params['us']);
        } else {
          if (window.localStorage.getItem('usuario')) {
            this.negociacaoService.usuarioResponsavel = parseInt(window.localStorage.getItem('usuario'), 10);
          }
        }
        if (params['pl']) {
          window.localStorage.setItem('plano', params['pl']);
          this.planoService.obterPlano(this.negociacaoService.chave, params['un'], params['pl']).subscribe((data) => {
            this.planoService.planoSelecionado = data;
            this.negociacaoService.codunidade = params['un'];
            this.empresaService.obterEmpresa(params['k'], params['un']).subscribe((dados) => {
              this.empresaService.unidadeSelecionada = dados;
              this.setarPlano2(this.getVendaJsonDecrypt(), params['un']);

              if (params['cupom']) {
                window.localStorage.setItem('cupomdesconto', params['cupom']);
                this.cupomParam = params['cupom'];
                this.validarCupom(params['cupom']);
                this.formGroup.get('cupomdesconto').setValue(params['cupom']);
              }
            });
            if (this.planoService.planoSelecionado.vendaComTurma) {
              this.router.navigate(['/nascimento', 'backoff']);
            }
          });
        } else if (!params['pr'] && !params['prh'] && !params['aula'] && !params['loc']) {
          this.planoService
            .obterPlano(
              this.negociacaoService.chave,
              this.negociacaoService.codunidade,
              window.localStorage.getItem('plano'),
            )
            .subscribe((data) => {
              this.planoService.planoSelecionado = data;
              this.atualizarArrayQtdPessoasCompartilhamentos();
              this.setarPlano(this.getVendaJsonDecrypt());
            });
        }
        if (params['pr']) {
          this.pagina = PaginaVendasOnLine[PaginaVendasOnLine.PRODUTO];
          let qtd = 1;
          if (params['qtd']) {
            qtd = params['qtd'];
          }

          this.produtoService
            .obterProduto(this.negociacaoService.chave, this.negociacaoService.codunidade, params['pr'])
            .subscribe((data) => {
              this.produtoService.adicionarProduto(data, qtd);
              window.localStorage.setItem('produtos', JSON.stringify(this.produtoService.produtosSelecionados));
            });
        } else if (!params['pl']) {
          const json = JSON.parse(window.localStorage.getItem('produtos'));
          if (json) {
            for (let i = 0; i < json.length; i++) {
              const venda = new VendaProduto(
                json[i].produto,
                json[i].qtd,
                json[i].valorUnitario,
                json[i].descricao,
                json[i].observacao
              );
              this.produtoService.produtosSelecionados.push(venda);
            }
          }
        }

        this.produtoService.obterProdutos(this.negociacaoService.chave,
          this.negociacaoService.codunidade, 0).subscribe(data => {
          this.produtoService.produtos = data;
        });

      }

      this.loadUnidade();
    });

    this.naoPermiteAlterarDataMenorDeIdade = false;
    this.verificaMenorDeIdadeNoModalRenovarContrato = false;
    this.exibeCampoDataUtilizacao = localStorage.getItem('exibeDataUtilizacao') === 'true' ? true : false;
  }
  public ngOnInit(): void {
    this.planoService.exibirCamposCompartilhamentoPlano$.subscribe(value => {
      this.exibirCamposCompartilhamentoPlano = value;
      this.planoService.dadosCompletosDesmascarados = null;

      //Tratativa para impedir o acesso da página de checkout a partir da página de pós pagamento
      if(localStorage.getItem('limparDadosCheckout') && localStorage.getItem('limparDadosCheckout') === 'true') {

        const urlPaginaSelecionarPlano = '/pospagamento?un=' + this.negociacaoService.codunidade + '&k=' + this.negociacaoService.chave;

        if(window.location.href.includes('?un=') && window.location.href.includes('&k=')
          && (window.location.href.includes('&pl=') || window.location.href.includes('&pr='))) {
          localStorage.removeItem('limparDadosCheckout');
        }else{
          Swal.fire({
            type: 'error',
            title: 'Operação não permitida',
            text: 'Não é permitido voltar após finalizar uma compra, por favor acesse o link novamente!',
            showConfirmButton: true,
          }).then((result) => {
            window.location.href = urlPaginaSelecionarPlano;
            localStorage.removeItem('limparDadosCheckout');
          });
        }
      }
    });

    this.mascaraTelefone = this.localizationService.getPhoneLocaleMask();

    if (window.localStorage.getItem('prh') != null) {
      this.vendas = JSON.parse(window.localStorage.getItem('prh')) as VendaProduto[];
      this.aulas = JSON.parse(window.localStorage.getItem('aula')) as Array<Aula>;
      this.aulasService.aulasSelecionadas = this.aulas;
      this.vendas.forEach((i) => {
        this.produtoService.produtosSelecionados.push(
          new VendaProduto(i.produto, i.qtd, i.valorUnitario, i.descricao, i.observacao),
        );
      });
    }
    if (this.negociacaoService.codigoRegistroAcessoPagina === 0) {
      this.negociacaoService.registrarAcessoPagina(this.pagina, this.router.url);
    }
    this.obterParq();

    if (this.getConfig() && this.getConfig().habilitarPreCadastro) {
      this.mostrarModalPreCadastro = true;
    }
  }

  public ngAfterViewChecked(): void {

    const dataUtilizacao = this.formGroup.get('dataUtilizacao');

    if(this.exibeCampoDataUtilizacao && dataUtilizacao && !dataUtilizacao.value) {
      const dtAtual = new Date();
      const dataUtilizacaoPadrao = dtAtual.toLocaleDateString('pt-BR');
      this.formGroup.patchValue({
        ['dataUtilizacao']: dataUtilizacaoPadrao
      });
    }
  }

  public carregarInfosDoPreCadastro() {
    this.leadService.nome$.subscribe((nome) => {
      this.formGroup.get('nome').setValue(nome);
    });
    this.leadService.telefone$.subscribe(telefone => this.formGroup.get('telefone').setValue(telefone));
    this.leadService.email$.subscribe(email => this.formGroup.get('email').setValue(email));
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  public fecharModalPreCadastro() {
    this.mostrarModalPreCadastro = false;
    this.carregarInfosDoPreCadastro();
  }
  public getVendaJson(): Venda {

    let permiteInformarDataUtilizacao: boolean = (this.exibeCampoDataUtilizacao && this.existeProdutoDiariaSelecionado()) ? true : false;

    return new Venda(
      this.empresaService.unidadeSelecionada.codigo !== undefined ||
        this.empresaService.unidadeSelecionada.codigo != null
        // tslint:disable-next-line:radix
        ? this.empresaService.unidadeSelecionada.codigo : parseInt(window.localStorage.getItem('unidade')),
      this.planoService.planoSelecionado ? this.planoService.planoSelecionado.codigo : 0,
      this.isFormularioPreenchido('nome') ? this.formGroup.get('nome').value : null,
      this.isFormularioPreenchido('cpf') ? this.formGroup.get('cpf').value : null,
      this.isFormularioPreenchido('sexo') ? this.formGroup.get('sexo').value : null,
      this.isFormularioPreenchido('dataNascimento')
        ? this.converterDataInternacionalizacaoParaPadraoPtBr(this.formGroup.get('dataNascimento').value)
        : '',
      this.isFormularioPreenchido('email') ? this.formGroup.get('email').value : null,
      this.isFormularioPreenchido('nomecartao') ? this.formGroup.get('nomecartao').value : null,
      this.isFormularioPreenchido('nrcartao') ? this.formGroup.get('nrcartao').value.replace(/ /g, '') : '',
      this.isFormularioPreenchido('validade') ? this.formGroup.get('validade').value.replace('/', '/20') : '',
      this.isFormularioPreenchido('cvv') ? this.formGroup.get('cvv').value : null,
      this.isFormularioPreenchido('telefone') ? this.formGroup.get('telefone').value : null,
      this.isFormularioPreenchido('endereco') ? this.formGroup.get('endereco').value : null,
      this.isFormularioPreenchido('numero') ? this.formGroup.get('numero').value : null,
      this.isFormularioPreenchido('bairro') ? this.formGroup.get('bairro').value : null,
      this.isFormularioPreenchido('complemento') ? this.formGroup.get('complemento').value : null,
      this.isFormularioPreenchido('cep') ? this.formGroup.get('cep').value : null,
      this.isFormularioPreenchido('diavencimento') ? this.formGroup.get('diavencimento').value : null,
      this.isFormularioPreenchido('parcelasCartao') ? this.formGroup.get('parcelasCartao').value : null,
      this.isFormularioPreenchido('cupomdesconto') ? this.formGroup.get('cupomdesconto').value : '',
      this.isFormularioPreenchido('cpftitularcard') ? this.formGroup.get('cpftitularcard').value : null,
      this.isFormularioPreenchido('vencimentoFatura') ? this.formGroup.get('vencimentoFatura').value : 0,
      this.produtoService.produtosSelecionados,
      this.negociacaoService.cobrarParcelasEmAberto,
      this.isFormularioPreenchido('dataInicioContrato') ? this.formGroup.get('dataInicioContrato').value : null,
      this.isFormularioPreenchido('responsavelPai') ? this.formGroup.get('responsavelPai').value : null,
      this.isFormularioPreenchido('responsavelMae') ? this.formGroup.get('responsavelMae').value : null,
      this.isFormularioPreenchido('cpfMae') ? this.formGroup.get('cpfMae').value : null,
      this.isFormularioPreenchido('cpfPai') ? this.formGroup.get('cpfPai').value : null,
      this.aulasService.codigosAulasSelecionadas(),
      this.planoService.permitirRenovacao,
      this.negociacaoService.categoriaPlano ? this.negociacaoService.categoriaPlano : null,
      this.negociacaoService.origemCobranca,
      this.negociacaoService.cobrancaAntecipada,
      this.negociacaoService.responsavel,
      this.negociacaoService.token,
      this.planoService.vezesEscolhidasParcelarTaxaMatricula,
      this.formGroup.get('rg') ? this.formGroup.get('rg').value : null,
      this.negociacaoService.codigoEvento,
      this.negociacaoService.usuarioResponsavel,
      this.negociacaoService.codigoRegistroAcessoPagina,
      '',
      (this.formGroup.get('tipoCredito') ? this.formGroup.get('tipoCredito').value : null), '',
      (this.formGroup.get('cnpj') ? this.formGroup.get('cnpj').value : null),
      (this.formGroup.get('nomeResponsavelEmpresa') ? this.formGroup.get('nomeResponsavelEmpresa').value : null),
      (this.formGroup.get('cpfResponsavelEmpresa') ? this.formGroup.get('cpfResponsavelEmpresa').value : null),
      this.negociacaoService.lancarContratoConcomitanteAoInvesDeRenovar,
      this.negociacaoService.pactoPayComunicacao,
      this.negociacaoService.utm_data, [],
      this.modalidadeService.modalidadesSelecionadas,
      this.turmaService.horariosSelecionados, null, false,
      OrigemSistemaEnum.VENDAS_ONLINE,
      this.formGroup.get("dataUtilizacao") ? this.formGroup.get("dataUtilizacao").value : null,
      permiteInformarDataUtilizacao,
      this.locacaoAmbienteService.locacoesSelecionadas && this.locacaoAmbienteService.locacoesSelecionadas.length > 0 ? true : false,
      this.locacaoAmbienteService.locacoesSelecionadas
    );
  }

  public getVendaJsonDecrypt(): Venda {
    if (this.planoService && this.planoService.dadosCompletosDesmascarados) {

      let endereco;
      if (this.planoService.dadosCompletosDesmascarados['enderecoCompleto']) {
        endereco = JSON.parse(this.planoService.dadosCompletosDesmascarados['enderecoCompleto'].toString());
      }

      let permiteInformarDataUtilizacao: boolean = (this.exibeCampoDataUtilizacao && this.existeProdutoDiariaSelecionado()) ? true : false;

      return new Venda(
        this.empresaService.unidadeSelecionada.codigo !== undefined ||
        this.empresaService.unidadeSelecionada.codigo != null
          // tslint:disable-next-line:radix
          ? this.empresaService.unidadeSelecionada.codigo : parseInt(window.localStorage.getItem('unidade')),
        this.planoService.planoSelecionado ? this.planoService.planoSelecionado.codigo : 0,
        this.isFormularioPreenchido('nome') && this.formGroup.get('nome').value.includes('*') ? this.planoService.dadosCompletosDesmascarados.nome : this.formGroup.get('nome').value,
        this.isFormularioPreenchido('cpf') && this.formGroup.get('cpf').value.includes('*') ? this.planoService.dadosCompletosDesmascarados.cpf : this.formGroup.get('cpf').value,
        this.isFormularioPreenchido('sexo') ? this.formGroup.get('sexo').value : null,
        this.isFormularioPreenchido('dataNascimento') && this.formGroup.get('dataNascimento').value.includes('*')
          ? this.converterDataInternacionalizacaoParaPadraoPtBr(this.planoService.dadosCompletosDesmascarados.dataNascimento)
          : this.isFormularioPreenchido('dataNascimento')
            ? this.converterDataInternacionalizacaoParaPadraoPtBr(this.formGroup.get('dataNascimento').value)
            : '',
        this.isFormularioPreenchido('email') && this.formGroup.get('email').value.includes('*') ? this.planoService.dadosCompletosDesmascarados.email : this.formGroup.get('email').value,
        this.isFormularioPreenchido('nomecartao') ? this.formGroup.get('nomecartao').value : null,
        this.isFormularioPreenchido('nrcartao') ? this.formGroup.get('nrcartao').value.replace(/ /g, '') : '',
        this.isFormularioPreenchido('validade') ? this.formGroup.get('validade').value.replace('/', '/20') : '',
        this.isFormularioPreenchido('cvv') ? this.formGroup.get('cvv').value : null,
        this.isFormularioPreenchido('telefone') && this.formGroup.get('telefone').value.includes('*')
          ? this.planoService.dadosCompletosDesmascarados.telefone
            : this.isFormularioPreenchido('telefone') ? this.formGroup.get('telefone').value : null,
        this.isFormularioPreenchido('endereco') && this.formGroup.get('endereco').value.includes('*')
            ? endereco.endereco : this.isFormularioPreenchido('endereco')
            ? this.formGroup.get('endereco').value : null,
        this.isFormularioPreenchido('numero') ? this.formGroup.get('numero').value : null,
        this.isFormularioPreenchido('bairro') && this.formGroup.get('bairro').value.includes('*')
            ? endereco.bairro : this.isFormularioPreenchido('bairro')
            ? this.formGroup.get('bairro').value : null,
        this.isFormularioPreenchido('complemento') && this.formGroup.get('complemento').value.includes('*')
            ? endereco.complemento : this.isFormularioPreenchido('complemento') ? this.formGroup.get('complemento').value : null,
        this.isFormularioPreenchido('cep') && this.formGroup.get('cep').value.includes('*')
            ? endereco.cep : this.isFormularioPreenchido('cep') ? this.formGroup.get('cep').value : null,
        this.isFormularioPreenchido('diavencimento') ? this.formGroup.get('diavencimento').value : null,
        this.isFormularioPreenchido('parcelasCartao') ? this.formGroup.get('parcelasCartao').value : null,
        this.isFormularioPreenchido('cupomdesconto') ? this.formGroup.get('cupomdesconto').value : '',
        this.isFormularioPreenchido('cpftitularcard') ? this.formGroup.get('cpftitularcard').value : null,
        this.isFormularioPreenchido('vencimentoFatura') ? this.formGroup.get('vencimentoFatura').value : 0,
        this.produtoService.produtosSelecionados,
        this.negociacaoService.cobrarParcelasEmAberto,
        this.isFormularioPreenchido('dataInicioContrato') ? this.formGroup.get('dataInicioContrato').value : null,
        this.isFormularioPreenchido('responsavelPai') ? this.formGroup.get('responsavelPai').value : null,
        this.isFormularioPreenchido('responsavelMae') ? this.formGroup.get('responsavelMae').value : null,
        this.isFormularioPreenchido('cpfMae') ? this.formGroup.get('cpfMae').value : null,
        this.isFormularioPreenchido('cpfPai') ? this.formGroup.get('cpfPai').value : null,
        this.aulasService.codigosAulasSelecionadas(),
        this.planoService.permitirRenovacao,
        this.negociacaoService.categoriaPlano ? this.negociacaoService.categoriaPlano : null,
        this.negociacaoService.origemCobranca,
        this.negociacaoService.cobrancaAntecipada,
        this.negociacaoService.responsavel,
        this.negociacaoService.token,
        this.planoService.vezesEscolhidasParcelarTaxaMatricula,
        this.isFormularioPreenchido('rg') && this.formGroup.get('rg').value.includes('*')
            ? this.planoService.dadosCompletosDesmascarados.rg : this.isFormularioPreenchido('rg')
            ? this.formGroup.get('rg').value : null,
        this.negociacaoService.codigoEvento,
        this.negociacaoService.usuarioResponsavel,
        this.negociacaoService.codigoRegistroAcessoPagina,
        '',
        (this.formGroup.get('tipoCredito') ? this.formGroup.get('tipoCredito').value : null), '',
        (this.formGroup.get('cnpj') ? this.formGroup.get('cnpj').value : null),
        (this.formGroup.get('nomeResponsavelEmpresa') ? this.formGroup.get('nomeResponsavelEmpresa').value : null),
        (this.formGroup.get('cpfResponsavelEmpresa') ? this.formGroup.get('cpfResponsavelEmpresa').value : null),
        this.negociacaoService.lancarContratoConcomitanteAoInvesDeRenovar,
        this.negociacaoService.pactoPayComunicacao,
        this.negociacaoService.utm_data, [],
        this.modalidadeService.modalidadesSelecionadas,
        this.turmaService.horariosSelecionados, null, false,
        OrigemSistemaEnum.VENDAS_ONLINE, this.formGroup.get("dataUtilizacao") ? this.formGroup.get("dataUtilizacao").value : null,
        permiteInformarDataUtilizacao,
        this.locacaoAmbienteService.locacoesSelecionadas && this.locacaoAmbienteService.locacoesSelecionadas.length > 0 ? true : false,
        this.locacaoAmbienteService.locacoesSelecionadas
      );
    } else {
      return this.getVendaJson();
    }
  }

  private msgPlanoIndisponivel(): void {
    Swal.fire({
      type: 'error',
      title: this.translateService.instant('checkout.plano-indisponivel'),
      text: this.translateService.instant('checkout.este-plano-nao-esta-mais-disponivel-para-venda'),
      showConfirmButton: true,
    });
  }

  public setarPlano(venda): void {
    if (venda.plano === null) {
      this.msgPlanoIndisponivel();
    } else {
      this.atualizarArrayQtdPessoasCompartilhamentos();
      this.planoService.planoSelecionado.codigo = venda.plano;
      if (this.planoService.planoSelecionado.codigo) {
        this.negociacaoService
          .simularVenda(venda, this.negociacaoService.codunidade)
          .subscribe((contrato: any) => {
            if (contrato.erro && contrato.erro.includes('ERRO: Parece')) {
              this.formGroup.get('nome').setValue('');
              this.formGroup.get('cpf').setValue('');
              this.formGroup.get('dataNascimento').setValue('');
              this.formGroup.get('email').setValue('');
              venda.nome = '';
              venda.cpf = '';
              venda.dataNascimento = '';
              venda.email = '';
              Swal.fire({
                type: 'info',
                title: 'Atenção',
                text: contrato.erro.split('ERRO:')[1],
                showConfirmButton: true,
              });
            } else if (contrato.erro && contrato.erro.includes('ERRO: O horário')) {
              venda.horariosSelecionados = [];
              Swal.fire({
                type: 'error',
                title: 'Não foi possível adicionar o horario da turma',
                text: contrato.erro.split('ERRO:')[1],
                showConfirmButton: true,
                onClose: () => {
                  this.location.back();
                }
              });
            } else {
              this.setarValorFinalPlano(contrato);
            }
          });
      }
    }
  }

  public setarPlano2(venda, codigo): void {
    if (venda.plano === null) {
      this.msgPlanoIndisponivel();
    } else {
      this.planoService.planoSelecionado.codigo = venda.plano;
      this.atualizarArrayQtdPessoasCompartilhamentos();
      if (this.planoService.planoSelecionado.codigo) {
        this.negociacaoService.simularVenda(venda, codigo).subscribe((contrato) => this.setarValorFinalPlano(contrato));
      }
    }
  }

  private setarValorFinalPlano(data): void {
    if (data.erro) {
      throw new Error ('Erro ao carregar dados sobre valores do plano: ' + data.erro);

    } else {
      this.negociacaoService.valorFinalContrato = data.return.valorBase;
      this.negociacaoService.parcelas = data.return.parcelas;
      this.negociacaoService.valorPrimeiraParcela = data.return.valorPrimeiraParcela;
      this.negociacaoService.valorProRata = data.return.valorProRata;
      this.negociacaoService.descricaoCobrancaPrimeiraParcela = data.return.descricaoCobrancaPrimeiraParcela;
      this.negociacaoService.anoCobrancaAnuidade = data.return.anoCobrancaAnuidade;
      if (this.planoSelecionado && this.planoSelecionado.vendaComTurma) {
        this.planoSelecionado.primeiraParcela = data.return.valorPrimeiraParcela;

        this.planoService.planoSelecionado.modalidades = [];
        for (let i = 0; i < data.return.modalidades.length; i++) {
          let modalidadeString = data.return.modalidades[i].modalidade;
          if (modalidadeString) {
            this.planoService.planoSelecionado.modalidades.push(modalidadeString);
          }
        }
      }
    }

  }

  private loadUnidade(): void {
    try {
      this.tryLoad();
    } catch (e) {
      console.log('Erro ao tentar obter a empresa, vou tentar novamente');
      try {
        this.tryLoad();
      } catch (ex) {
        console.log('O erro persistiu');
        console.log(this.negociacaoService.chave);
        console.log(this.negociacaoService.codunidade);
        console.log(e);
      }
    }
  }

  private tryLoad() {
    if (!this.empresaService.unidadeSelecionada) {
      console.log('Try to load company');
      this.empresaService
        .obterEmpresa(this.negociacaoService.chave, this.negociacaoService.codunidade)
        .subscribe((data) => (this.empresaService.unidadeSelecionada = data));
    }
    this.empresaService
      .obterConfigs(this.negociacaoService.chave, this.negociacaoService.codunidade)
      .subscribe((data) => {
        this.formaPagamento = FormaPagamento.NENHUMA;
        this.empresaService.config = data;
        if (this.empresaService.config && this.empresaService.config.apresentarCartaoVenda) {
          this.formaPagamento = FormaPagamento.CARTAO;
        }
        this.pixelId = this.empresaService.config.pixelId;
        this.tokenApiConversao = this.empresaService.config.tokenApiConversao;
        if (this.empresaService.config && this.empresaService.config.habilitarPreCadastro) {
          this.mostrarModalPreCadastro = true;
        }
        if (this.empresaService.config && this.empresaService.config.usarFormaPagamentoPlanoProduto) {
          this.obterConfigsFormaPagamentoPlanoProduto();
        }
        if(data.exibeDataUtilizacao) {
          localStorage.setItem('exibeDataUtilizacao', 'true');
        }else{
          localStorage.setItem('exibeDataUtilizacao', 'false');
        }

      });
  }

  public alterouTipoDocumento(): void {
      if (this.tipoDocumento === 'CPF') {
        this.formGroup.get('cpf').setValue('');

        this.labelCampoNome = 'checkout.nome-empresa';
        this.labelCampoTelefone = 'checkout.telefone-celular';
        this.placeholderCampoNome = 'checkout.digite-nome-sua-empresa';
        this.tipoDocumento = 'CNPJ';
      } else {
        this.formGroup.get('cnpj').setValue('');
        this.formGroup.get('nomeResponsavelEmpresa').setValue('');
        this.formGroup.get('cpfResponsavelEmpresa').setValue('');

        this.labelCampoNome = 'checkout.nome-completo';
        this.labelCampoTelefone = 'checkout.telefone';
        this.placeholderCampoNome = 'checkout.digite-seu-nome-completo';
        this.tipoDocumento = 'CPF';
      }
    this.negociacaoService.tipoDocumento = this.tipoDocumento;
  }

  public consultarPorCnpj(): void {
    if (!this.isFormularioPreenchido('cnpj')) {
      return;
    }

    this.recaptchaV3Service.execute('importantAction').subscribe((token) => {
      this.alunoService
        .cnpj(this.negociacaoService.chave, this.formGroup.get('cnpj').value, token)
        .subscribe((data: any) => {
          if (data.erro && !data.erro.toString().toUpperCase().includes('NENHUM CLIENTE ENCONTRADO')) {
            Swal.fire({
              type: 'error',
              title: 'Erro',
              text: data.erro,
              showConfirmButton: true,
            });
          } else if (data.return) {
            if (data.return.length > 1) {
              Swal.fire({
                type: 'error',
                title: 'Verificação',
                text: 'Mais de um cadastro encontrado para este CPF, não será possível prosseguir!',
                showConfirmButton: true,
              });
              this.formGroup.get('cnpj').setValue('');
            } else if (this.alunoAtivoEDifUnidade(data.return[0])) {
              const tituloModal = this.translateService.instant('checkout.atencao');
              const mensagemModal = this.translateService.instant('checkout.aluno-ativo-outra-unidade');
              Swal.fire({
                type: 'warning',
                title: tituloModal,
                text: mensagemModal,
                showConfirmButton: true,
              });
              this.formGroup.get('cnpj').setValue('');
            } else {
              this.validarUso('cnpj', 'CNPJ foi encontrado na base', data.return[0], 'cnpj', false, 0);
              return; // só será setado o plano ao validar o uso
            }
          }

          this.setarPlano(this.getVendaJsonDecrypt());
        });
    });
  }

  public consultarPorCpf(consultandoCpfClienteCompartilhamento: boolean, indexClienteCompartilhamento: number): void {
    let campoCpf = 'cpf';
    let campoEmail = 'email';
    const sufixoComp = '_comp' + indexClienteCompartilhamento;

    if (consultandoCpfClienteCompartilhamento) {
      campoCpf += sufixoComp;
      campoEmail += sufixoComp;
    }

    if (!this.isFormularioPreenchido(campoCpf)) {
      return;
    }

    this.recaptchaV3Service.execute('importantAction').subscribe((token) => {
      this.alunoService
        .cpf(this.negociacaoService.chave, this.formGroup.get(campoCpf).value, token,
          this.unidadeSelecionada.utilizaGestaoClientesComRestricoes)
        .subscribe((data: any) => {
          // se não encontrar nenhum cliente, sempre limpar o email | ticket M2-946
          this.cpfValidado = '';

          if (data && data.unidadesRestricoes && data.unidadesRestricoes.length > 0) {
            this.exibirMensagemRestricao(data.unidadesRestricoes);
            return;
          }

          if (consultandoCpfClienteCompartilhamento && data.return) {
            if(data.return[0].situacao === 'Ativo') {
              Swal.fire({
                type: 'error',
                title: 'Atenção',
                text: 'Dependente com CPF ' + this.formGroup.get(campoCpf).value + ' já possui um contrato ativo.',
                showConfirmButton: true,
              });
              this.formGroup.get(campoCpf).setValue('');
              return;
            }
          }

          if (data.return) { // caso nao tenha dado erro na requisiçao, pega a quantidade de dependentes
            this.clienteTitularJaPossuiDependentes = data.return[0].quantidadeDependentes > 0;
          }

          if (data.erro && data.erro.toString().toUpperCase().includes('NENHUM CLIENTE ENCONTRADO')) {
            this.clienteTitularJaPossuiDependentes = false;
          }

          if (data.erro && !data.erro.toString().toUpperCase().includes('NENHUM CLIENTE ENCONTRADO')) {
            Swal.fire({
              type: 'error',
              title: 'Erro',
              text: data.erro,
              showConfirmButton: true,
            });
          } else if (data.return) {
            if (data.return.length > 1) {
              Swal.fire({
                type: 'error',
                title: 'Verificação',
                text: 'Mais de um cadastro encontrado para este CPF, não será possível prosseguir!',
                showConfirmButton: true,
              });
              this.formGroup.get(campoCpf).setValue('');
            } else if (this.alunoAtivoEDifUnidade(data.return[0])) {
              // venda somente de produto, sem plano;
              const vendaSomenteDeProduto = (this.negociacaoService.temPlano === null || this.negociacaoService.temPlano === undefined) &&
                this.negociacaoService.temProduto;
              if (vendaSomenteDeProduto) {
                if (!this.empresaService.config.permiteVendaProdutoAlunoOutraUnidade) {
                  const tituloModalProd = this.translateService.instant('checkout.atencao');
                  const mensagemModalProd = this.translateService.instant('checkout.aluno-ativo-outra-unidade-produto');
                  Swal.fire({
                    type: 'warning',
                    title: tituloModalProd,
                    text: mensagemModalProd,
                    showConfirmButton: true,
                  });
                  return;
                } else { // permite Venda de Produto para Aluno de Outra Unidade
                  this.validarUso(
                    'cpf',
                    'CPF foi encontrado na base',
                    data.return[0],
                    campoCpf,
                    consultandoCpfClienteCompartilhamento,
                    indexClienteCompartilhamento);
                  return; // só será setado o plano ao validar o uso
                }
              }

              // venda somente de plano, sem produto;
              const tituloModal = this.translateService.instant('checkout.atencao');
              const mensagemModal = this.translateService.instant('checkout.aluno-ativo-outra-unidade');
              Swal.fire({
                type: 'warning',
                title: tituloModal,
                text: mensagemModal,
                showConfirmButton: true,
              });
              this.formGroup.get(campoCpf).setValue('');
            } else {
              this.validarUso('cpf',
                'CPF foi encontrado na base',
                data.return[0],
                campoCpf,
                consultandoCpfClienteCompartilhamento,
                indexClienteCompartilhamento);
              return; // só será setado o plano ao validar o uso
            }
          }

          this.setarPlano(this.getVendaJsonDecrypt());
        });
    });
  }

  public consultarCep(): void {
    if (!this.isFormularioPreenchido('cep')) {
      return;
    }
    this.alunoService.cep(this.formGroup.get('cep').value.replace(/\D/g, '')).subscribe((data: any) => {
      if (data.return) {
        this.formGroup.get('endereco').setValue(data.return.enderecoLogradouro);
        this.formGroup.get('bairro').setValue(data.return.bairroDescricao);
      }
    });
  }

  public consultarPorEmail(consultandoEmailClienteCompartilhamento: boolean, indexClienteCompartilhamento: number): void {
    let campoEmail = 'email';
    const sufixoComp = '_comp' + indexClienteCompartilhamento;

    if (consultandoEmailClienteCompartilhamento) {
      campoEmail += sufixoComp;
    }

    if (!this.isFormularioPreenchido(campoEmail)) {
      return;
    }
    this.recaptchaV3Service.execute('importantAction').subscribe((token) => {
      this.alunoService
        .email(this.negociacaoService.chave, this.formGroup.get(campoEmail).value, token)
        .subscribe((data: any) => {
          if (data.erro && !data.erro.toString().toUpperCase().includes('NENHUM CLIENTE ENCONTRADO')) {
            Swal.fire({
              type: 'error',
              title: 'Erro',
              text: data.erro,
              showConfirmButton: true,
            });
          } else if (data.return) {
            if (data.return.length > 1) {
              Swal.fire({
                type: 'error',
                title: this.translateService.instant('verificacao'),
                text: this.translateService.instant('mais-de-um-email-encontrado'),
                showConfirmButton: true,
              });
              this.formGroup.get(campoEmail).setValue('');
            } else if (this.alunoAtivoEDifUnidade(data.return[0])) {
              const tituloModal = this.translateService.instant('checkout.atencao');
              const mensagemModal = this.translateService.instant('checkout.aluno-ativo-outra-unidade');
              Swal.fire({
                type: 'warning',
                title: tituloModal,
                text: mensagemModal,
                showConfirmButton: true,
              });
              this.formGroup.get(campoEmail).setValue('');
            } else {
              const mensagemModal = this.translateService.instant('checkout.email-encontrado-na-base');
              this.validarUso(
                'email',
                mensagemModal,
                data.return[0],
                campoEmail,
                consultandoEmailClienteCompartilhamento,
                indexClienteCompartilhamento
              );
              return; // só será setado o plano ao validar o uso
            }
          }

          this.setarPlano(this.getVendaJsonDecrypt());
        });
    });
  }

  private validarUso(
    operacao,
    msg,
    data,
    campolimpar,
    validandoCampoClienteCompartilhamento: boolean,
    indexClienteCompartilhamento: number): void {

    // a variavel sufixoComp adiciona aos campos buscados nessa funçao o sufixo que completa o nome dos campos dos clientes
    // com quem o plano será compartilhado.
    // no html, os campos desses clientes sao iguais ao do cliente principal,
    // porém com o sufixo '_comp' adicionado e um index, pois a qtd de compartilhamentos pode variar..
    let sufixoComp = '';
    let perguntaNome = 'Seu nome é ' + this.mascararNome(data.nome) + '?';
    let textoBtnConfirmar = 'Sim, sou eu';
    let textoBtnCancelar = 'Não, não sou eu!';

    if (validandoCampoClienteCompartilhamento) {
      // caso esteja validando esses clientes, adiciona o sufixo e o index para pegar o campo correto no html.
      sufixoComp = '_comp' + indexClienteCompartilhamento;
      perguntaNome = 'O nome do parceiro de compartilhamento é ' + data.nome + '?';
      textoBtnConfirmar = 'Sim';
      textoBtnCancelar = 'Não';
    }

    if (
      data.matricula &&
      ((operacao === 'cpf' && this.cpfValidado !== data.cpf) || (operacao === 'email' && this.emailValidado !== data.email))
    ) {
      Swal.fire({
        title: msg,
        text: perguntaNome,
        type: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: textoBtnConfirmar,
        cancelButtonText: textoBtnCancelar,
      }).then((result) => {
        if (result.value) {
          if (operacao === 'cpf') {
            this.cpfValidado = data.cpf;
          } else if (operacao === 'email') {
            this.emailValidado = data.email;
          }
          if (data && data.codigoPessoa) {
            localStorage.setItem('codigoPessoa', data.codigoPessoa);
          }
          this.preencherFormularioComDadosMascarados(data, sufixoComp);
          this.buscarContratosARenovar(data);
          if(!this.verificaMenorDeIdadeNoModalRenovarContrato) {
            this.verificaMenorDeIdadeAoCarregarDadosDaConsulta(data.dataNascimento);
          }

        } else {
          if (this.isFormularioPreenchido(campolimpar)) {
            this.formGroup.get('nome' + sufixoComp).setValue('');
            this.formGroup.get('email' + sufixoComp).setValue('');
            this.formGroup.get('cpf' + sufixoComp).setValue('');
            this.clienteTitularJaPossuiDependentes = false;
            const tituloModal = this.translateService.instant('checkout.atencao');
            const mensagemModal = this.translateService.instant('checkout.seus-dados-podem-estar-sendo-utilizados-em-outro-cadastro');
            Swal.fire(
              tituloModal,
              mensagemModal,
              'warning'
            );
          }
          this.planoService.permitirRenovacao = false;
          this.setarPlano(this.getVendaJsonDecrypt());
        }
      });
    }
  }

  private preencherFormularioComDadosMascarados(dados: any, sufixoComp: string): void {
    this.formGroup.patchValue({
      ['nome' + sufixoComp]: this.mascararNome(dados.nome),
      ['cpf' + sufixoComp]: this.mascararCpf(dados.cpf),
      ['email' + sufixoComp]: this.mascararEmail(dados.email)
    });
    if (this.formGroup.get('dataNascimento' + sufixoComp)) {
      this.formGroup.patchValue({
        ['dataNascimento' + sufixoComp]: dados.dataNascimento ? '**/**/****' : '',
      });
    }
    if (this.formGroup.get('telefone' + sufixoComp)) {
      if (dados.telCelular) {
        this.formGroup.patchValue({
          ['telefone' + sufixoComp]: this.mascararTelefone(dados.telCelular),
        });
      } else if (dados.telResidencial) {
        this.formGroup.patchValue({
          ['telefone' + sufixoComp]: this.mascararTelefone(dados.telResidencial),
        });
      }
    }
    if (this.formGroup.get('sexo' + sufixoComp)) {
      this.formGroup.patchValue({
        ['sexo' + sufixoComp]: dados.sexo === 'M' ? 'Masculino' : 'Feminino'
      });
    }
    if (this.formGroup.get('rg' + sufixoComp)) {
      this.formGroup.patchValue({
        ['rg' + sufixoComp]: dados.rg ? this.mascararRG(dados.rg) : '',
      });
    }
    if (dados.enderecoCompleto != null) {
      const endereco = JSON.parse(dados['enderecoCompleto'].toString());
      if (endereco) {
        if (this.formGroup.get('cep' + sufixoComp) && endereco.cep) {
          this.formGroup.patchValue({
            ['cep' + sufixoComp]: this.mascararCep(endereco.cep)
          });
        }
        if (this.formGroup.get('endereco' + sufixoComp) && endereco.endereco) {
          this.formGroup.patchValue({
            ['endereco' + sufixoComp]: this.mascararString(endereco.endereco)
          });
        }
        if (this.formGroup.get('numero' + sufixoComp) && endereco.numero) {
          this.formGroup.patchValue({
            ['numero' + sufixoComp]: this.mascararNumeroEndereco(endereco.numero)
          });
        }
        if (this.formGroup.get('bairro' + sufixoComp) && endereco.bairro) {
          this.formGroup.patchValue({
            ['bairro' + sufixoComp]: this.mascararString(endereco.bairro)
          });
        }
        if (this.formGroup.get('complemento' + sufixoComp) && endereco.complemento) {
          this.formGroup.patchValue({
            ['complemento' + sufixoComp]: this.mascararString(endereco.complemento)
          });
        }
      }
    }

    // Armazene os dados completos sem mascaras em uma variável para envio posterior
    this.planoService.dadosCompletosDesmascarados = dados;
  }

  private buscarContratosARenovar(dataC): void {
    this.negociacaoService.lancarContratoConcomitanteAoInvesDeRenovar = false;
    const exibirBotaoLancarNovoContratoAoInvesDeRenovar =
      this.convertStringToBoolean(this.empresaService.config.permitecontratosconcomintante) &&
      this.empresaService.config.permiteRenovacaoDeContrato;
    this.alunoService
      .contratosRenovarComPermissao(
        this.negociacaoService.chave,
        this.empresaService.unidadeSelecionada.codigo,
        dataC.codigo,
      )
      .subscribe((data: any) => {
        console.log('Buscado ' + data);
        if (data.erro) {
          Swal.fire({
            type: 'error',
            title: 'Erro',
            text: data.erro,
            showConfirmButton: true,
          });
        } else if (data.return.length > 0) {
          const contratoTemp: Contrato = this.contratoAdapter.adapt(data.return[0]);

          if (!contratoTemp.codigoPlano) {
            this.planoService.permitirRenovacao = false;
            this.planoService.contratoRematricula = false;
            this.consultarPlano();
            this.setarPlano(this.getVendaJsonDecrypt());
            return;
          }
          if (contratoTemp.renovacao && contratoTemp.renovacao === true) {
            this.verificaMenorDeIdadeNoModalRenovarContrato = true;
            Swal.fire({
              title: this.translateService.instant('checkout.contrato-encontrado-na-base'),
              text: this.translateService
                .instant('checkout.renovar-contrato-existente')
                .replace('{nomePlanoAntigo}', contratoTemp.nomePlano)
                .replace('{vigenciaContrato}', contratoTemp.vigenciaAteAjustada)
                .replace('{nomePlanoNovo}', this.planoSelecionado.nome),
              type: 'warning',
              showCancelButton: true,
              confirmButtonColor: '#3085d6',
              cancelButtonColor: exibirBotaoLancarNovoContratoAoInvesDeRenovar ? '#d77b39' : '#d33',
              confirmButtonText: exibirBotaoLancarNovoContratoAoInvesDeRenovar ?
                this.translateService.instant('checkout.renovar-contrato-existente-2') :
                this.translateService.instant('checkout.sim-renovar'),
              cancelButtonText: exibirBotaoLancarNovoContratoAoInvesDeRenovar ?
                this.translateService.instant('checkout.lancar-novo-contrato-concomitante') :
                this.translateService.instant('checkout.nao-renovar'),
            }).then((result) => {
              if (result.value) {
                this.planoService.permitirRenovacao = true;
              } else if (exibirBotaoLancarNovoContratoAoInvesDeRenovar) {
                this.negociacaoService.lancarContratoConcomitanteAoInvesDeRenovar = true;
              } else {
                this.planoService.permitirRenovacao = false;
              }
              this.consultarPlano();
              this.setarPlano(this.getVendaJsonDecrypt());
              this.verificaMenorDeIdadeAoCarregarDadosDaConsulta(dataC.dataNascimento);
            });

            return;
           } else if (contratoTemp.rematricula && contratoTemp.rematricula === true) {
              this.planoService.contratoRematricula = true;
           }
          }
        this.planoService.permitirRenovacao = false;
        this.consultarPlano();
        this.setarPlano(this.getVendaJsonDecrypt());
      });
  }

  convertStringToBoolean(value): boolean {
    if (typeof value === 'string') {
      return JSON.parse(value.toLowerCase());
    }
    return value;
  }

  public get Validators() {
    return Validators;
  }

  public get planoSelecionado(): Plano {
    return this.planoService.planoSelecionado;
  }

  public get produtosSelecionados(): Array<VendaProduto> {
    return this.produtoService.produtosSelecionados;
  }

  public get config(): Config {
    return this.empresaService.config;
  }

  public get unidadeSelecionada(): Empresa {
    return this.empresaService.unidadeSelecionada;
  }

  public get apresentarCartaoVenda(): boolean {
    if (this.empresaService.config.usarFormaPagamentoPlanoProduto && this.vendasConfigsFormaPagamentoPlanoProduto.length > 0) {
      let apresentarCartaoConfirmado = false;
      const apresentarCartaoVendaBackup = this.empresaService.config.apresentarCartaoVenda;
      this.empresaService.config.apresentarCartaoVenda = false;
      let existeConfigPlanoProduto = false;
      this.vendasConfigsFormaPagamentoPlanoProduto.forEach(vFpp => {
        if (vFpp.produto == 0 && vFpp.plano > 0 && this.planoSelecionado && this.planoSelecionado.codigo == vFpp.plano) {
          existeConfigPlanoProduto = true;
          if (vFpp.formaPagamento == '3') {
            apresentarCartaoConfirmado = true;
            this.empresaService.config.apresentarCartaoVenda = true;
          }
        }
        if (vFpp.plano == 0 && vFpp.produto > 0 &&
          this.produtosSelecionados && (this.produtosSelecionados.filter(p => p.produto == vFpp.produto)[0] !== undefined)) {
          existeConfigPlanoProduto = true;
          if (vFpp.formaPagamento == '3') {
            apresentarCartaoConfirmado = true;
            this.empresaService.config.apresentarCartaoVenda = true;
          }
        }
      });
      if (!existeConfigPlanoProduto) {
        this.empresaService.config.apresentarCartaoVenda = apresentarCartaoVendaBackup;
      }
      return (existeConfigPlanoProduto && (apresentarCartaoConfirmado && this.empresaService.config.apresentarCartaoVenda)) ||
        (!existeConfigPlanoProduto && this.empresaService.config.apresentarCartaoVenda);
    } else {
      return this.empresaService.config.apresentarCartaoVenda;
    }
  }

  public get apresentarPixVenda(): boolean {
    //Condição especifica do cliente que pediu para cobrar no Pix e guardar o cartão
    //Se não entrar nela, segue o fluxo normal

    let planoSelecionaEstaPreenchido = this.planoSelecionado !== undefined && this.planoSelecionado !== null;
    let configPrimeiroCobrancaPixEGuardarCartao = this.empresaService.config.primeiraCobrancaPixEGuardarCartao;

    if (configPrimeiroCobrancaPixEGuardarCartao) {
      return false;
    } else if (this.empresaService.config.usarFormaPagamentoPlanoProduto && this.vendasConfigsFormaPagamentoPlanoProduto.length > 0) {
      let apresentarPixConfirmado = false;
      let existeConfigPlanoProduto = false;
      this.vendasConfigsFormaPagamentoPlanoProduto.forEach(vFpp => {
        if (vFpp.produto == 0 && vFpp.plano > 0 && this.planoSelecionado && this.planoSelecionado.codigo == vFpp.plano) {
          existeConfigPlanoProduto = true;
          if (vFpp.formaPagamento == '1') {
            apresentarPixConfirmado = true;
          }
        }
        if (vFpp.plano == 0 && vFpp.produto > 0 &&
          this.produtosSelecionados && (this.produtosSelecionados.filter(p => p.produto == vFpp.produto)[0] !== undefined)) {
          existeConfigPlanoProduto = true;
          if (vFpp.formaPagamento == '1') {
            apresentarPixConfirmado = true;
          }
        }
      });
      return (existeConfigPlanoProduto && (apresentarPixConfirmado && this.empresaService.config.apresentarPixVenda &&
        (this.planoSelecionado == null || (this.planoSelecionado && !this.planoSelecionado.renovavelAutomaticamente)))) ||
        (!existeConfigPlanoProduto && this.empresaService.config.apresentarPixVenda &&
        (this.planoSelecionado == null || (this.planoSelecionado && !this.planoSelecionado.renovavelAutomaticamente))
      );
    } else {
      return (
        this.empresaService.config.apresentarPixVenda &&
        (this.planoSelecionado == null || (this.planoSelecionado && !this.planoSelecionado.renovavelAutomaticamente))
      );
    }
  }

  public get apresentarTipoDocumento(): boolean {
    return this.empresaService.config.exibirTipoDocumentoTelaVendasOnline;
  }

  private obterConfigsFormaPagamentoPlanoProduto() {
    this.empresaService.obterDadosFormaPagamentoPlanoProduto(this.negociacaoService.chave, this.negociacaoService.codunidade)
      .subscribe(data => {
        if (data.hasOwnProperty('return')) {
          this.vendasConfigsFormaPagamentoPlanoProduto = data.return;
        }
      });
  }

  public get apresentarBoletoVenda(): boolean {
    if (this.empresaService.config.usarFormaPagamentoPlanoProduto && this.vendasConfigsFormaPagamentoPlanoProduto.length > 0) {
      let apresentarBoletoConfirmado = false;
      let existeConfigPlanoProduto = false;
      this.vendasConfigsFormaPagamentoPlanoProduto.forEach(vFpp => {
        if (vFpp.produto == 0 && vFpp.plano > 0 && this.planoSelecionado && this.planoSelecionado.codigo == vFpp.plano) {
          existeConfigPlanoProduto = true;
          if (vFpp.formaPagamento == '2') {
            apresentarBoletoConfirmado = true;
          }
        }
        if (vFpp.plano == 0 && vFpp.produto > 0 &&
          this.produtosSelecionados && (this.produtosSelecionados.filter(p => p.produto == vFpp.produto)[0] !== undefined)) {
          existeConfigPlanoProduto = true;
          if (vFpp.formaPagamento == '2') {
            apresentarBoletoConfirmado = true;
          }
        }
      });
      return (existeConfigPlanoProduto && (apresentarBoletoConfirmado && this.empresaService.config.apresentarBoletoVenda &&
        (this.planoSelecionado == null || (this.planoSelecionado && !this.planoSelecionado.renovavelAutomaticamente)))) ||
        (!existeConfigPlanoProduto && this.empresaService.config.apresentarBoletoVenda &&
        (this.planoSelecionado == null || (this.planoSelecionado && !this.planoSelecionado.renovavelAutomaticamente))
      );
    } else {
      return (
        this.empresaService.config.apresentarBoletoVenda &&
        (this.planoSelecionado == null || (this.planoSelecionado && !this.planoSelecionado.renovavelAutomaticamente))
      );
    }
  }

  public get widthNavegador(): boolean {
    return window.outerWidth >= 768;
  }

  public addCupomEnter() {
    this.validarCupom(this.formGroup.get('cupomdesconto').value);
  }

  public validarCupom(cupom): void {
    Swal.fire({
      title: this.translateService.instant('checkout.validando-seu-cupom'),
      onOpen: function () {
        Swal.showLoading();
      },
    });
    this.negociacaoService
      .validarCupom(
        cupom,
        this.planoService.planoSelecionado.nome + ';empresa=' + this.empresaService.unidadeSelecionada.codigo,
      )
      .subscribe((data) => {
        this.negociacaoService.cupom = data;
        if (this.negociacaoService.cupom && this.negociacaoService.cupom.numeroCupom) {
          Swal.fire({
            type: 'success',
            title: this.translateService.instant('checkout.cupom-valido'),
            text: this.translateService.instant('checkout.clique-em-ok-e-prossiga-com-os-dados-da-sua-compra'),
            showConfirmButton: true,
          });
          this.negociacaoService.simularVenda(this.getVendaJsonDecrypt(), this.negociacaoService.codunidade);
        } else {
          this.negociacaoService.cupom = null;
          Swal.fire({
            type: 'error',
            title: this.translateService.instant('checkout.cupom-invalido-ou-expirado'),
            text:
              window.localStorage.getItem('ERROCUPOM') +
              this.translateService.instant('checkout.insira-os-dados-de-outro-cupom'),
            showConfirmButton: true,
          });
        }
      });
  }

  private getMensagemConfirmacaoLaudoMedicoParq(): string {
    let nomeDocumentoParaApresentar = 'laudo médico';

    if (this.empresaService.config.configSescHabilitada) {
      nomeDocumentoParaApresentar = 'documento oficial';
    }

    const msg = 'Ao prosseguir com a compra, você está assumindo a responsabilidade e o '
      + 'compromisso de apresentar um ' + nomeDocumentoParaApresentar
      + ' liberando-o para a prática de atividades físicas presencialmente na unidade.';

    return msg;
  }

  public validarParq(): void {
    let apresentarPagamento = true;
    this.negociacaoService.parq.perguntas.forEach(p => {
      p.respostas.forEach(r => {
        if (r.value === null || r.value === undefined) {
          apresentarPagamento = false;
          Swal.fire({
            type: 'error',
            title: 'Par-Q Incompleto!',
            text: 'Por favor responder todas as perguntas do questionario.',
            showConfirmButton: true,
          });
        }
        if (r.descricao === 'Sim' && r.value === 'TRUE') {
          apresentarPagamento = false;
          Swal.fire({
            title: 'Par-Q Positivo!',
            text: this.getMensagemConfirmacaoLaudoMedicoParq(),
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: this.translateService.instant('Prosseguir'),
            cancelButtonText: this.translateService.instant('Abandonar')
          }).then((result) => {
            if (result.value) {
              this.parqValidado = true;
            }
          });
        }
      });
    });
    if (apresentarPagamento) {
      this.parqValidado = true;
      Swal.fire({
        type: 'success',
        title: 'Par-Q Negativo!',
        text: 'As respostas foram validadas com sucesso.',
        showConfirmButton: true,
      });
    }
  }

  public get cupom(): Cupom {
    return this.negociacaoService.cupom;
  }

  public get parq(): Parq {
    return this.negociacaoService.parq;
  }

  public limparCupom() {
    this.negociacaoService.cupom = null;
  }

  public get moeda(): string {
    return this.empresaService.unidadeSelecionada.moeda;
  }

  private isFormularioPreenchido(nomeForm): any {
    return this.formGroup.get(nomeForm) && this.formGroup.get(nomeForm).value;
  }

  public get aulasSelecionadas() {
    return this.aulasService.aulasSelecionadas.length;
  }

  public usaSistemaInternacional(): boolean {
    try {
      return (
        window.localStorage.getItem('usarSistemaInternacional') !== undefined &&
        window.localStorage.getItem('usarSistemaInternacional').toUpperCase() === 'TRUE'
      );
    } catch (e) {
      return true;
    }
  }

  public getLabelCampoObrigatorioOuOpcional(): string {
    try {
      if (this.menorIdade) {
        return 'global.campoopcional';
      } else {
        return 'global.campoobrigatorio';
      }
    } catch (e) {
      return 'global.campoobrigatorio';
    }
  }

  public check(event) {
    const codPerg = event.currentTarget.id.split('_')[0];
    const codResp = event.currentTarget.id.split('_')[1];
    this.negociacaoService.parq.perguntas.forEach(p => {
      if (p.codigo === Number(codPerg)) {
        p.respostas.forEach(r => {
          if (r.codigo === Number(codResp)) {
            r.value = 'TRUE';
          } else {
            r.value = 'FALSE';
          }
        });
      }
    });
  }

  public obterParq() {
      this.negociacaoService.obterURlTw().subscribe(linkTw => {
        this.negociacaoService.obterFormularioParq(linkTw, this.negociacaoService.codunidade).subscribe(dados => {
          this.negociacaoService.parq = dados.parq;
          this.negociacaoService.apresentarLeiParq = dados.apresentarLeiParq;
          this.negociacaoService.siglaEstadoLeiParq = dados.siglaEstadoLeiParq;
        });
      });
  }

  public preencherNascimentoTurmaVerificandoIdade(): void {
    if (this.formGroup && this.formGroup.get('dataNascimento') &&
      this.planoService && this.planoService.planoSelecionado && this.planoService.planoSelecionado.vendaComTurma) {
      this.formGroup.get('dataNascimento').setValue(this.converterData(this.nascimentoService.getDataNascimento()));
      this.enableShowErrorInput = false;
      this.formGroup.get('dataNascimento').disable();
    }
    this.verificaMenorDeIdade();
  }

  public verificaMenorDeIdade(): void {
    if (this.formGroup.get('dataNascimento').value) {
      this.negociacaoService.menorIdade = false;
      const dataAtual = new Date();
      const [dia, mes, ano] = this.formGroup
        .get('dataNascimento')
        .value.split('/')
        .map((a) => parseInt(a, 10));
      const dataNascimento = new Date(ano, mes - 1, dia);

      let idade = dataAtual.getFullYear() - dataNascimento.getFullYear();
      const m = dataAtual.getMonth() - dataNascimento.getMonth();

      if (m < 0 || (m === 0 && dataAtual.getDate() < dataNascimento.getDate())) {
        idade--;
      }

      if (idade < 18) {
        this.menorIdade = true;
        this.negociacaoService.menorIdade = true;
        localStorage.setItem('menor', 'true');
        Swal.fire({
          type: 'info',
          title: this.translateService.instant('checkout.aluno-menor-idade'),
          text: this.translateService.instant('checkout.por-favor-incluir-ao-menos-um-responsavel'),
          showConfirmButton: true,
        });
      } else {
        this.menorIdade = false;
        localStorage.setItem('menor', 'false');
      }
    }
  }

  public verificaCpfs(campo): void {
    const cpf = this.formGroup.get('cpf');
    const cpfMae = this.formGroup.get('cpfMae');
    const cpfPai = this.formGroup.get('cpfPai');

    if (cpf.value !== null) {
      if (cpf.value === cpfMae.value) {
        this.alertSemelhancaCpfs(campo, cpfMae);
      } else if (cpf.value === cpfPai.value) {
        this.alertSemelhancaCpfs(campo, cpfPai);
      } else {
        if (campo === 1) {
          this.requiredCpf(cpfMae, cpfPai);
        } else {
          this.requiredCpf(cpfPai, cpfMae);
        }
      }
    }
  }

  private alertSemelhancaCpfs(campo, cpf) {
    let responsavel: string;
    campo === 1 ? ((responsavel = 'a Mãe'), cpf.setValue('')) : ((responsavel = 'o Pai'), cpf.setValue(''));
    Swal.fire({
      type: 'error',
      title: 'CPF inválido!',
      text: 'O CPF d' + responsavel + ' deve ser diferente do aluno!',
      showConfirmButton: true,
    });
  }

  private requiredCpf(cpf1, cpf2) {
    if (cpf1.value === null || cpf1.value === '') {
      if (cpf1.value === '') {
        cpf2.setValidators(Validators.required);
        cpf2.updateValueAndValidity();
      }
    } else {
      cpf2.clearValidators();
      cpf2.updateValueAndValidity();
    }
  }

  public verificaResponsavel(campo): void {
    const responsavelMae = this.formGroup.get('responsavelMae');
    const responsavelPai = this.formGroup.get('responsavelPai');

    if (campo === 1) {
      this.requiredResponsavel(responsavelMae, responsavelPai);
    } else {
      this.requiredResponsavel(responsavelPai, responsavelMae);
    }
  }

  private requiredResponsavel(responsavel1, responsavel2): void {
    if (responsavel1.value === null || responsavel1.value === '') {
      if (responsavel1.value === '') {
        responsavel2.setValidators(Validators.required);
        responsavel2.updateValueAndValidity();
      }
    } else {
      responsavel2.clearValidators();
      responsavel2.updateValueAndValidity();
    }
  }

  private alunoAtivoEDifUnidade(dados): boolean {
    return dados.empresa !== Number(this.negociacaoService.codunidade) && dados.situacao === 'Ativo';
  }

  public formaCartao(): boolean {
    return this.formaPagamento === FormaPagamento.CARTAO;
  }

  public formaPix(): boolean {
    return this.formaPagamento === FormaPagamento.PIX;
  }

  public formaBoleto(): boolean {
    return this.formaPagamento === FormaPagamento.BOLETO;
  }

  public cobrarCartao(): void {
    this.formaPagamento = FormaPagamento.CARTAO;
  }

  public cobrarPix(): void {
    this.formaPagamento = FormaPagamento.PIX;
    this.recaptchaV3Service.execute('importantAction').subscribe((token) => {
      const dto = this.getVendaJsonDecrypt();
      this.debitoPix = new DebitoPixComponent(
        this.negociacaoService,
        this.empresaService,
        this.alunoService,
        this.translateService,
        this.router,
        this.analitycsPixel
      );
      this.debitoPix
        .gerarPixContrato(dto, token)
        .then((json: string) => {
          const jsonRetorno = JSON.parse(json);
          if (jsonRetorno.sucesso === true) {
            this.url = jsonRetorno.UrlQRcode;
            this.debitoPix.url = this.url;
            this.cobrancaGeradaComSucesso = true;
            if (this.empresaService && this.empresaService.config && this.empresaService.config.primeiraCobrancaPixEGuardarCartao) {
              this.estaNaTelaDePagamentoPixAposFluxoDeAdicionarCartaoSucesso = true;
            }
          } else {
            this.estaNaTelaDePagamentoPixAposFluxoDeAdicionarCartaoSucesso = false;
            this.limparFormaPagamento();
          }
        })
        .catch((param: any) => {
          this.limparFormaPagamento();
          this.estaNaTelaDePagamentoPixAposFluxoDeAdicionarCartaoSucesso = false;
          this.infoPagamento.naoAprovada(param);
        });
    });
  }

  public cobrarBoleto(): void {
    this.formaPagamento = FormaPagamento.BOLETO;
    this.recaptchaV3Service.execute('importantAction').subscribe((token) => {
      const dto = this.getVendaJsonDecrypt();
      this.boleto = new BoletoComponent(
        this.negociacaoService,
        this.empresaService,
        this.alunoService,
        this.translateService,
        this.router,
        this.analitycsPixel
      );
      this.boleto
        .gerarBoletoVenda(dto, token)
        .then((json: string) => {
          const jsonRetorno = JSON.parse(json);
          if (jsonRetorno.sucesso === true) {
            this.url = jsonRetorno.boleto_url;
            this.vencimento = jsonRetorno.boleto_vencimento;
            this.valor = jsonRetorno.boleto_valor;
            this.linhaDigitavel = jsonRetorno.boleto_linha_digitavel;
            this.status = this.url.length > 0 ? 'sucesso' : 'erro';
            this.boleto.url = this.url;
            this.cobrancaGeradaComSucesso = true;
            this.variosBoletos = jsonRetorno.boleto_qtd > 1;
          } else {
            this.limparFormaPagamento();
          }
        })
        .catch((param: any) => {
          console.log(param);
          this.limparFormaPagamento();
          this.infoPagamento.naoAprovada(param);
        });
    });
  }

  public get mascaraCep() {
    return this.localizationService.getMascaraCep();
  }

  public get mascaraData(): { mask: (string | RegExp)[] } {
    return this.localizationService.getMascaraData();
  }

  // converte a data dos outros padroes para o padrao br
  private converterDataInternacionalizacaoParaPadraoPtBr(data: string) {
    if (this.usaSistemaInternacional() && window.localStorage.getItem('locale') !== undefined) {
      switch (window.localStorage.getItem('locale')) {
        case 'en_US':
          // converter do padrao MM/DD/YYYY para DD/MM/YYYY
          return data.split('/')[1] + '/' + data.split('/')[0] + '/' + data.split('/')[2];
        default:
          return data;
      }
    } else {
      return data;
    }
  }

  public formaNenhuma(): boolean {
    return this.formaPagamento === FormaPagamento.NENHUMA;
  }

  private limparFormaPagamento() {
    if (this.empresaService.config && this.empresaService.config.apresentarCartaoVenda) {
      this.formaPagamento = FormaPagamento.CARTAO;
    } else {
      this.formaPagamento = FormaPagamento.NENHUMA;
    }
  }

  obterPixelMeta(): string {
    if (this.empresaService && this.empresaService.config && this.empresaService.config.pixelId) {
      return this.empresaService.config.pixelId;
    }
    return null;
  }

  obterTokenApiConversaoMeta(): string {
    if (this.empresaService && this.empresaService.config && this.empresaService.config.tokenApiConversao) {
      return this.empresaService.config.tokenApiConversao;
    }
    return null;
  }

  consultarPlano() {
    this.planoService.obterPlano(this.negociacaoService.chave, this.negociacaoService.codunidade,
      this.planoService.planoSelecionado.codigo).subscribe(
      (plano) => {
        this.planoService.planoSelecionado = plano;
      });
  }

  isExibirCampo(campo: string): boolean {
    if (this.estaNaTelaDePagamentoPixAposFluxoDeAdicionarCartaoSucesso) {
      return false;
    }
    if (this.existePlanoSelecionado && this.existeProdutoSelecionado) {
      return this.config.camposAdicionaisProdutoPlano.indexOf(campo) > -1;
    }
    if (this.existePlanoSelecionado) {
      return this.config && this.config.camposAdicionais.indexOf(campo) > -1;
    } else if (campo === 'ParQ') {
      return false;
    }
    // Cadastro Visitante ou Venda de produto
    return this.config && this.config.camposAdicionaisProduto.indexOf(campo) > -1;
  }

  isExibirCampoPessoaCompartilhamento(campo: string): boolean {
  return this.config && this.config.camposAdicionaisProduto.indexOf(campo) > -1;
}

  get existePlanoSelecionado(): boolean {
    return this.planoSelecionado && this.planoSelecionado.codigo > 0;
  }

  get existeProdutoSelecionado(): boolean {
    return this.produtosSelecionados && this.produtosSelecionados.length > 0;
  }

  get existeLocacaoSelecionada(): boolean {
    return this.locacaoAmbienteService.locacoesSelecionadas && this.locacaoAmbienteService.locacoesSelecionadas.length > 0;
  }

  public toggleCompartilharPlanoCheckbox() {
    this.exibirCamposCompartilhamentoPlano = !this.exibirCamposCompartilhamentoPlano;
    this.planoService.setExibirCamposCompartilhamentoPlano(this.exibirCamposCompartilhamentoPlano);
  }

  converterData(formatoOriginal: string): string {
    const partes = formatoOriginal.split('/');
    const ano = partes[0];
    const mes = partes[1];
    const dia = partes[2];
    const dataConvertida = `${dia}/${mes}/${ano}`;
    return dataConvertida;
  }

  private exibirMensagemRestricao(unidadesRestricoes: []) {
    this.formGroup.get('cpf').setValue('');

    let mensagem = `Seu cadastro está bloqueado devido a pendências na(s) unidade(s): #msg.
                    Para resolver esta situação e reativar seus serviços, solicitamos que entre em contato com
                     a(s) referida(s) unidade(s) para regularizar suas pendências.`;

    let unidades = '';
    unidadesRestricoes.forEach((unidade) => {
      unidades += unidades === '' ? unidade : ', ' + unidade;
    });
    mensagem = mensagem.replace('#msg', unidades);
    Swal.fire({
      type: 'warning',
      title: 'Aviso',
      text: mensagem,
      showConfirmButton: true
    });
  }

  obteveCartaoAgoraCobrarPix(data: string) {
    console.log('Dados recebidos do filho:', data);
    if (data === 'true') {
      this.cobrarPix();
    }
  }

  isApresentarLeiParqRJ(): boolean {
    return this.negociacaoService.apresentarLeiParq && this.negociacaoService.siglaEstadoLeiParq === 'RJ';
  }

  isApresentarLeiParqGO(): boolean {
    return this.negociacaoService.apresentarLeiParq && this.negociacaoService.siglaEstadoLeiParq === 'GO';
  }

  private mascararNome(nome: string): string {
    // Separar o nome completo em partes (palavras)
    const partesNome = nome.split(' ');

    // Garantir que haja pelo menos um nome
    if (partesNome.length === 0) {
      return nome;
    }

    // Primeiro nome, deve ser exibido completamente
    let resultado = partesNome[0];

    // Para todos os nomes intermediários, devem ser mascarados completamente
    for (let i = 1; i < partesNome.length - 1; i++) {
      resultado += ' ' + partesNome[i][0] + '*'.repeat(partesNome[i].length - 1);
    }

    // Se houver sobrenome, exibe as duas primeiras letras e mascara o resto
    if (partesNome.length > 1) {
      const sobrenome = partesNome[partesNome.length - 1];
      resultado += ' ' + sobrenome.slice(0, 2) + '*'.repeat(sobrenome.length - 2);
    }

    return resultado;
  }

  private mascararCpf(cpf: string): string {
    // Remover todos os caracteres que não sejam números
    const cpfLimpo = cpf.replace(/\D/g, '');

    // Verificar se o CPF limpo possui 11 dígitos e aplicar a máscara
    if (cpfLimpo.length === 11) {
      return cpfLimpo.replace(/^(\d{3})(\d{3})(\d{3})(\d{2})$/, '$1.***.***-**');
    }

    // Se o CPF não tiver 11 dígitos, retornar o valor original
    return cpf;
  }

  private mascararEmail(email: string): string {
    const [localPart, domain] = email.split('@');

    // Máscara da parte local, mantendo os 4 primeiros caracteres e substituindo o restante por "****"
    const localPartMascarado = `${localPart.slice(0, 4)}****@`;

    // Máscara do domínio inteiro
    const dominioMascarado = domain.replace(/./g, '*');

    return `${localPartMascarado}${dominioMascarado}`;
  }

  private mascararRG(rg) {
    // Verifica se o RG tem comprimento suficiente para aplicar a máscara
    if (rg.length > 2) {
      return rg.substring(0, 2) + rg.substring(2, rg.length - 1).replace(/\d/g, '*') + rg.charAt(rg.length - 1);
    }
    return rg;  // Caso o RG tenha apenas 2 caracteres, não há o que mascarar
  }

  private mascararString(endereco) {
    const palavras = endereco.split(' '); // Divide o endereço em palavras
    const palavrasMascaradas = palavras.map((palavra, index) => {
      if (index === 0) {
        // Primeira palavra: mantém os 3 primeiros caracteres abertos e mascara o restante
        return palavra.slice(0, 3) + palavra.slice(3).replace(/\S/g, '*');
      } else {
        // Outras palavras: mantém o primeiro caractere aberto e mascara o restante
        return palavra.slice(0, 1) + palavra.slice(1).replace(/\S/g, '*');
      }
    });

    return palavrasMascaradas.join(' '); // Junta as palavras mascaradas novamente em uma string
  }

  private mascararNumeroEndereco(numero) {
    // Mantém o primeiro caractere e mascara o restante
    return numero.charAt(0) + numero.slice(1).replace(/\S/g, '*');
  }

  private mascararTelefone(telefone: string): string {
    if (!telefone) {
      return ''; // Retorna vazio se o telefone for nulo ou indefinido
    }

    // Remove quaisquer caracteres que não sejam números
    const telefoneLimpo = telefone.replace(/\D/g, '');

    // Aplica a máscara com base no comprimento do número
    if (telefoneLimpo.length === 10) {
      // Telefone fixo (8 dígitos)
      return telefoneLimpo.replace(/(\d{2})(\d{4})(\d{4})/, '($1) ****-$3');
    } else if (telefoneLimpo.length === 11) {
      // Telefone celular (9 dígitos)
      return telefoneLimpo.replace(/(\d{2})(\d{5})(\d{4})/, '($1) *****-$3');
    } else {
      // Retorna o número original caso não seja um telefone válido
      return telefone;
    }
  }

  private mascararCep(cep: string): string {
    return cep.replace(/^(\d{2})\.(\d{3})-(\d{3})$/, '**.***-$3');
  }

  opcaoSexo(): any[] {
    const opcao = [
      {id: '', label: ''},
      {id: 'Masculino', label: this.translateService.instant('masculino')},
      {id: 'Feminino', label: this.translateService.instant('feminino')}
    ];
    return opcao;
  }

  public verificaMenorDeIdadeAoCarregarDadosDaConsulta(dataNasc: string): void {
    if (dataNasc) {
      this.negociacaoService.menorIdade = false;
      const dataAtual = new Date();
      const [dia, mes, ano] = dataNasc
        .split('/')
        .map((a) => parseInt(a, 10));
      const dataNascimento = new Date(ano, mes - 1, dia);

      let idade = dataAtual.getFullYear() - dataNascimento.getFullYear();
      const m = dataAtual.getMonth() - dataNascimento.getMonth();

      if (m < 0 || (m === 0 && dataAtual.getDate() < dataNascimento.getDate())) {
        idade--;
      }

      if (idade < 18) {
        this.menorIdade = true;
        this.negociacaoService.menorIdade = true;
        localStorage.setItem('menor', 'true');
        Swal.fire({
          type: 'info',
          title: this.translateService.instant('checkout.aluno-menor-idade'),
          text: this.translateService.instant('checkout.por-favor-incluir-ao-menos-um-responsavel'),
          showConfirmButton: true,
        });
        this.naoPermiteAlterarDataMenorDeIdade = true;
      } else {
        this.menorIdade = false;
        localStorage.setItem('menor', 'false');
        this.naoPermiteAlterarDataMenorDeIdade = false;
      }
    }
  }

  onFocusOutDataUtilizacao(): void {
      if (this.formGroup.get('dataUtilizacao').value) {
        const dataAtual = new Date();
        dataAtual.setHours(0, 0, 0, 0)

        const [dia, mes, ano] = this.formGroup
          .get('dataUtilizacao')
          .value.split('/')
          .map((a) => parseInt(a, 10));
        const dataUtilizacaoInformada = new Date(ano, mes - 1, dia);

        if(dataUtilizacaoInformada < dataAtual) {
          Swal.fire({
            type: 'warning',
            title: 'Data Inválida',
            text: 'A data de utilização não pode ser anterior à data atual!',
            showConfirmButton: true,
          });
        }
    }
  }

  existeProdutoDiariaSelecionado(): boolean {
    let retorno = false;

    if(this.produtoService && this.produtoService.produtos && this.produtosSelecionados) {

      for(const produtoSelecionado of this.produtosSelecionados) {
        for(const produto of this.produtoService.produtos) {

          if(produto.codigo === produtoSelecionado.produto) {
            if(produto.tipo === 'DI') {
              retorno = true;
              break;
            }
          }

        }
        if(retorno) {
          break;
        }
      }
    }

    return retorno;
  }

}
