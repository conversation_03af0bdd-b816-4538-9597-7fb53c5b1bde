@import '../../../assets/css/variaveis';

@media only screen and (min-width: 768px) {
  pacto-unidade-selecionada.dispositivo-pequeno {
    display: none;
  }

  pacto-unidade-selecionada.dispositivo-grande {
    display: block;
  }
}

@media only screen and (max-width: 769px) {
  pacto-unidade-selecionada.dispositivo-pequeno {
    display: block;
  }

  pacto-unidade-selecionada.dispositivo-grande {
    display: none;
  }
}

.leiacontrato {
  margin: 23px 0;
  font-size: 18px;
}

h1 {
  font-size: 30px;
  color: #2c343b;
}

h4 {
  font-size: 16px;
  color: #bdc3c7;
}

.resumo {
  font-size: 24px;
  color: #bdc3c7;
}

@media only screen and (max-width: 748px) {
  h4 {
    margin-bottom: 5px;
  }

  .validar {
    margin-left: 32%;
  }

  .validar-red {
    margin-left: 32%;
  }
}

.align-icon-info-dia-vencimento {
  position: absolute;
  margin-left: 37%;
  margin-top: 2px;
}

.validar {
  cursor: pointer;
  color: #ffffff;
  margin-top: 3vh;
  height: 5vh;
  border-radius: 4px;
  box-shadow: 0 2px 3px 0 $bordacinza;
  background-color: #25d366;
  display: inline-flex;

  .btn {
    margin-left: 3px;
    text-align: center;
    font-size: 20px;
    width: 100px;
    line-height: 5vh;
    vertical-align: middle;
  }

  .detalhes {
    line-height: 5.5vh;
    width: 30px;
    font-size: 2.2vh;
    vertical-align: middle;
    text-align: left;
  }
}
.three.columns{
  width: 40%;
}
.mtPequeno{
  margin-top: 2vh;
}

.center {
  text-align: center;
}

.sucesso-parq {
  color: #25d166;
  font-weight: bold;
  font-size: 1em;
}

.lblDestaque, .lbl{
  color: #f04100;
  margin-bottom: .8em;
  font-size: 1em;
  font-weight: bold;
}

.rowValidarParq {
  width: 100%;
  text-align: center;
  .validar-parq {
    cursor: pointer;
    color: #ffffff;
    margin-top: 3vh;
    height: 5vh;
    border-radius: 4px;
    box-shadow: 0 2px 3px 0 $bordacinza;
    background-color: #25d366;
    display: inline-flex;

    .btn {
      text-align: center;
      font-size: 20px;
      width: 120px;
      line-height: 5vh;
      vertical-align: middle;
    }

    .detalhes {
      line-height: 5.5vh;
      width: 30px;
      font-size: 2.2vh;
      vertical-align: middle;
      text-align: left;
    }
  }
}

.validar-red {
  cursor: pointer;
  color: #ffffff;
  margin-top: 3vh;
  height: 5vh;
  border-radius: 4px;
  box-shadow: 0 2px 3px 0 $bordacinza;
  background-color: red;
  display: inline-flex;

  .btn {
    margin-left: 3px;
    text-align: center;
    font-size: 20px;
    width: 100px;
    line-height: 5vh;
    vertical-align: middle;
  }

  .detalhes {
    line-height: 5.5vh;
    width: 30px;
    font-size: 2.2vh;
    vertical-align: middle;
    text-align: left;
  }
}

.desconto {
  margin: 10px 2px;
  vertical-align: middle;
  font-weight: bold;
}

.modalidade,
.detalhe {
  margin: 10px 2px;
  display: inline-block;
  font-weight: bold;
  font-size: 16px;
  color: rgba(44, 52, 59, 0.5);

  i,
  span {
    vertical-align: middle;
  }

  i {
    margin-right: 3px;
  }
}

.valor-desc {
  position: absolute;
  margin-top: 1vh;
  text-align: right;
  width: 100%;
  right: 4%;
  color: rgba(44, 52, 59, 0.5);
}

.titulo {
  text-align: center;
  display: block;
  font-weight: 300;
  color: $textoclaro;
}

ul {
  list-style-type: none;
  padding-left: 20px;
  padding-right: 20px;
}

.align-icon-cartao {
  position: absolute;
  margin-left: 4px;
  margin-top: -1px;
  width: 36px;
  height: 36px;
}

.align-icon-pix {
  position: absolute;
  margin-left: 26px;
  margin-top: 4px;
  width: 27px;
  height: 27px;
}

.align-icon-boleto {
  position: absolute;
  margin-left: 15px;
  margin-top: 7px;
  width: 30px;
  height: 20px;
}

.pacto-btn-pagamento {
  background-color: rgb(250, 250, 250);
  border-radius: 3px;
  border-width: 0.1px;
  border-color: rgb(199, 201, 204);
  width: 140px;
  height: 35px;
  font-family: Arial;
  font-size: x-small;
  color: rgb(197, 199, 202);
  font-weight: bold;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  cursor: pointer;
  border: solid 1px #bdc3c7;
}

.pacto-btn-pagamento-sel {
  background-color: rgb(250, 250, 250);
  border-radius: 3px;
  border-width: 0.1px;
  border-color: rgb(7, 109, 189);
  width: 140px;
  height: 35px;
  font-family: Arial;
  font-size: x-small;
  color: rgb(7, 109, 189);
  font-weight: bold;
  border: solid 1px rgb(7, 109, 189);
}


@keyframes fadeDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pessoas-compartilhamento-plano-container {
  margin-top: 1rem;
  color: #797d86;
  display: flex;
  flex-direction: column;
  flex: 1;

  gap: 10px;
  background-color: #fafafa;
  border-radius: 5px;
  padding: 10px;
  animation: fadeDown 0.5s ease-out forwards;

}
