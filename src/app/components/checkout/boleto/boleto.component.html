<div class="container-pix">
  <div class="titulo row">
    <div class="col-12">Dados do boleto</div>
  </div>
  <div class="pacto-padding-medio">
    <div class="row" *ngIf="status == 'sucesso'">
      <div class="col" style="text-align: center">
        <p *ngIf="!variosBoletos">Utilize o <strong>código de barras</strong> abaixo para realizar o pagamento:</p>
        <p *ngIf="variosBoletos">Faça o download dos boletos:</p>
      </div>
    </div>
    <div class="col" style="text-align: center" *ngIf="status == 'sucesso' && !variosBoletos">
      <p>{{linhaDigitavel}}</p>
    </div>
    <div class="col" style="text-align: left" *ngIf="status == 'sucesso' && !variosBoletos">
      <p>Vencimento: {{vencimento}}</p>
      <p>Valor: {{valor}}</p>
    </div>
    <div class="row box-btn" *ngIf="status == 'sucesso'">
      <div class="box-button" (click)="copyTextToClipboard()" *ngIf="!variosBoletos">
        <span>Copiar código </span>
        <img class="imgpix" src="assets/images/icon/copy-white.svg"/>
      </div>
      <a class="box-button-down btn-down" (click)="facebookPixelInsertEventBoleto()" target="_blank">
        <p *ngIf="!variosBoletos" style="margin: 0;">Baixar boleto</p>
        <p *ngIf="variosBoletos">Baixar boletos</p>
      </a>
    </div>
    <div class="row" *ngIf="status == 'erro'">
      <div class="col" style="text-align: center">
        <p>Não foi possível gerar o boleto </p>
      </div>
    </div>
  </div>
</div>
