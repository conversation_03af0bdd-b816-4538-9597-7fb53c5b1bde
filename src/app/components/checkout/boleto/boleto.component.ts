import {Component, Input, OnInit, inject} from '@angular/core';
import {InfoPagamentos} from '../shared/info-pagamentos';
import {FormGroup} from '@angular/forms';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {AlunoService} from '@base-core/aluno/aluno.service';
import {FormaPagamento} from '../shared/enum-forma-pagamento';
import Swal from 'sweetalert2';
import {TranslateService} from '@ngx-translate/core';
import { Router } from '@angular/router';
import { FacebookPixelService } from '@base-core/analytics/facebook-pixel.service';
@Component({
  selector: 'pacto-boleto',
  templateUrl: './boleto.component.html',
  styleUrls: ['./boleto.component.scss']
})
export class BoletoComponent implements OnInit {
  private infoPagamentos: InfoPagamentos;

  @Input() formGroup: FormGroup;
  @Input() chave: any;
  @Input() unidade: any;
  @Input() url: string;
  @Input() linhaDigitavel: string;
  @Input() vencimento: string;
  @Input() valor: string;
  @Input() status: string;
  @Input() variosBoletos = false;
  @Input() pixelId: string;
  @Input() tokenApiConversao: string;

  constructor(private negociacaoService: NegociacaoService, private empresaService: EmpresaService, private alunoService: AlunoService,
              private translateService: TranslateService, private router: Router, private analitycsPixel: FacebookPixelService) {
    this.infoPagamentos = new InfoPagamentos(FormaPagamento.BOLETO, translateService);
    this.infoPagamentos.empresaService = empresaService;
    this.infoPagamentos.negociacaoService = negociacaoService;
    this.infoPagamentos.alunoService = alunoService;
    this.infoPagamentos.formaPagamento = FormaPagamento.BOLETO;
  }

  ngOnInit() {
    this.url = '../assets/images/produto-sem-imagem.png';
    this.infoPagamentos.formGroup = this.formGroup;
  }

  gerar(): Promise<string> {
    this.status = 'gerando';
    localStorage.removeItem('boleto_url');
    localStorage.removeItem('boleto_linha_digitavel');
    const titulo = 'Gerando Boleto';
    Swal.fire({
      title: titulo,
      onOpen: function () {
        Swal.showLoading();
      }
    });
    return new Promise((resolved) => {
      this.negociacaoService.cobrarParcelaBoleto(this.infoPagamentos.getCobrancaJson()).subscribe((result) => {
        console.log(result);
        if (result.hasOwnProperty('return')) {
          const jsonRetorno = JSON.parse(result['return'].toString());
          if (jsonRetorno.sucesso === false) {
            this.infoPagamentos.naoAprovada(jsonRetorno.msg.toString());
          } else {
            Swal.close();
            resolved(result);
          }
        } else {
          this.infoPagamentos.naoAprovada(result['erro'].toString());
        }
      });
    }).then((result: object) => {
      const jsonRetorno = JSON.parse(result['return'].toString());
      localStorage.setItem('boleto_url', jsonRetorno.boleto_url.toString());
      localStorage.setItem('boleto_linha_digitavel', jsonRetorno.boleto_linha_digitavel.toString());
      const retorno = '{"boleto_url": "' + jsonRetorno.boleto_url.toString() + '", ' +
        '"boleto_linha_digitavel": "' + jsonRetorno.boleto_linha_digitavel.toString() + '",' +
        '"boleto_valor": "' + jsonRetorno.boleto_valor.toString() + '",' +
        '"boleto_vencimento": "' + jsonRetorno.boleto_vencimento.toString() + '"}';
      this.status = 'sucesso';
      return retorno;
    });
  }

  copyTextToClipboard(): void {
    try {
      const textArea = document.createElement('textarea');
      textArea.value = this.linhaDigitavel.valueOf();
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      console.log('Dados copiados ' + textArea);
    } catch (err) {
      console.log('Erro ao copiar o qrcode');
    }
  }

  gerarBoletoVenda(venda, captcha): Promise<string> {
    this.status = 'gerando';
    localStorage.removeItem('boleto_url');
    localStorage.removeItem('boleto_linha_digitavel');
    const titulo = 'Gerando Boleto';
    Swal.fire({
      title: titulo,
      onOpen: function () {
        Swal.showLoading();
      }
    });
    return new Promise((resolved) => {
      this.negociacaoService.gravarVendaBoleto(venda, captcha).subscribe((result) => {
        console.log(result);
        if (result.hasOwnProperty('return')) {
          const jsonRetorno = JSON.parse(result['return'].toString());
          if (jsonRetorno.sucesso === false) {
            this.infoPagamentos.naoAprovada(jsonRetorno.msg.toString());
          } else {
            Swal.close();
            resolved(result);
            this.analitycsPixel.triggerCustomEventFacebookPurchase({
              value: this.negociacaoService.valorFinalContrato,
              currency: 'BRL',
              formaPagamento: 'Boleto'
            });
          }
        } else {
          this.infoPagamentos.naoAprovada(result['erro'].toString());
        }
      });
    }).then((result: object) => {
      const jsonRetorno = JSON.parse(result['return'].toString());
      localStorage.setItem('boleto_url', jsonRetorno.boleto_url.toString());
      localStorage.setItem('boleto_linha_digitavel', jsonRetorno.boleto_linha_digitavel.toString());
      const retorno = '{"boleto_url": "' + jsonRetorno.boleto_url.toString() + '", ' +
        '"boleto_linha_digitavel": "' + jsonRetorno.boleto_linha_digitavel.toString() + '",' +
        '"boleto_valor": "' + jsonRetorno.boleto_valor.toString() + '",' +
        '"boleto_qtd": ' + jsonRetorno.boleto_qtd.toString() + ',' +
        '"sucesso": true,' +
        '"boleto_vencimento": "' + jsonRetorno.boleto_vencimento.toString() + '"}';
      this.status = 'sucesso';
      return retorno;
    });
  }

  facebookPixelInsertEventBoleto() {
    let temPixelId = false;
    const valorPixelId = this.pixelId;
    if (valorPixelId === undefined || valorPixelId === null || valorPixelId === '') {
      temPixelId = false;
    }
    if (temPixelId) {
      const params = {
        'value': this.negociacaoService.valorFinalContrato,
        'currency': 'BRL',
        'formaPagamento': 'Boleto'
      };
      this.analitycsPixel.triggerEventFacebookPurchase(params);
    } else {
      window.open(encodeURI(this.url));
    }
  }
}
