import { Component, Input, OnInit } from '@angular/core';
import { NegociacaoService } from '@base-core/negociacao/negociacao.service';
import { EmpresaService } from '@base-core/empresa/empresa.service';
import Swal from 'sweetalert2';
import { InfoPagamentos } from '../shared/info-pagamentos';
import { FormGroup } from '@angular/forms';
import { FormaPagamento } from '../shared/enum-forma-pagamento';
import { AlunoService } from '@base-core/aluno/aluno.service';
import { TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { FacebookPixelService } from '@base-core/analytics/facebook-pixel.service';


@Component({
  selector: 'pacto-debito-pix',
  templateUrl: './debito-pix.component.html',
  styleUrls: ['./debito-pix.component.scss']
})
export class DebitoPixComponent implements OnInit {

  private infoPagamentos: InfoPagamentos;

  @Input() formGroup: FormGroup;

  constructor(
      private negociacaoService: NegociacaoService,
      private empresaService: EmpresaService,
      private alunoService: AlunoService,
      private translateService: TranslateService,
      private router: Router,
      private  analitycsPixel: FacebookPixelService
  ) {
    this.infoPagamentos = new InfoPagamentos(FormaPagamento.PIX, translateService);
    this.infoPagamentos.empresaService = empresaService;
    this.infoPagamentos.negociacaoService = negociacaoService;
    this.infoPagamentos.alunoService = alunoService;
    this.infoPagamentos.formaPagamento = FormaPagamento.PIX;

  }

  public ulQrCode: string;
  public copybutton: boolean = false;

  @Input() url: string;
  @Input() chave: any;
  @Input() unidade: any;

  ngOnInit() {
    this.url = '../assets/images/produto-sem-imagem.png';
    setInterval(() => {
      this.consultarParcelaPix();
    }, 5000);
    this.infoPagamentos.formGroup = this.formGroup;
  }

  gerarPix(): Promise<string> {
    const titulo = 'Gerando pix';
    Swal.fire({
      title: titulo,
      onOpen: function () {
        Swal.showLoading();
      }
    });
    return new Promise((resolved) => {
      this.negociacaoService.cobrarParcelaPix(this.infoPagamentos.getCobrancaJson()).subscribe((result) => {
        console.log(result);
        if (result['status'] === 'False') {
          this.infoPagamentos.naoAprovada(result['return'].toString());
        } else {
          window.localStorage.setItem('msgFinalOperacao', '1');
          console.log(result);
          this.negociacaoService.codigoPix = result['codigo'].toString();
          Swal.close();
          resolved(result);
        }
      });
    }).then((result: object) => {
      this.ulQrCode = result['UrlQRcode'].toString();
      localStorage.setItem('qrtext', result['qrtext'].toString());
      return this.ulQrCode;
    });
  }

  gerarPixContrato(venda, captcha): Promise<string> {
    const titulo = 'Gerando pix';
    Swal.fire({
      title: titulo,
      onOpen: function () {
        Swal.showLoading();
      }
    });
    return new Promise((resolved) => {
      this.negociacaoService.gravarVendaPix(venda, captcha).subscribe((result) => {
        console.log(result);
        if (result.hasOwnProperty('return')) {
          const jsonRetorno = JSON.parse(result['return'].toString());
          if (jsonRetorno.sucesso === false) {
            this.infoPagamentos.naoAprovada(jsonRetorno.msg);
          } else {
            window.localStorage.setItem('msgFinalOperacao', '3');
            if (jsonRetorno.pix !== undefined) {
              this.negociacaoService.codigoPix = jsonRetorno.pix;
              Swal.close();
            } else if (jsonRetorno.sucesso === true && jsonRetorno.mensagem === 'Renovação efetuada com sucesso') {
              this.negociacaoService.codigoPix = 0;
              this.concluir(false, this.translateService.instant('pospagamento.venda-realizada-com-sucesso'), jsonRetorno.mensagem);
            } else {
              Swal.close();
            }
          }
        } else {
          this.infoPagamentos.naoAprovada(result['erro'].toString());
        }
        resolved(result);
      });
    }).then((result: string) => {
      if (result.hasOwnProperty('return')) {
        const jsonRetorno = JSON.parse(result['return'].toString());
        this.ulQrCode = jsonRetorno.UrlQRcode;
        localStorage.setItem('qrtext', jsonRetorno.qrtext);
        return result['return'].toString();
      } else {
        return result['erro'].toString();
      }
    });
  }

  consultarParcelaPix(): void {
    this.negociacaoService.consulatarParcelaPix().subscribe((result) => {
      if (result['status'].toString() == 'CONCLUIDA') {
        this.facebookPixelInsertEventDebitoPix();
        this.infoPagamentos.redirectPosVenda();
      }
    });
  }

  facebookPixelInsertEventDebitoPix() {
    const params = {
      'value': this.negociacaoService.valorFinalContrato,
      'currency': 'BRL',
      'formaPagamento': 'Débito/Pix'
    };
    this.analitycsPixel.triggerEventFacebookPurchase(params);
  }
   copyTextToClipboard(): void {
    this.copybutton = true;
     const textArea = document.createElement('textarea');
     textArea.value = localStorage.getItem('qrtext');
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
      const successful = document.execCommand('copy');
      const msg = successful ? 'sucesso' : 'erro';
      console.log('Dados copiados' + msg);
    } catch (err) {
      console.log('Erro ao copiar o qrcode');
    }
    document.body.removeChild(textArea);
  }

  concluir(emailEnviado: boolean, title: String, texto: string): void {
    this.analitycsPixel.triggerCustomEventFacebookPurchase({
      value: this.negociacaoService.valorFinalContrato,
      currency: 'BRL',
      formaPagamento: 'Débito/Pix'
    });
    Swal.fire({
      type: 'success',
      title: title,
      text: texto,
      showConfirmButton: true,
      onClose: () => {
        this.infoPagamentos.redirectPosVenda();
      }
    });
  }

}

