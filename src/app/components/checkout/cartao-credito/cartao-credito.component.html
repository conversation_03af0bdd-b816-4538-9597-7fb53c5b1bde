<div class="container-cartao" style="margin-bottom: 1rem">

  <div class="titulo row" *ngIf="!apresentarBotaoPrimeiraCobrancaPixEGuardarCartao">
    <div class="col-12">{{'checkout.dados-do-cartao-de-credito' |translate}}</div>
  </div>

  <div class="titulo row" *ngIf="apresentarBotaoPrimeiraCobrancaPixEGuardarCartao">
    <div class="col-12">{{'checkout.dados-do-cartao-de-credito-cobrancas-futuras' |translate}}</div>
  </div>

  <div class="pacto-padding-medio">

    <div class="row pacto-no-margin-padding pacto-mg-top-pequeno">
      <div class="col-12">
        <pacto-input
          idinput="idnomecartao"
          [label]="'checkout.nome-impresso-no-cartao'|translate"
          [name]="'nomecartao'"
          [pactoFormGroup]="formGroup"
        ></pacto-input>
      </div>
    </div>

    <div class="row pacto-no-margin-padding pacto-mg-top-pequeno">
      <div class="col-12 numerocartao">
        <pacto-input
          idinput="idnrcartao"
          [label]="'checkout.numero-do-cartao'|translate"
          [name]="'nrcartao'" [type]="'tel'"
          [textMask]="{mask: mascaracartao, guide: true}"
          [pactoFormGroup]="formGroup"
        ></pacto-input>
        <pacto-bandeira-cartao [numero]="getNumeroCartao()"></pacto-bandeira-cartao>
      </div>
    </div>

    <div class="row pacto-no-margin-padding pacto-mg-top-pequeno">
      <div class="col-6">
        <pacto-input [type]="'tel'"
                     idinput="idvalidade"
                     [textMask]="{mask: mascaravencimento, guide: true}"
                     [label]="'checkout.validade'|translate"
                     [name]="'validade'"
                     [pactoFormGroup]="formGroup"
                     [placeholder]="'checkout.mm-aa'|translate|uppercase"
        ></pacto-input>
      </div>

      <div class="col-1">
      </div>

      <div class="col-4">
        <i *ngIf="getWidthNavegador()" class="tem pct-info align-icon-info-cvv"
           [title]="'checkout.informe-o-codigo-de-seguranca-cvv-de-3-digitos-localizado-no-verso-do-seu-cartao-de-credito'|translate"></i>
        <img *ngIf="getWidthNavegador()" class="align-icon-cvv" src="assets/images/icon/cvv-icon.png" height="47"
             width="44"/>
        <pacto-input idinput="idcvv"
                     [textMask]="{mask: getMaskCVV(), guide: true}"
                     [label]="'checkout.codigo-de-seguranca'|translate" [type]="'tel'"
                     [name]="'cvv'"
                     [pactoFormGroup]="formGroup"
                     placeholder="CVV"
        ></pacto-input>
      </div>
    </div>

    <div class="row pacto-no-margin-padding pacto-mg-top-pequeno" *ngIf="getPlanoSelecionado() && getPlanoSelecionado().parcelamentoOperadora">
      <div class="col-6" *ngIf="getConfig()?.permitirMudarTipoParcelamento">
        <pacto-input [opcoes]="opcoesTipoCredito()"
                     [label]="'Tipo de crédito'"
                     [default]="'Parcelado'"
                     [name]="'tipoCredito'"
                     [pactoFormGroup]="formGroup"
                     [mensagem]="'global.campoobrigatorio'|translate"
        ></pacto-input>
      </div>

      <div class="col-1" *ngIf="getConfig()?.permitirMudarTipoParcelamento">
      </div>

      <div class="col-5"
           *ngIf="exibirSelectDividirEmQuantasVezesLinkAntigoPlanoParcelado()">
        <pacto-input [opcoes]="opcoesParcelamento(getPlanoSelecionado().maxDivisao)"
                     [label]="'checkout.dividir-em-quantas-vezes'|translate"
                     [default]="getPlanoSelecionado().maxDivisao"
                     [name]="'parcelasCartao'"
                     [pactoFormGroup]="formGroup"
                     [mensagem]="'global.campoobrigatorio'|translate"
        ></pacto-input>
      </div>
    </div>

    <div class="row pacto-no-margin-padding aceite">
      <span
        *ngIf="exibirSelectDividirEmQuantasVezesLinkAntigoPlanoParcelado()">
        Será cobrado o valor total do contrato dividido em parcelas de acordo com a opção de parcelamento escolhida.
      </span>

      <span
        *ngIf="(this.formGroup.get('tipoCredito') != null && this.formGroup.get('tipoCredito').value == 'Recorrente')">
        O valor da parcela do contrato será cobrado de forma automática todos os meses sem utilizar o limite do seu cartão.
      </span>
    </div>

      <div class="row pacto-no-margin-padding pacto-mg-top-pequeno"
           *ngIf="getConfig() && getConfig()?.apresentarCPFLinkPag">
        <div class="col-12">
          <pacto-input
            idinput="idcpftitular"
            [label]="'checkout.cpf-do-titular'|translate"
            [name]="'cpftitularcard'" [type]="'tel'"
            [textMask]="{mask: mascaracpf, guide: true}"
            [pactoFormGroup]="formGroup"
            [mensagem]="'global.campoobrigatorio'|translate"
            [placeholder]="'checkout.cpf-titular-do-cartao'|translate"
            [validators]="[Validators.required]"
          ></pacto-input>
        </div>
      </div>

      <div class="row pacto-no-margin-padding pacto-mg-top-pequeno"
           *ngIf="exibirSelectDividirEmQuantasVezesParcelamentoTelaAluno()">
        <div class="col-12">
          <pacto-input [opcoes]="opcoesParcelamentoProduto()"
                       [label]="'checkout.dividir-em-quantas-vezes'|translate"
                       [default]="1"
                       [name]="'parcelasCartao'"
                       [pactoFormGroup]="formGroup"
                       [mensagem]="'global.campoobrigatorio'|translate"
          ></pacto-input>
        </div>
      </div>

<!--    Remover campo pois ele não faz nada atualmente-->
<!--      <div class="row pacto-no-margin-padding pacto-mg-top-pequeno"-->
<!--           *ngIf="getConfig() && (getConfig()?.apresentarDtFaturaLinkPag || isExibirCampo('VENCIMENTO_FATURA'))"-->
<!--      >-->
<!--        <div class="col-12">-->
<!--          <pacto-input [opcoes]="opcoesVencimentoFatura(31)"-->
<!--                       [label]="'checkout.vencimento-da-fatura'|translate"-->
<!--                       [default]="getDiaHoje()"-->
<!--                       [name]="'vencimentoFatura'"-->
<!--                       [pactoFormGroup]="formGroup"-->
<!--                       [mensagem]="'global.campoobrigatorio'|translate"-->
<!--          ></pacto-input>-->
<!--        </div>-->
<!--      </div>-->

    <!--checkbox (Termo de Aceitação)-->
    <div class="row pacto-no-margin-padding pacto-mg-top-pequeno">
      <div class="col-12 checkbox">
        <pacto-input [type]="'checkbox'"
                     [name]="'aceitaTermos'"
                     (change)="getTermos()">
        </pacto-input>
        <span>{{'checkout.aceito-salvar-dados-cartao' | translate}}
          <strong>{{'checkout.seus-dados-protegidos-pci-dss-lgpd' | translate}}</strong>
        </span>
      </div>
    </div>

      <!--termo de aceite para tela de checkout (planos, produtos, etc) (não configurável)-->
      <div *ngIf="router.indexOf('pagamento') < 0 && getPlanoSelecionado() && !isMostrandoContrato()" class="row pacto-no-margin-padding pacto-mg-top-pequeno aceite">
        <pacto-visualizar-documento [tipo]="'aceite'" idinput="idli"
                                    [textoLink]="'global.termoslidos'|translate"></pacto-visualizar-documento>
      </div>

      <!--termo de aceite para links de pagamento (configurável se pode aparecer ou não)-->
      <div *ngIf="router.indexOf('pagamento') >= 0 && isMostrarAceiteLinkPagamento()" class="row pacto-no-margin-padding pacto-mg-top-pequeno aceite">
        <app-visualizar-documento-link-pagamento [tipo]="'aceite link'" idinput="idli"
                                                 [textoLink]="'global.termoslidos'|translate"></app-visualizar-documento-link-pagamento>
      </div>

     <!--Link de pagamento-->
      <a [(class.disabled)]="!aceitaTermo" class="pacto-btn-primary disabled" (click)="cobrarParcela()" *ngIf="router.indexOf('pagamento') >= 0 && !processando && alunoSelecionado().valorCobrar > 0.0 && negociacao()?.cobrarParcelasEmAberto">
       {{"checkout.pagar-agora"|translate}} >>>
      </a>

      <!--Link de cadastro de cartão-->
      <a [(class.disabled)]="!aceitaTermo" class="pacto-btn-primary disabled" (click)="cobrarParcela()" *ngIf="router.indexOf('pagamento') >= 0 && !processando && (alunoSelecionado().valorCobrar === 0.0 || !negociacao()?.cobrarParcelasEmAberto)">
        {{"checkout.adicionar-cartao-agora"|translate}} >>>
      </a>

      <!--Demais vendas-->
      <a [(class.disabled)]="!aceitaTermo" class="pacto-btn-primary disabled" (click)="finalizarVenda()" *ngIf="router.indexOf('pagamento') < 0 && !processando && !apresentarBotaoPrimeiraCobrancaPixEGuardarCartao" id="idfinalizar">
        {{"checkout.pagar-agora"|translate}} >>
      </a>

      <!--Configuração PrimeiraCobrancaPixEGuardarCartao marcada-->
      <a [(class.disabled)]="!aceitaTermo" class="pacto-btn-primary disabled" (click)="validarCartaoECobrarPrimeiraPix()" *ngIf="router.indexOf('pagamento') < 0 && !processando && apresentarBotaoPrimeiraCobrancaPixEGuardarCartao" id="idPrimeiraCobrancaPixEGuardarCartao">
        {{"checkout.adicionar-cartao-agora-e-gerar-pix-para-pagamento"|translate}} >>
      </a>

  </div>
</div>
