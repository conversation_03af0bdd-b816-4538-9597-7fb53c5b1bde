.titulo img {
  margin-top: 10px;
  margin-left: 10px;
}
.titulo {
  color: #2c343b;
  height: 56px;
  line-height: 56px;
  padding: 0 15px;
  border-bottom: solid 1px #bdc3c7;
  background-color: #eff2f7;
  font-weight: bold;
}

pacto-visualizar-documento {
  z-index: 999;
}
.container-cartao {
  width: 100%;
  min-height: 448px;
  border-radius: 4px;
  border: solid 1px #bdc3c7;
  overflow: hidden;
  // margin-bottom: 80px;
  .pacto-btn-primary {
    margin: 20px 15px;
    width: calc(100% - 30px);
    background-color: #25d366;
  }
}

.aceite {
  font-size: 14px;
  padding: 15px;
  color: #2c343b;
}
@media only screen and (max-width: 748px) {
  .pacto-padding-medio {
    padding: 0px;
  }

  .align-icon-cvv {
    display: none;
  }
}
.numerocartao {
  position: relative;
}
.align-icon-info-cvv {
  position: absolute;
  margin-left: 75%;
  margin-top: 3px;
}
.align-icon-cvv {
  position: absolute;
  margin-left: 60%;
  margin-top: 14%;
}

.checkbox pacto-input::ng-deep {
  input {
    box-shadow: none !important;
  }
  small {
    display: none;
  }
}

.checkbox {
  display: flex;
  span {
    margin-left: 10px;
  }
}

a.disabled {
  opacity: 0.4;
  cursor: default;
  pointer-events: none;
}
