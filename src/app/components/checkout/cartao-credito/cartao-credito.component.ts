import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { AulasService } from "@base-core/agenda/aulas.service";
import { <PERSON><PERSON> } from "@base-core/aluno/aluno.model";
import { AlunoService } from "@base-core/aluno/aluno.service";
import { Config } from "@base-core/empresa/config.model";
import { EmpresaService } from "@base-core/empresa/empresa.service";
import { NegociacaoService } from "@base-core/negociacao/negociacao.service";
import { Venda } from "@base-core/negociacao/venda.model";
import { Plano } from "@base-core/plano/plano.model";
import { PlanoService } from "@base-core/plano/plano.service";
import { Produto, VendaProduto } from "@base-core/produto/produto.model";
import { ProdutoService } from "@base-core/produto/produto.service";
import { ReCaptchaV3Service } from "ng-recaptcha";
import Swal from "sweetalert2";
import { FormaPagamento } from "../shared/enum-forma-pagamento";
import { InfoPagamentos } from "../shared/info-pagamentos";
import { TranslateService } from "@ngx-translate/core";
import { FacebookPixelService } from "@base-core/analytics/facebook-pixel.service";
import { HttpClient } from "@angular/common/http";
import { FacebookApiConversaoService } from "@base-core/analytics/facebook-api-conversao.service";
import { Cliente } from "@base-core/cliente/cliente.model";
import { LocalizationService } from "@base-core/service/localization.service";
import { ClienteService } from "@base-core/cliente/cliente.service";
import { ModalidadeService } from "@base-core/modalidade/modalidade-service";
import { TurmaService } from "@base-core/turma/turma.service";
import { CrmMsService } from "@base-core/crm-ms/crm-ms.service";
import { map, switchMap } from "rxjs/operators";
import { of } from "rxjs";
import { OrigemSistemaEnum } from '@base-core/negociacao/enum-origem-sistema';
import {LocacaoAmbienteService} from '@base-core/locacao-ambiente/locacao-ambiente.service';

@Component({
  selector: "pacto-cartao-credito",
  templateUrl: "./cartao-credito.component.html",
  styleUrls: ["./cartao-credito.component.scss"],
})
export class CartaoCreditoComponent implements OnInit {
  @Input() formGroup: FormGroup;
  @Input() pixelId: string;
  @Input() tokenApiConversao: string;
  @Input() dadosRecebidosLinkPagamento: any;
  @Output() aprovouCadastroCartaoPrimeiroPagamentoPix: EventEmitter<string> = new EventEmitter();
  mascaracpf = [
    /\d/,
    /\d/,
    /\d/,
    ".",
    /\d/,
    /\d/,
    /\d/,
    ".",
    /\d/,
    /\d/,
    /\d/,
    "-",
    /\d/,
    /\d/,
  ];
  mascaracartao = [
    /\d/,
    /\d/,
    /\d/,
    /\d/,
    " ",
    /\d/,
    /\d/,
    /\d/,
    /\d/,
    " ",
    /\d/,
    /\d/,
    /\d/,
    /\d/,
    " ",
    /\d/,
    /\d/,
    /\d/,
    /\d/,
  ];
  mascaravencimento = [/\d/, /\d/, "/", /\d/, /\d/];
  router: string;
  processando = false;
  infoPagamento: InfoPagamentos;
  menor: boolean;
  aceitaTermo: boolean = false;
  ipPublico: string = "";
  public exibeCampoDataUtilizacao: boolean = false;

  constructor(
    private empresaService: EmpresaService,
    private _router: Router,
    private negociacaoService: NegociacaoService,
    private aulaService: AulasService,
    private planoService: PlanoService,
    private produtoService: ProdutoService,
    private recaptchaV3Service: ReCaptchaV3Service,
    private alunoService: AlunoService,
    private translateService: TranslateService,
    private analitycsPixel: FacebookPixelService,
    private analitycsApiConversao: FacebookApiConversaoService,
    private localizationService: LocalizationService,
    private http: HttpClient,
    private modalidadeService: ModalidadeService,
    private turmaService: TurmaService,
    private crmService: CrmMsService,
    private locacaoAmbienteService: LocacaoAmbienteService,
  ) {
    this.router = _router.url;
    this.infoPagamento = new InfoPagamentos(
      FormaPagamento.CARTAO,
      this.translateService,
    );
  }

  ngOnInit() {
    this.tryLoad();
    this.exibeCampoDataUtilizacao = this.getConfig().exibeDataUtilizacao;
    this.getIPAddress();
  }

  private getCampo(campo: string, valorPadrao: any = null): any {
    return this.formGroup.get(campo)
      ? this.formGroup.get(campo).value
      : valorPadrao;
  }
  private getCampoData(campo: string): any {
    return this.formGroup.get(campo)
      ? this.localizationService.getDataFormatoPtbr(campo)
      : "";
  }

  getClienteDependentePlanoCompartilhadoByIndex(index: number): Cliente | null {
    // CAMPOS DO FORMULARIO DO DEPENDENTE
    const campos = {
      nome: this.getCampo("nome_comp" + index),
      cpf: this.getCampo("cpf_comp" + index),
      responsavelPai: this.getCampo("responsavelPai_comp" + index),
      responsavelMae: this.getCampo("responsavelMae_comp" + index),
      sexo: this.getCampo("sexo_comp" + index),
      dataNascimento: this.getCampo("dataNascimento_comp" + index),
      email: this.getCampo("email_comp" + index),
      telefone: this.getCampo("telefone_comp" + index),
      cep: this.getCampo("cep_comp" + index),
      endereco: this.getCampo("endereco_comp" + index),
      numero: this.getCampo("numero_comp" + index),
      bairro: this.getCampo("bairro_comp" + index),
      complemento: this.getCampo("complemento_comp" + index),
      cpfMae: this.getCampo("cpfMae_comp" + index),
      cpfPai: this.getCampo("cpfPai_comp" + index),
      rg: this.getCampo("rg_comp" + index),
    };

    // Verifica se pelo menos um campo não é vazio ou nulo..
    const algumCampoPreenchido = Object.values(campos).some((campo) => campo);

    if (!algumCampoPreenchido) {
      return null;
    }

    // Cria e retorna o objeto Cliente se pelo menos um campo é válido..
    return new Cliente(
      this.empresaService.unidadeSelecionada.codigo,
      campos.nome,
      campos.cpf,
      campos.responsavelPai,
      campos.responsavelMae,
      campos.sexo,
      campos.dataNascimento,
      campos.email,
      campos.telefone,
      campos.cep,
      campos.endereco,
      campos.numero,
      campos.bairro,
      campos.complemento,
      campos.cpfMae,
      campos.cpfPai,
      null,
      campos.rg,
      this.negociacaoService.codigoEvento,
      this.negociacaoService.usuarioResponsavel,
      this.negociacaoService.codigoRegistroAcessoPagina,
      null,
      null,
      true,
      this.getCampo("cpf"),
    );
  }

  private getListaClientesDependentes(): Cliente[] {
    const qtdCompartilhamentos =
      this.getPlanoSelecionado().quantidadeCompartilhamentos;

    const clientesCadastradosComoDependentesPlanoCompartilhado: Cliente[] = [];

    if (qtdCompartilhamentos >= 1) {
      for (let i = 0; i < qtdCompartilhamentos; i++) {
        const clienteDependentePreenchido =
          this.getClienteDependentePlanoCompartilhadoByIndex(i);
        if (clienteDependentePreenchido) {
          clientesCadastradosComoDependentesPlanoCompartilhado.push(
            clienteDependentePreenchido,
          );
        }
      }
    }

    return clientesCadastradosComoDependentesPlanoCompartilhado;
  }

  getVendaJson(): Venda {
    let permiteInformarDataUtilizacao: boolean = (this.exibeCampoDataUtilizacao && this.existeProdutoDiariaSelecionado()) ? true : false;
    return new Venda(
      this.empresaService.unidadeSelecionada.codigo,
      this.planoService.planoSelecionado
        ? this.planoService.planoSelecionado.codigo
        : 0,
      this.formGroup.get("nome") ? this.formGroup.get("nome").value : null,
      this.formGroup.get("cpf") ? this.formGroup.get("cpf").value : null,
      this.formGroup.get("sexo") ? this.formGroup.get("sexo").value : null,
      this.formGroup.get("dataNascimento")
        ? this.formGroup.get("dataNascimento").value
        : "",
      this.formGroup.get("email") ? this.formGroup.get("email").value : null,
      this.formGroup.get("nomecartao").value,
      this.formGroup.get("nrcartao").value
        ? this.formGroup.get("nrcartao").value.replace(/ /g, "")
        : "",
      this.formGroup.get("validade").value
        ? this.formGroup.get("validade").value.replace("/", "/20")
        : "",
      this.formGroup.get("cvv").value,
      this.formGroup.get("telefone")
        ? this.formGroup.get("telefone").value
        : null,
      this.formGroup.get("endereco")
        ? this.formGroup.get("endereco").value
        : null,
      this.formGroup.get("numero") ? this.formGroup.get("numero").value : null,
      this.formGroup.get("bairro") ? this.formGroup.get("bairro").value : null,
      this.formGroup.get("complemento")
        ? this.formGroup.get("complemento").value
        : null,
      this.formGroup.get("cep") ? this.formGroup.get("cep").value : null,
      this.formGroup.get("diavencimento")
        ? this.formGroup.get("diavencimento").value
        : null,
      this.formGroup.get("parcelasCartao")
        ? this.formGroup.get("parcelasCartao").value
        : null,
      this.formGroup.get("cupomdesconto")
        ? this.formGroup.get("cupomdesconto").value
        : "",
      this.formGroup.get("cpftitularcard")
        ? this.formGroup.get("cpftitularcard").value
        : null,
      this.formGroup.get("vencimentoFatura")
        ? this.formGroup.get("vencimentoFatura").value
        : 0,
      this.produtoService.produtosSelecionados,
      this.negociacaoService.cobrarParcelasEmAberto,
      this.formGroup.get("dataInicioContrato")
        ? this.formGroup.get("dataInicioContrato").value
        : null,
      this.formGroup.get("responsavelPai")
        ? this.formGroup.get("responsavelPai").value
        : null,
      this.formGroup.get("responsavelMae")
        ? this.formGroup.get("responsavelMae").value
        : null,
      this.formGroup.get("cpfMae") ? this.formGroup.get("cpfMae").value : null,
      this.formGroup.get("cpfPai") ? this.formGroup.get("cpfPai").value : null,
      this.aulaService.codigosAulasSelecionadas(),
      this.planoService.permitirRenovacao,
      this.negociacaoService.categoriaPlano
        ? this.negociacaoService.categoriaPlano
        : null,
      this.negociacaoService.origemCobranca,
      this.negociacaoService.cobrancaAntecipada,
      this.negociacaoService.responsavel,
      this.negociacaoService.token,
      this.planoService.vezesEscolhidasParcelarTaxaMatricula,
      this.formGroup.get("rg") ? this.formGroup.get("rg").value : null,
      this.negociacaoService.codigoEvento,
      this.negociacaoService.usuarioResponsavel,
      this.negociacaoService.codigoRegistroAcessoPagina,
      "",
      this.formGroup.get("tipoCredito")
        ? this.formGroup.get("tipoCredito").value
        : null,
      this.negociacaoService.povoarJsonRespostaParq(this.isExibirCampo("ParQ")),
      this.formGroup.get("cnpj") ? this.formGroup.get("cnpj").value : null,
      this.formGroup.get("nomeResponsavelEmpresa")
        ? this.formGroup.get("nomeResponsavelEmpresa").value
        : null,
      this.formGroup.get("cpfResponsavelEmpresa")
        ? this.formGroup.get("cpfResponsavelEmpresa").value
        : null,
      this.negociacaoService.lancarContratoConcomitanteAoInvesDeRenovar,
      this.negociacaoService.pactoPayComunicacao,
      this.negociacaoService.utm_data,
      this.planoService.getExibirCamposCompartilhamentoPlanoValue()
        ? this.getListaClientesDependentes()
        : [],
      this.modalidadeService.modalidadesSelecionadas,
      this.turmaService.horariosSelecionados, null, false,
      OrigemSistemaEnum.VENDAS_ONLINE,
      this.formGroup.get("dataUtilizacao") ? this.formGroup.get("dataUtilizacao").value : null,
      permiteInformarDataUtilizacao,
      this.locacaoAmbienteService.locacoesSelecionadas && this.locacaoAmbienteService.locacoesSelecionadas.length > 0 ? true : false,
      this.locacaoAmbienteService.locacoesSelecionadas
    );
  }

  getVendaJsonDecrypt(): Venda {
    if (this.planoService && this.planoService.dadosCompletosDesmascarados) {

      let endereco;
      if (this.planoService.dadosCompletosDesmascarados['enderecoCompleto']) {
        endereco = JSON.parse(this.planoService.dadosCompletosDesmascarados['enderecoCompleto'].toString());
      }

      let telefoneDesmascarado = '';
      if (this.planoService.dadosCompletosDesmascarados.telCelular) {
        telefoneDesmascarado = this.planoService.dadosCompletosDesmascarados.telCelular;
      } else if (this.planoService.dadosCompletosDesmascarados.telResidencial) {
        telefoneDesmascarado = this.planoService.dadosCompletosDesmascarados.telResidencial;
      }

      let permiteInformarDataUtilizacao: boolean = (this.exibeCampoDataUtilizacao && this.existeProdutoDiariaSelecionado()) ? true : false;

      return new Venda(
        this.empresaService.unidadeSelecionada.codigo,
        this.planoService.planoSelecionado ? this.planoService.planoSelecionado.codigo : 0,
        (this.formGroup.get('nome') && this.formGroup.get('nome').value != null && this.formGroup.get('nome').value != undefined && this.formGroup.get('nome').value.includes('*') ? this.planoService.dadosCompletosDesmascarados.nome : this.formGroup.get('nome').value),
        (this.formGroup.get('cpf') && this.formGroup.get('cpf').value != null && this.formGroup.get('cpf').value != undefined && this.formGroup.get('cpf').value.includes('*') ? this.planoService.dadosCompletosDesmascarados.cpf : (this.formGroup.get('cpf') ? this.formGroup.get('cpf').value : null)),
        (this.formGroup.get('sexo') && this.formGroup.get('sexo') ? this.formGroup.get('sexo').value : ''),
        (this.formGroup.get('dataNascimento') && this.formGroup.get('dataNascimento').value != null && this.formGroup.get('dataNascimento').value != undefined && this.formGroup.get('dataNascimento').value.includes('*') ? this.planoService.dadosCompletosDesmascarados.dataNascimento : (this.formGroup.get('dataNascimento') ? this.localizationService.getDataFormatoPtbr(this.formGroup.get('dataNascimento').value) : '')),
        (this.formGroup.get('email') && this.formGroup.get('email').value != null && this.formGroup.get('email').value != undefined && this.formGroup.get('email').value.includes('*') ? this.planoService.dadosCompletosDesmascarados.email : this.formGroup.get('email').value),
        (this.formGroup.get("nomecartao").value),
        this.formGroup.get("nrcartao").value ? this.formGroup.get("nrcartao").value.replace(/ /g, "") : "",
        this.formGroup.get("validade").value ? this.formGroup.get("validade").value.replace("/", "/20") : "",
        this.formGroup.get("cvv").value,
        (this.formGroup.get('telefone') && this.formGroup.get('telefone').value != null && this.formGroup.get('telefone').value != undefined && this.formGroup.get('telefone').value.includes('*') ? telefoneDesmascarado : (this.formGroup.get('telefone') ? this.formGroup.get('telefone').value : null)),
        (this.formGroup.get('endereco') && this.formGroup.get('endereco').value != null && this.formGroup.get('endereco').value != undefined && this.formGroup.get('endereco').value.includes('*') ? endereco.endereco : (this.formGroup.get('endereco') ? this.formGroup.get('endereco').value : null)),
        (this.formGroup.get('numero') && this.formGroup.get('numero').value != null && this.formGroup.get('numero').value != undefined && this.formGroup.get('numero').value.includes('*') ? endereco.numero : (this.formGroup.get('numero') ? this.formGroup.get('numero').value : null)),
        (this.formGroup.get('bairro') && this.formGroup.get('bairro').value != null && this.formGroup.get('bairro').value != undefined && this.formGroup.get('bairro').value.includes('*') ? endereco.bairro : (this.formGroup.get('bairro') ? this.formGroup.get('bairro').value : null)),
        (this.formGroup.get('complemento') && this.formGroup.get('complemento').value != null && this.formGroup.get('complemento').value != undefined && this.formGroup.get('complemento').value.includes('*') ? endereco.complemento : (this.formGroup.get('complemento') ? this.formGroup.get('complemento').value : null)),
        (this.formGroup.get('cep') && this.formGroup.get('cep').value != null && this.formGroup.get('cep').value != undefined && this.formGroup.get('cep').value.includes('*') ? endereco.cep : (this.formGroup.get('cep') ? this.formGroup.get('cep').value : null)),
        this.formGroup.get("diavencimento") ? this.formGroup.get("diavencimento").value : null,
        this.formGroup.get("parcelasCartao") ? this.formGroup.get("parcelasCartao").value : null,
        this.formGroup.get("cupomdesconto") ? this.formGroup.get("cupomdesconto").value : "",
        this.formGroup.get("cpftitularcard") ? this.formGroup.get("cpftitularcard").value : null,
        this.formGroup.get("vencimentoFatura") ? this.formGroup.get("vencimentoFatura").value : 0,
        this.produtoService.produtosSelecionados,
        this.negociacaoService.cobrarParcelasEmAberto,
        this.formGroup.get("dataInicioContrato") ? this.formGroup.get("dataInicioContrato").value : null,
        (this.formGroup.get('responsavelPai') && this.formGroup.get('responsavelPai') ? this.formGroup.get('responsavelPai').value : null),
        (this.formGroup.get('responsavelMae') && this.formGroup.get('responsavelMae') ? this.formGroup.get('responsavelMae').value : null),
        (this.formGroup.get('cpfMae') && this.formGroup.get('cpfMae') ? this.formGroup.get('cpfMae').value : null),
        (this.formGroup.get('cpfPai') && this.formGroup.get('cpfPai') ? this.formGroup.get('cpfPai').value : null),
        this.aulaService.codigosAulasSelecionadas(),
        this.planoService.permitirRenovacao,
        this.negociacaoService.categoriaPlano  ? this.negociacaoService.categoriaPlano : null,
        this.negociacaoService.origemCobranca,
        this.negociacaoService.cobrancaAntecipada,
        this.negociacaoService.responsavel,
        this.negociacaoService.token,
        this.planoService.vezesEscolhidasParcelarTaxaMatricula,
        (this.formGroup.get('rg') && this.formGroup.get('rg').value.includes('*') ? this.planoService.dadosCompletosDesmascarados.rg : (this.formGroup.get('rg') ? this.formGroup.get('rg').value : null)),
        this.negociacaoService.codigoEvento,
        this.negociacaoService.usuarioResponsavel,
        this.negociacaoService.codigoRegistroAcessoPagina,
        "",
        this.formGroup.get("tipoCredito") ? this.formGroup.get("tipoCredito").value : null,
        this.negociacaoService.povoarJsonRespostaParq(this.isExibirCampo("ParQ")),
        this.formGroup.get("cnpj") ? this.formGroup.get("cnpj").value : null,
        this.formGroup.get("nomeResponsavelEmpresa") ? this.formGroup.get("nomeResponsavelEmpresa").value : null,
        this.formGroup.get("cpfResponsavelEmpresa") ? this.formGroup.get("cpfResponsavelEmpresa").value : null,
        this.negociacaoService.lancarContratoConcomitanteAoInvesDeRenovar,
        this.negociacaoService.pactoPayComunicacao,
        this.negociacaoService.utm_data,
        this.planoService.getExibirCamposCompartilhamentoPlanoValue() ? this.getListaClientesDependentes() : [],
        this.modalidadeService.modalidadesSelecionadas,
        this.turmaService.horariosSelecionados, null, false,
        OrigemSistemaEnum.VENDAS_ONLINE,
        this.formGroup.get("dataUtilizacao") ? this.formGroup.get("dataUtilizacao").value : null,
        permiteInformarDataUtilizacao,
        this.locacaoAmbienteService.locacoesSelecionadas && this.locacaoAmbienteService.locacoesSelecionadas.length > 0 ? true : false,
        this.locacaoAmbienteService.locacoesSelecionadas
      );
    } else {
       return this.getVendaJson();
    }
  }

  getNumeroCartao(): string {
    return this.formGroup.get("nrcartao").value
      ? this.formGroup.get("nrcartao").value.replace(/ /g, "")
      : "";
  }

  finalizarVenda(): void {
    if (
      !this.empresaService.unidadeSelecionada ||
      !this.empresaService.unidadeSelecionada.codigo
    ) {
      this.loadUnidade();
      Swal.fire({
        type: "warning",
        text: this.translateService.instant(
          "checkout.poderia-tentar-novamente-por-favor",
        ),
        showConfirmButton: true,
      });
      return;
    }

    if (
      this.getConfig().permitirMudarTipoParcelamento &&
      this.getPlanoSelecionado() != null &&
      this.getPlanoSelecionado().parcelamentoOperadora
    ) {
      if (
        this.formGroup.get("tipoCredito") != null &&
        this.formGroup.get("tipoCredito").value === "Recorrente"
      ) {
        this.formGroup.get("parcelasCartao").setValue(null);
      }
    }

    // validar
    const validado = this.negociacaoService.validar(
      this.getVendaJsonDecrypt(),
      this.empresaService.config,
    );
    if (validado) {
      Swal.fire({
        type: "warning",
        text: validado,
        showConfirmButton: true,
      });
      return;
    }
    Swal.fire({
      title: this.translateService.instant(
        "checkout.processando-seu-pagamento",
      ),
      onOpen: function () {
        Swal.showLoading();
      },
    });
    this.recaptchaV3Service.execute("importantAction").subscribe((token) => {
      this.processando = true;
      const dto = this.getVendaJsonDecrypt();
      // Link da loja ou de produtos ou de planos (Checkout)...
      dto.ipPublico = this.ipPublico;
      this.negociacaoService.gravarVenda(dto, token).subscribe((result) => {
        if (result["return"] === "APROVADA") {
          if (
            window.localStorage
              .getItem("chave")
              .indexOf("e0aa2e5c7ed462647540be7a58465a26") !== -1
          ) {
            this.crmService
              .enviarMensagem(dto.cpf, dto.email)
              .pipe(
                switchMap((resultMensagem) => {
                  console.log("Mensagem enviada com sucesso", resultMensagem);
                  this.concluirAposAprovado();
                  return of(resultMensagem);
                }),
              )
              .subscribe();
          } else {
            this.concluirAposAprovado();
          }
        } else {
          this.infoPagamento.naoAprovada(result["return"]);
          this.processando = false;
        }
      });
    });
  }

  concluirAposAprovado(): void {
    window.localStorage.setItem("msgFinalOperacao", "3");
    this.concluir(
      false,
      this.translateService.instant("pospagamento.venda-realizada-com-sucesso"),
    );
  }

  cobrarParcela(): void {
    const title =
      this.alunoSelecionado().valorCobrar === 0.0
        ? this.translateService.instant("checkout.adicionando-seu-cartao")
        : this.translateService.instant("checkout.processando-seu-pagamento");
    Swal.fire({
      title: title,
      allowOutsideClick: false,
      onOpen: function () {
        Swal.showLoading();
      },
    });

    if (
      this.getConfig().permitirMudarTipoParcelamento &&
      this.getPlanoSelecionado() != null &&
      this.getPlanoSelecionado().parcelamentoOperadora
    ) {
      if (
        this.formGroup.get("tipoCredito") != null &&
        this.formGroup.get("tipoCredito").value === "Recorrente"
      ) {
        this.formGroup.get("parcelasCartao").setValue(null);
      }
    }

    this.negociacaoService
      .cobrarParcela(this.infoPagamento.getCobrancaJson())
      .subscribe((result) => {
        let emailEnviado = false;
        if (result["dados"]) {
          const json = JSON.parse(result["dados"]);
          emailEnviado = json["emailEnviado"];
        }

        if (result["return"] === "APROVADA") {
          if (this.negociacaoService.cobrarParcelasEmAberto) {
            // Link de pagamento
            window.localStorage.setItem("msgFinalOperacao", "1");
            this.concluir(
              emailEnviado,
              this.translateService.instant(
                "pospagamento.pagamento-efetuado-com-sucesso",
              ),
            );
          } else {
            // Link de cadastro do cartão
            window.localStorage.setItem("msgFinalOperacao", "2");
            this.concluirCadastroCartao();
          }
        } else if (result["motivo"] != null) {
          this.infoPagamento.naoAprovada(result["motivo"]);
        } else {
          this.infoPagamento.naoAprovada(result["return"]);
        }
      });
  }

  concluir(emailEnviado: boolean, title: String): void {
    this.analitycsPixel.triggerCustomEventFacebookPurchase({
      value: this.negociacaoService.valorFinalContrato,
      currency: 'BRL',
      formaPagamento: 'Cartão Crédito',
    });
    if (!this.negociacaoService.cobrarParcelasEmAberto) { // não é link de pagamento envia o evento de compra
      this.facebookPixelInsertEventCartaoCred();
    }
    const text = emailEnviado
      ? this.translateService.instant(
          "checkout.em-breve-voce-recebera-um-email",
        )
      : this.translateService.instant(
          "checkout.deu-tudo-certo-com-o-seu-pagamento",
        );
    Swal.fire({
      type: "success",
      title: title,
      text: text,
      showConfirmButton: true,
      onClose: () => {
        this.infoPagamento.redirectPosVenda();
      },
    });
  }

  facebookPixelInsertEventCartaoCred() {
    const params = {
      value: this.negociacaoService.valorFinalContrato,
      currency: "BRL",
      formaPagamento: "Cartão Crédito",
    };
    this.analitycsPixel.triggerEventFacebookPurchase(params);

    // Api de conversão meta
    if (this.tokenApiConversao) {
      const dto = this.getVendaJsonDecrypt();

      const nome = this.analitycsApiConversao.stringToSha256(
        dto.nome.split(" ")[0],
      );
      const sobrenome = this.analitycsApiConversao.stringToSha256(
        dto.nome.split(" ")[1],
      );
      const email = this.analitycsApiConversao.stringToSha256(dto.email);
      const telefone = this.analitycsApiConversao.stringToSha256(dto.telefone);
      const sexo = this.analitycsApiConversao.stringToSha256(dto.sexo);
      const dataNascimento = this.analitycsApiConversao.stringToSha256(
        dto.dataNascimento,
      );
      const cidade = this.analitycsApiConversao.stringToSha256(
        this.empresaService.unidadeSelecionada.cidade,
      );
      const estado = this.analitycsApiConversao.stringToSha256(
        this.empresaService.unidadeSelecionada.estado,
      );
      const cep = this.analitycsApiConversao.stringToSha256(dto.cep);

      let valorTotalGeral = 0;
      const contents = [];
      if (dto.produtos && dto.produtos.length > 0) {
        dto.produtos.forEach((p) => {
          contents.push({
            id: "PRODUTO-" + p.produto,
            quantity: p.qtd,
            item_price: p.getValorTotal(),
          });
          valorTotalGeral = valorTotalGeral + p.getValorTotal();
        });
      }
      if (dto.plano) {
        contents.push({
          id: "PLANO-" + dto.plano,
          quantity: 1,
          item_price: this.negociacaoService.valorFinalContrato,
        });
        valorTotalGeral =
          valorTotalGeral + this.negociacaoService.valorFinalContrato;
      }

      const params2 = {
        data: [
          {
            event_name: "Purchase",
            event_time: Math.round(+new Date() / 1000),
            action_source: "website",
            user_data: {
              fn: nome,
              ln: sobrenome,
              em: email,
              ph: telefone,
              ge: sexo,
              db: dataNascimento,
              ct: cidade,
              st: estado,
              zp: cep,
            },
            custom_data: {
              content_name:
                "EMPRESA: " + this.empresaService.unidadeSelecionada.nome,
              currency: "BRL",
              contents: contents,
              value: valorTotalGeral,
              content_category: "Cartão de Crédito",
            },
          },
        ],
      };
      this.analitycsApiConversao
        .enviarEvento(this.pixelId, this.tokenApiConversao, params2)
        .subscribe((ret) => {
          console.log(
            "Api Conversão Meta: evento de venda disparado => " +
              JSON.stringify(ret),
          );
        });
    }
  }

  concluirCadastroCartao(): void {
    Swal.fire({
      type: "success",
      title: this.translateService.instant(
        "pospagamento.cartao-cadastrado-com-sucesso",
      ),
      showConfirmButton: true,
      onClose: () => {
        this.infoPagamento.redirectPosVenda();
      },
    });
  }

  loadUnidade(): void {
    try {
      this.tryLoad();
    } catch (e) {
      console.log("Erro ao tentar obter a empresa, vou tentar novamente");
      try {
        this.tryLoad();
      } catch (ex) {
        console.log("O erro persistiu");
        console.log(this.negociacaoService.chave);
        console.log(this.negociacaoService.codunidade);
        console.log(e);
      }
    }
  }

  private tryLoad() {
    if (!this.empresaService.unidadeSelecionada) {
      console.log("Try to load company");
      this.empresaService
        .obterEmpresa(
          this.negociacaoService.chave,
          this.negociacaoService.codunidade,
        )
        .subscribe((data) => (this.empresaService.unidadeSelecionada = data));
    }
    this.empresaService
      .obterConfigs(
        this.negociacaoService.chave,
        this.negociacaoService.codunidade,
      )
      .subscribe((data) => {
        this.empresaService.config = data;

        if (
          this.empresaService.config.permitirMudarTipoParcelamento === false &&
          this.planoService.planoSelecionado.parcelamentoOperadora
        ) {
          this.formGroup.addControl(
            "tipoCredito",
            new FormControl("Parcelado"),
          );
        }
      });

    this.infoPagamento.produtoService = this.produtoService;
    this.infoPagamento.planoService = this.planoService;
    this.infoPagamento.empresaService = this.empresaService;
    this.infoPagamento.negociacaoService = this.negociacaoService;
    this.infoPagamento.formGroup = this.formGroup;
    this.infoPagamento.alunoService = this.alunoService;
  }

  getPlanoSelecionado(): Plano {
    return this.planoService.planoSelecionado;
  }

  getProdutosSelecionados(): Array<VendaProduto> {
    return this.produtoService.produtosSelecionados;
  }

  getTodosProdutos(): Array<Produto> {
    return this.produtoService.getTodosProdutos(
      this.negociacaoService.chave,
      this.negociacaoService.codunidade,
    );
  }

  opcoesParcelamentoProduto(): number[] {
    let maxQtd = 1;

    //Lógica tela de Vendas
    for (const vendaProduto of this.getProdutosSelecionados()) {
      for (const produto of this.getTodosProdutos()) {
        if (
          produto.codigo === vendaProduto.produto &&
          produto.maxDivisao > maxQtd
        ) {
          maxQtd = produto.maxDivisao;
        }
      }
    }

    //Lógica para Link de Pagamento
    const vendaProdutos = this.getProdutosSelecionados();
    if (maxQtd == 1 && vendaProdutos.length == 0 && this.dadosRecebidosLinkPagamento != undefined && this.dadosRecebidosLinkPagamento > 1) {
      maxQtd = this.dadosRecebidosLinkPagamento;
    }

    return this.opcoesParcelamento(maxQtd);
  }

  opcoesParcelamento(limite): number[] {
    const opcoes = [];
    let i = 1;

    while (i <= limite) {
      opcoes.push(i);
      i++;
    }
    return opcoes;
  }

  opcoesTipoCredito(): number[] {
    const opcoes = [];
    opcoes.push("Parcelado");
    opcoes.push('Recorrente');
    // plano parcelado pela operadora deve exibir sempre somente o Parcelado e não exibir Recorrente;
    // if (!this.planoService.planoSelecionado.parcelamentoOperadora) {
    //   opcoes.push("Recorrente");
    // }
    return opcoes;
  }

  getMaskCVV() {
    if (this.formGroup.get("nrcartao").value != null) {
      return sessionStorage.getItem("BAND") &&
        sessionStorage.getItem("BAND").includes("AMERICAN-EXPRESS")
        ? [/\d/, /\d/, /\d/, /\d/]
        : [/\d/, /\d/, /\d/];
    } else {
      return [/\d/, /\d/, /\d/];
    }
  }

  getWidthNavegador(): boolean {
    return window.outerWidth >= 768;
  }

  get Validators() {
    return Validators;
  }

  getDiaHoje(): number {
    return new Date().getDate();
  }

  opcoesVencimentoFatura(limite): number[] {
    const opcoes = [];
    let i = 1;

    while (i <= limite) {
      opcoes.push(i);
      i++;
    }
    return opcoes;
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  alunoSelecionado(): Aluno {
    return this.infoPagamento.alunoSelecionado();
  }

  negociacao(): NegociacaoService {
    return this.negociacaoService;
  }

  isMostrarAceiteLinkPagamento(): boolean {
    if (this.empresaService.config != null) {
      return this.empresaService.config.apresentarTermoAceiteLinkPag;
    }
  }

  isMostrandoContrato(): boolean {
    return localStorage.getItem("contratoAberto") === "true";
  }

  getTermos() {
    if (this.aceitaTermo === true) {
      this.aceitaTermo = false;
    } else if (this.aceitaTermo === false) {
      this.aceitaTermo = true;
    }
  }

  getIPAddress() {
    this.http
      .get<any>("https://api.ipify.org/?format=json")
      .subscribe((response) => {
        console.log(response.ip);
        this.ipPublico = response.ip;
      });
  }

  isExibirCampo(campo: string): boolean {
    if (
      this.planoService.planoSelecionado &&
      this.planoService.planoSelecionado.codigo > 0
    ) {
      if (this.getConfig().camposAdicionais.length != 0) {
        return (
          this.getConfig() &&
          this.getConfig().camposAdicionais.indexOf(campo) > -1
        );
      } else if (this.getConfig().camposAdicionaisProdutoPlano.length != 0) {
        return (
          this.getConfig() &&
          this.getConfig().camposAdicionaisProdutoPlano.indexOf(campo) > -1
        );
      }
    } else if (campo === "ParQ") {
      return false;
    }
    // Cadastro com venda de produto
    return (
      this.getConfig() &&
      this.getConfig().camposAdicionaisProduto.indexOf(campo) > -1
    );
  }

  public get apresentarBotaoPrimeiraCobrancaPixEGuardarCartao(): boolean {
    let apresentarBotao = false;
    let planoSelecionaEstaPreenchido = this.getPlanoSelecionado() !== undefined && this.getPlanoSelecionado() !== null;
    let configPrimeiroCobrancaPixEGuardarCartao = this.empresaService.config.primeiraCobrancaPixEGuardarCartao;
    if (configPrimeiroCobrancaPixEGuardarCartao) {
      apresentarBotao = configPrimeiroCobrancaPixEGuardarCartao;
    }
    return apresentarBotao;
  }

  validarCartaoECobrarPrimeiraPix(): void {
    // validar
    const validado = this.negociacaoService.validar(
      this.getVendaJsonDecrypt(),
      this.empresaService.config,
    );
    if (validado) {
      Swal.fire({
        type: "warning",
        text: validado,
        showConfirmButton: true,
      });
      return;
    } else {
      this.aprovouCadastroCartaoPrimeiroPagamentoPix.emit("true");
    }
  }

  existeProdutoDiariaSelecionado(): boolean {
    let retorno = false;

    if(this.produtoService && this.produtoService.produtos && this.produtoService.produtosSelecionados) {

      for(const produtoSelecionado of this.produtoService.produtosSelecionados) {
        for(const produto of this.produtoService.produtos) {

          if(produto.codigo === produtoSelecionado.produto) {
            if(produto.tipo === 'DI') {
              retorno = true;
              break;
            }
          }

        }
        if(retorno) {
          break;
        }
      }
    }

    return retorno;
  }

  exibirSelectDividirEmQuantasVezesParcelamentoTelaAluno(): boolean {
    // Lógica atual tela de pagamento venda de plano
    const validaCondicoesAtuais = !this.getPlanoSelecionado() && this.getProdutosSelecionados().length > 0 && this.opcoesParcelamentoProduto().length > 1;
    if (validaCondicoesAtuais) {
      return true;
    }
    // Se não retornar acima, valida se é Link de Pagamento
    const validaComBaseNosDados = this.dadosRecebidosLinkPagamento;
    if (validaComBaseNosDados != undefined && validaComBaseNosDados > 1) {
      return true;
    }
    // Se não entrar em nehuma das lógicas acima, retorna false para não exibir o select
    return false;
  }

  exibirSelectDividirEmQuantasVezesLinkAntigoPlanoParcelado(): boolean {
    const validaCondicoesAtuais = this.formGroup.get('tipoCredito') != null && this.formGroup.get('tipoCredito').value == 'Parcelado';
    if (validaCondicoesAtuais && !this.exibirSelectDividirEmQuantasVezesParcelamentoTelaAluno()) {
      return true;
    }
    return false;
  }

}
