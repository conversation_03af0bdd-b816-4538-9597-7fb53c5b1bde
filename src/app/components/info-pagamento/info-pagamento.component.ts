import {Component, OnInit} from '@angular/core';
import {AlunoService} from '@base-core/aluno/aluno.service';
import {Aluno} from '@base-core/aluno/aluno.model';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {Parcela} from '@base-core/negociacao/parcela.model';

@Component({
  selector: 'pacto-info-pagamento',
  templateUrl: './info-pagamento.component.html',
  styleUrls: ['./info-pagamento.component.scss']
})
export class InfoPagamentoComponent implements OnInit {

  constructor(private alunoService: AlunoService,
              private empresaService: EmpresaService) {
  }

  ngOnInit() {
  }

  getMoeda(): string {
    return this.empresaService.unidadeSelecionada.moeda;
  }

  alunoSelecionado(): Aluno {
    return this.alunoService.alunoSelecionado ? this.alunoService.alunoSelecionado : new Aluno(null, '', '', '',
      '', 0.0, '', false, 1, 0.0, [], '',
      '', '', '');
  }

  valorCobrar(): number {
    return this.alunoSelecionado().valorCobrar - this.alunoSelecionado().desconto;
  }

  multaJurosParcela(parcela: Parcela): boolean {
    if (parcela !== undefined && parcela !== null) {
      if (parcela.multa > 0 || parcela.juros > 0) {
        return true;
     }
    }
    return false;
  }
}
