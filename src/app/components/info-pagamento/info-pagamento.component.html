<div class="caixa-info-pagamento">
  <div class="row">
    <div class="column col-12" *ngIf="alunoSelecionado().valorCobrar > 0.0">
      <span class="selecionou">Será cobrado agora:</span>
    </div>

    <div class="column col-12" *ngIf="alunoSelecionado().valorCobrar == 0.0">
      <span class="selecionou"><strong>O cartão será cadastrado sem efetuar cobrança no momento, pois não foi identificada nenhuma parcela pendente apta para pagamento aqui. </strong></span>
      <div> </div>
      <span class="selecionou">Caso tenha alguma parcela em aberto fale com a academia pois a parcela já está em remessa para cobrança ou já possui boleto gerado e por isso não será cobrada aqui para evitar duplicidade.</span>
    </div>
  </div>

  <div class="dados row " *ngIf="alunoSelecionado().valorCobrar > 0.0 && alunoSelecionado().parcelasCobrar.length == 0">
    <div class="column col-6">
      <span class="selecionou text-normal">{{alunoSelecionado().referente}}</span>
    </div>
    <div class="column col-6 dados-right text-normal">
      <span class="rs">{{this.getMoeda()}}</span>
      <span class="valor valor-normal">{{this.alunoSelecionado().valorCobrar | currency:false:''}}</span>
    </div>

    <div class="column col-6">
      <span class="selecionou text-normal">Desconto</span>
    </div>
    <div class="column col-6 dados-right text-normal">
      <span class="rs">{{this.getMoeda()}}</span>
      <span class="valor valor-normal">{{this.alunoSelecionado().desconto | currency:false:''}}</span>
    </div>

    <div class="column col-12 dados-linha"></div>

    <div class="column col-6">
      <span class="selecionou">Total</span>
    </div>
    <div class="column col-6 dados-right">
      <span class="rs">{{this.getMoeda()}}</span>
      <span class="valor">{{this.valorCobrar() | currency:false:''}}</span>
    </div>
  </div>

  <div class="dados row " *ngIf="alunoSelecionado().valorCobrar > 0.0 && alunoSelecionado().parcelasCobrar.length > 0">

    <div class="row column col-12" style="width: 100%; margin-right: 0px; padding-right: 0px;" *ngFor="let parcela of alunoSelecionado().parcelasCobrar">
      <div class="column col-6">
        <span class="selecionou text-normal">{{parcela.descricao}}</span>
      </div>
      <div class="column col-6 dados-right text-normal" style="width: 100%; margin-right: 0px; padding-right: 0px;">
        <span class="rs">{{this.getMoeda()}}</span>
        <span class="valor valor-normal">{{parcela.valor | currency:false:''}}</span>
      </div>
      <div *ngIf="multaJurosParcela(parcela)" class="column col-6">
        <span class="selecionou">Multa/Juros</span>
      </div>
      <div *ngIf="multaJurosParcela(parcela)" class="column col-6 dados-right text-normal" style="width: 100%; margin-right: 0px; padding-right: 0px;">
        <span class="rs">{{this.getMoeda()}}</span>
        <span class="valor valor-normal">{{parcela.multa + parcela.juros | currency:false:''}}</span>
      </div>
      <div class="column col-12 dados-linha"></div>
    </div>
    <div class="column col-6">
      <span class="selecionou">Desconto</span>
    </div>
    <div class="column col-6 dados-right text-normal">
      <span class="rs">{{this.getMoeda()}}</span>
      <span class="valor valor-normal">{{this.alunoSelecionado().desconto | currency:false:''}}</span>
    </div>

    <div class="column col-12 dados-linha"></div>

    <div class="column col-6">
      <span class="selecionou">TOTAL</span>
    </div>
    <div class="column col-6 dados-right">
      <span class="rs">{{this.getMoeda()}}</span>
      <span class="valor">{{this.valorCobrar() | currency:false:''}}</span>
    </div>
  </div>

  <div class="row" *ngIf="alunoSelecionado().valorCobrar > 0.0">
    <div class="column col-12">
<!--      <span class="selecionou">Descrição das parcelas: {{alunoSelecionado().referente}}</span>-->
    </div>
  </div>
</div>



