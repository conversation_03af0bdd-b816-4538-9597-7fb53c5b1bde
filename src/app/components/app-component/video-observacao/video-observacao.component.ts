import {AfterViewInit, Component, Input, OnDestroy, OnInit} from '@angular/core';
import {DomSanitizer, SafeResourceUrl} from '@angular/platform-browser';

@Component({
  selector: 'pacto-video-observacao',
  templateUrl: './video-observacao.component.html',
  styleUrls: ['./video-observacao.component.scss'],
})
export class VideoObservacaoComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() videoYouTubeCode: string;
  @Input() observacao: string;
  isModalOpen = false;
  sanitizedUrl: SafeResourceUrl;
  youTubeUrl = '';

  constructor(private readonly sanitizer: DomSanitizer) {}

  public ngOnInit(): void {
    this.youTubeUrl = 'https://www.youtube.com/embed/' + this.videoYouTubeCode + '?enablejsapi=1&autoplay=1';
    this.sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.youTubeUrl);
  }

  ngAfterViewInit(): void {
    if (!window['YT']) {
      const tag = document.createElement('script');
      tag.src = 'https://www.youtube.com/iframe_api';
      const firstScriptTag = document.getElementsByTagName('script')[0];
      firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
    }

    window['onYouTubeIframeAPIReady'] = () => {
      this.initYouTubePlayer();
    };

    if (window['YT'] && window['YT'].Player) {
      this.initYouTubePlayer();
    }
  }

  ngOnDestroy(): void {
    if (window['YT'] && window['YT'].Player) {
      const iframe = document.getElementById('youtube-iframe') as HTMLIFrameElement;
      if (iframe && iframe.contentWindow) {
        iframe.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}', '*');
      }
    }
  }

  initYouTubePlayer(): void {
    new window['YT'].Player('youtube-iframe', {
      events: {
        'onReady': (event: any) => this.onPlayerReady(event),
        'onStateChange': (event: any) => this.onPlayerStateChange(event)
      }
    });
  }

  onPlayerReady(event: any): void {
    event.target.playVideo();
  }

  onPlayerStateChange(event: any): void {
    // Lógica adicional para quando o estado do player mudar, se necessário
  }

  openModal(): void {
    const iframe = document.getElementById('youtube-iframe') as HTMLIFrameElement;
    if (iframe && iframe.contentWindow) {
      iframe.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}', '*');
    }
    // this.sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl('');
    this.isModalOpen = true;
  }

  closeModal(): void {
    // this.sanitizedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.videoUrl);
    this.isModalOpen = false;
  }

  getPossuiVideo(): boolean {
    return (this.videoYouTubeCode && this.videoYouTubeCode !== '');
  }

  getPossuiObs(): boolean {
    return (this.observacao && this.observacao !== '');
  }
}
