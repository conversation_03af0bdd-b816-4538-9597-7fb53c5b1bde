@media only screen and (max-width: 748px) {
  .point-event {
    pointer-events: visible !important;
  }
  .renderize-mobile{
    width: 295px !important;
    height: 195px !important;
  }
  .video-modal {
    display: none !important;
  }
  .modal-content {
    padding-top: 15px !important;
    padding-left: 0px !important;
    padding-right: 0px !important;
    padding-bottom: 0px !important;
  }
}
.point-event {
  pointer-events: none;
}
.video-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.8);
}

.modal-content {
  position: relative;
  text-align: center;
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  .open {
    max-width: 70%;
  }
}

.close {
  position: absolute;
  top: 10px;
  right: 20px;
  color: #aaa;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}
