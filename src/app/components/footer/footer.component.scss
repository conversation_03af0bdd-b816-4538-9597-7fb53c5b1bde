@import "../../../assets/css/variaveis";

.row {
  padding: 0;
  border: 0;
  margin: 0;
}

.col-md-2 {
  padding: 0;
  border: 0;
  margin: 0;
}

.col-md-8 {
  padding: 0;
  border: 0;
  margin: 0;
}

.col-md-12 {
  padding: 0;
  border: 0;
  margin: 0;
}

.container-footer {
  color: $textoclaro;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: $branco;
  box-shadow: 0 -4px 10px 0 $pale-grey;
  padding: 0;
}

.footer {
  display: flex;
  height: $alturafooter;
}

[class^="span_"] {
  min-height: $alturafooter;
}

.heart {
  color: $coracao;
  font-size: 16px;
}


.btn-continuar {
  width: 100%;
  line-height: 20px;
  padding: 5px;
  font-weight: bold;
  border-radius: 4px;
  color: $branco;
  cursor: pointer;
  text-transform: uppercase;
  margin-top: 30px;
  border: none;
}

.btn-continuar:disabled {
  background-color: #cccccc;
  /* Cor de fundo quando o botão está desabilitado */
  cursor: not-allowed;
  /* Muda o cursor para indicar que o botão está inativo */
  color: #666666;
  /* Mudar a cor do texto se necessário */
}

.btn-limpar {
  width: 100%;
  line-height: 20px;
  padding: 5px;
  font-weight: bold;
  border-radius: 4px;
  color: $branco;
  background-color: $textoclaro;
  border-color: $textoclaro;
  cursor: pointer;
  text-transform: uppercase;
  margin-top: 30px;
  border: none;
}

.selecionar {
  cursor: pointer;
  margin-top: 30px;
  color: #ffffff;
  height: 30px;
  border-radius: 4px;
  box-shadow: 0 2px 3px 0 $bordacinza;
  background-color: $azulclaro;
  display: inline-flex;

  .btn {
    margin-left: 18px;
    text-align: center;
    font-size: 12px;
    width: 100px;
    line-height: 30px;
    vertical-align: middle;
  }

  .detalhes {
    line-height: 30px;
    width: 30px;
    vertical-align: middle;
    text-align: center;
  }
}

.selecionar-alterar {
  background-color: #c7ccd3;
}

.vcselec {
  color: $textoescuro;
  margin-top: 20px;
  display: block;
  font-size: 14px;
}

.info-plano {
  text-align: left;
  margin-top: 10px;
  color: $textoclaro;
  font-size: 14px;
}

.pedido {
  font-size: 18px;
  font-weight: normal;
  color: $textoescuro;
  margin-top: 40px;
}

.container-voltar a {
  border: 1px solid $azulclaro;
  color: $azulclaro;
  margin-top: 50px;
  padding: 5px 15px;
  font-size: 12px;
  display: inline-block;
  text-decoration: none;
  border-radius: 50px;
}

.barraLateralFooter {
  border-left: 8px solid $azulclaro;
}

.arrow-right {
  width: 0;
  height: 0;
  border-top: 15px solid transparent;
  border-bottom: 15px solid transparent;
  border-left: 15px solid $azulclaro;
  margin-top: 39px;
}

.primeiraparcela {
  font-weight: bold;
  color: $azulclaro;
}

.btn-remover-produto {
  cursor: pointer;
  padding-left: 5px;
  color: $textoescuro;
}

.tableProdutos {
  text-align: left;
  width: 100%;
}

.gridBtn {
  display: grid;
  grid-template-columns: 1fr;
}

.produtos {
  width: 100%;
}

.paddinLeft10 {
  padding-left: 10px;
}

.width190 {
  max-width: 190px;
}

.tableFixHead {
  overflow-y: auto;
  height: 105px;
  margin-top: 16px;
}

.tableFixHead thead th {
  position: sticky;
  top: 0;
}

/* Just common table stuff. Really. */
table {
  border-collapse: collapse;
  width: 100%;
}

th,
td {
  //padding: 8px 16px;
}

th {
  background: $branco;
}

.footerPlano {
  padding-left: 10px;
  overflow: auto;
  max-height: 100%;
  max-width: 100%;
}

.btnAlterarQtd {
  cursor: pointer;
  color: $textoescuro;
}

.spanProduto {
  font-size: 14px;
}

.spanHeader {
  font-weight: normal;
  color: $textoescuro;
  font-size: 14px;
}

.footer-desktop {
  display: block;
}

.footer-mobile {
  display: none;
}

@media only screen and (max-width: 748px) {
  .footer-mobile {
    display: block;
  }

  .footer-desktop {
    display: none;
  }
}

.divSuperiorMobile {
  padding: 5px 5px 10px 5px;
}

.divTopoCarrinho {
  padding: 5px 0 5px 0;
  display: flex;
  align-items: center;
  font-size: 25px;
  color: $azulclaro;
}

.divInferiorMobile {
  background: $azulclaro;
  display: flex;
}

.divPlanoMobile {
  display: flex;
  align-items: center;
}

.divProdutoMobile {}

.divFooterMobile {
  border-left: 3px solid $azulclaro;
  display: grid;
}

.descricaoProdutoTable {
  width: 40%;
}

.infoPlanoMobile {
  text-align: left;
  margin-top: 5px;
  color: $textoclaro;
  font-size: 14px;
}

.divItens {
  display: grid;
  align-items: center;
  justify-items: center;
  cursor: pointer;
  width: 100%;
  grid-template-columns: 0.5fr 1.5fr;
}

.carrinho {
  font-size: 25px;
  position: relative;
  top: 15px;
  color: $branco;
}

.divQtdItens {
  background: $branco;
  border-radius: 100%;
  display: grid;
  align-items: center;
  justify-items: center;
  cursor: pointer;
  width: 22px;
  height: 22px;
  position: relative;
  top: -20px;
  right: -25px;
}

.btn-qtd-itens {
  font-weight: bold;
  color: $branco;
  text-transform: uppercase;
}

.btn-topo-carrinho {
  color: $azulclaro;
  width: 100%;
  border: none;
  font-size: 18px;
  background: $branco;
}

.divBtnComprar {
  width: 100%;
  text-align: center;
  padding-top: 20px;
}


.btn-continuar-mobile {
  color: $branco;
  cursor: pointer;
  border: none;
  width: 90%;
  height: 40px;
  border-radius: 5px;
  font-size: 18px;
}

.divPlanoSelecionadoMobile {
  width: 90%;
}

.divPlanoRemoverSelecionadoMobile {
  width: 10%;
  color: $textoescuro;
  font-size: 14px;
}

.vcselecMobile {
  color: $textoescuro;
  display: block;
  font-size: 14px;
}

.tableProdutosMobile {
  text-align: left;
}

.tableFixHeadMobile {
  overflow-y: auto;
  max-height: 200px;
}

.btn-vercarrinho {
  color: $branco;
  padding-right: 40px;
}


.justify-collunn {
  display: flex;
  flex-direction: column;
}

.footerTurma {
  margin-bottom: 10px;
}

.titleFooter {
  font-family: Europa;
  font-size: 14px;
  text-align: left;
  color: #2c343b;
  margin-bottom: 10px;
}

span {
  font-family: Europa;
  font-size: 14px;
  font-weight: 400;
  line-height: 14.4px;
  text-align: left;
  color: #bdc3c7;
}

.bg-blue {
  color: #1998FC !important;
}
