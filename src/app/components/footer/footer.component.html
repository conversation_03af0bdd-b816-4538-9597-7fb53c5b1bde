<footer *ngIf="!currentUrl.startsWith('/checkout')" class="container-footer">

  <!--normal-->
  <div class="footer-desktop">
    <div class="container footer" [style.border-left-color]="getConfig()?.cor">

      <div class="barraLateralFooter" [style.border-left-color]="getConfig()?.cor">
      </div>

      <div class="arrow-right" *ngIf="getPlanoSelecionado()" [style.border-left-color]="getConfig()?.cor">
      </div>

      <div class="ta-left footerPlano" *ngIf="getVendaPlano()" [ngClass]="getClassPLano()">
        <span class="vcselec">{{'rodape.plano'|translate}}</span>

        <div *ngIf="!getPlanoSelecionado()">
          <div class="info-plano">
            {{'rodape.nenhum-plano-selecionado'|translate}}
          </div>
        </div>

        <div *ngIf="getPlanoSelecionado()">
          <div class="info-plano">
            <div>{{getPlanoSelecionado().nome | truncate}}</div>
            <div *ngIf="getExibirValorPrimeiroMes()">
              {{this.getUnidadeSelecionada().moeda}} {{getPlanoSelecionado().primeiraParcela| currency:false:''}}
              {{'rodape.no-primeiro-mes'|translate}}
            </div>
            <div *ngIf="getExibirValorPorMes()">
              {{this.getUnidadeSelecionada().moeda}} {{getPlanoSelecionado().mensalidade| currency:false:''}}
              {{'rodape.por-mes'|translate}}
            </div>
            <div *ngIf="getPlanoSelecionado().vendaComTurma && getApresentarValorDoPlano() && modalidadesSelecionadas != null && modalidadesSelecionadas.length > 0 && getPlanoSelecionado().valorTotalPlanoTurma > 0">
              {{this.getUnidadeSelecionada().moeda}} {{getPlanoSelecionado().valorTotalPlanoTurma| currency:false:''}}
              {{'rodape.total'|translate}}
            </div>
          </div>
          <div *ngIf="vendaComTurma && getVendaProduto() && modalidadesSelecionadas.length > 0" class="d-flex justify-collunn footerTurma">

            <div *ngIf="vendaComTurma">
              <p class="titleFooter vcselec"> Modalidade</p>
              <div *ngFor="let modalidade of modalidadesSelecionadas; let i = index">
                <span *ngIf="i < 3">{{ modalidade.modalidade | uppercase }}</span>
                <span *ngIf="i === 2 && modalidadesSelecionadas.length > 3" class="bg-blue" [matTooltip]="getTooltipContent()" matTooltipClass="tooltip-custom">
              (+{{ modalidadesSelecionadas.length - 3 }})
            </span>
              </div>
            </div>

            <div *ngIf="hasTurma">
              <p class="titleFooter vcselec "> Turma</p>
              <div *ngFor="let turma of daySchedules; let i = index">
            <span *ngIf="i < 3">
              {{ turma.modalidadeName | uppercase }}
            </span>
                <span class="bg-blue" [matTooltip]="getDaysTooltipContent(turma)" matTooltipClass="tooltip-custom">
              (+{{ turma.currentTurmaSessions.length }})
            </span>
              </div>
            </div>

          </div>
          <div>
          </div>
        </div>
      </div>

      <div class="produtos" *ngIf="getVendaProduto()"
        [ngClass]="{'col-md-10': (getVendaProduto() && !getVendaPlano()), 'col-md-8 paddinLeft10': getVendaPlano()}">
        <div style="text-align: left" *ngIf="getProdutosSelecionados()?.length == 0">
          <span class="vcselec">{{'rodape.produto-s'|translate}}</span>
          <div>
            <div class="info-plano">
              {{'rodape.nenhum-produto-selecionado'|translate}}
            </div>
          </div>
        </div>
        <div *ngIf="getProdutosSelecionados()?.length > 0" class="tableFixHead">
          <table class="tableProdutos">
            <thead>
              <tr>
                <th>
                  <span class="spanHeader">{{'rodape.produto'|translate}}</span>
                </th>
                <th>
                  <span class="spanHeader">{{'rodape.preco'|translate}}</span>
                </th>
                <th>
                  <span class="spanHeader">{{'rodape.qtd'|translate}}</span>
                </th>
                <th>
                  <span class="spanHeader">{{'rodape.total'|translate}}</span>
                </th>
                <th>
                  <span class="spanHeader">{{'rodape.excluir'|translate}}</span>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let vendaProduto of getProdutosSelecionados()">
                <td><span class="spanProduto">{{vendaProduto.descricao}}</span></td>
                <td><span class="spanProduto"> {{this.getUnidadeSelecionada().moeda}} {{vendaProduto.valorUnitario|
                    currency:false:''}}</span></td>
                <td>
                  <div class="spanProduto">
                    <i class="pct-minus-circle btnAlterarQtd" (click)="removerQtdProduto(vendaProduto)"></i>
                    <span>
                      {{vendaProduto.qtd}}
                    </span>
                    <i class="pct-plus-circle btnAlterarQtd" (click)="adicionarQtdProduto(vendaProduto)"></i>
                  </div>
                </td>
                <td><span class="spanProduto"> {{this.getUnidadeSelecionada().moeda}} {{vendaProduto.getValorTotal()|
                    currency:false:''}}</span></td>
                <td><span class="btn-remover-produto spanProduto" (click)="removerProduto(vendaProduto)"><i
                      class="pct-trash-2"></i></span></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div *ngIf="vendaComTurma && !getVendaProduto()" class="col-md-7 d-flex">

        <div *ngIf="vendaComTurma" class="col-md-auto justify-collunn">
          <p class="titleFooter vcselec"> Modalidade</p>
          <div *ngFor="let modalidade of modalidadesSelecionadas; let i = index">
            <span *ngIf="i < 3">{{ modalidade.modalidade | uppercase }}</span>
            <span *ngIf="i === 2 && modalidadesSelecionadas.length > 3" class="bg-blue" [matTooltip]="getTooltipContent()" matTooltipClass="tooltip-custom">
              (+{{ modalidadesSelecionadas.length - 3 }})
            </span>
          </div>
        </div>

        <div *ngIf="hasTurma" class="col-md-auto justify-collunn">
          <p class="titleFooter vcselec "> Turma</p>
          <div *ngFor="let turma of daySchedules; let i = index">
            <span *ngIf="i < 3">
              {{ turma.modalidadeName | uppercase }}
            </span>
            <span class="bg-blue" [matTooltip]="getDaysTooltipContent(turma)" matTooltipClass="tooltip-custom">
              (+{{ turma.currentTurmaSessions.length }})
            </span>
          </div>
        </div>
      </div>

      <div class="col-md-2">
        <button class="btn-continuar"
          *ngIf="(getPlanoSelecionado() || getProdutosSelecionados()?.length > 0) && getConfig()"
          [style.background-color]="getConfig()?.cor" (click)="continuar()" [disabled]="isDisabledButtonCont">
          {{'rodape.continuar'|translate|uppercase}} <i class="pct-arrow-right"></i>
        </button>

        <button class="btn-limpar"
          *ngIf="!isVisiblededButtonClear && (getPlanoSelecionado() || getProdutosSelecionados()?.length > 0)"
          (click)="limparCarrinho()">
          {{'rodape.limpar'|translate|uppercase}} <i class="pct-trash-2"></i>
        </button>
      </div>
    </div>
  </div>

  <!--footer mobile-->
  <div class="footer-mobile">
    <div class="divFooterMobile" [style.border-left-color]="getConfig()?.cor"
      *ngIf="getPlanoSelecionado() || getProdutosSelecionados()?.length > 0">

      <div class="divSuperiorMobile" *ngIf="carrinho">

        <div class="divTopoCarrinho" [style.color]="getConfig()?.cor" (click)="visualizarCarrinho()">
          <i class="pct-chevron-down"></i>
          <button class="btn-topo-carrinho" [style.color]="getConfig()?.cor">
            {{'rodape.carrinho'|translate}}
          </button>
        </div>

        <div class="divPlanoMobile" *ngIf="getVendaPlano() && getPlanoSelecionado()">
          <div class="divPlanoSelecionadoMobile">
            <span class="vcselecMobile">{{'rodape.plano'|translate}}</span>
            <div class="infoPlanoMobile">
              <div>{{getPlanoSelecionado().nome}}</div>
              <div *ngIf="getPlanoSelecionado().primeiraParcela != getPlanoSelecionado().mensalidade">
                {{this.getUnidadeSelecionada().moeda}} {{getPlanoSelecionado().primeiraParcela| currency:false:''}}
                {{'rodape.no-primeiro-mes'|translate}}
              </div>
              <div *ngIf="getPlanoSelecionado().primeiraParcela == getPlanoSelecionado().mensalidade">
                {{this.getUnidadeSelecionada().moeda}} {{getPlanoSelecionado().mensalidade| currency:false:''}}
                {{'rodape.por-mes'|translate}}
              </div>
            </div>
          </div>
          <div class="divPlanoRemoverSelecionadoMobile" (click)="limparPlano()">
            <i class="pct-trash-2"></i>
          </div>
        </div>

        <div class="divProdutoMobile" *ngIf="getVendaProduto() && getProdutosSelecionados()?.length > 0">
          <div class="tableFixHeadMobile">
            <table class="tableProdutosMobile">
              <thead>
                <tr>
                  <th>
                    <span class="spanHeader">{{'rodape.produto'|translate}}</span>
                  </th>
                  <th>
                    <span class="spanHeader">{{'rodape.preco'|translate}}</span>
                  </th>
                  <th>
                    <span class="spanHeader">{{'rodape.qtd'|translate}}</span>
                  </th>
                  <th>
                    <span class="spanHeader">{{'rodape.total'|translate}}</span>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let vendaProduto of getProdutosSelecionados()">
                  <td class="descricaoProdutoTable">
                    <span class="spanProduto">{{vendaProduto.descricao}}</span>
                  </td>
                  <td>
                    <span class="spanProduto"> {{this.getUnidadeSelecionada().moeda}} {{vendaProduto.valorUnitario|
                      currency:false:''}}</span>
                  </td>
                  <td>
                    <div class="spanProduto">
                      <i class="pct-minus-circle btnAlterarQtd" (click)="removerQtdProduto(vendaProduto)"></i>
                      <span>
                        {{vendaProduto.qtd}}
                      </span>
                      <i class="pct-plus-circle btnAlterarQtd" (click)="adicionarQtdProduto(vendaProduto)"></i>
                    </div>
                  </td>
                  <td>
                    <span class="spanProduto"> {{this.getUnidadeSelecionada().moeda}} {{vendaProduto.getValorTotal()|
                      currency:false:''}}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div class="divBtnComprar"
             *ngIf="(getPlanoSelecionado() || getProdutosSelecionados()?.length > 0) && getConfig()">
          <button class="btn-continuar-mobile"
                  [style.background-color]="getConfig()?.cor"
                  (click)="continuar()">
            {{'rodape.continuar'|translate|uppercase}}
          </button>
        </div>
      </div>

      <div class="divInferiorMobile" *ngIf="!carrinho" [style.background]="getConfig()?.cor">

        <div class="divItens" (click)="visualizarCarrinho()">
          <div>
            <i class="pct-shopping-cart carrinho"></i>
            <div class="divQtdItens">
              <span class="btn-qtd-itens" [style.color]="getConfig()?.cor">
                {{getQtdItensCarrinho()}}
              </span>
            </div>
          </div>
          <span class="btn-vercarrinho">{{'rodape.ver-carrinho'|translate}}</span>
        </div>
      </div>
    </div>
  </div>
</footer>
