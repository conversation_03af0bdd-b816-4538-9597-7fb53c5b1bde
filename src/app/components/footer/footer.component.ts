import {Component, Input, OnInit, EventEmitter, Output} from '@angular/core';
import {Plano} from '@base-core/plano/plano.model';
import {PlanoService} from '@base-core/plano/plano.service';
import {Empresa} from '@base-core/empresa/empresa.model';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {Router} from '@angular/router';
import {Config} from '@base-core/empresa/config.model';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {ProdutoService} from '@base-core/produto/produto.service';
import {VendaProduto} from '@base-core/produto/produto.model';
import Swal from 'sweetalert2';
import {Observable} from 'rxjs';
import {map} from 'rxjs/operators';
import { ModalidadeService } from '@base-core/modalidade/modalidade-service';
import { Modalidade } from 'src/app/base/model/vendasOnlineV3.model';
import { TurmaService } from '@base-core/turma/turma.service';
import 'hammerjs';
import {FacebookApiConversaoService} from '@base-core/analytics/facebook-api-conversao.service';
import {FacebookPixelService} from '@base-core/analytics/facebook-pixel.service';
declare let fbq: Function;

@Component({
  selector: 'pacto-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss']
})
export class FooterComponent implements OnInit {
  @Input() vendaProduto: boolean;
  @Input() vendaPlano: boolean;
  @Input() componentSource: string;
  @Input() isDisabledButtonCont = false;
  @Input() isVisiblededButtonClear = false;
  @Input() currentTurmaIndex: number;
  @Input() weekSchedule: number;
  @Input() vendaComTurma = false;
  @Input() hasTurma = false;

  @Output() nextTurma = new EventEmitter<any>();

  currentUrl: string;
  carrinho: boolean;
  validado = false;
  modalidadesSelecionadas: Modalidade[];
  daySchedules: any[] = [];

  constructor(private planoService: PlanoService,
    private produtoService: ProdutoService,
    private router: Router,
    private negociacaoService: NegociacaoService,
    private empresaService: EmpresaService,
    private modalidadeService: ModalidadeService,
    private turmaService: TurmaService,
              private pixelService: FacebookPixelService
  ) {
    this.currentUrl = this.router.url;
    this.vendaProduto = false;
    this.vendaPlano = false;
  }

  ngOnInit() {
    this.carrinho = false;
    this.carregarModalidadesSelecionadas();
    this.carregarTurmasSelecionadas();
    this.loadConfigs();
  }

  loadConfigs() {
    this.empresaService
      .obterConfigs(this.negociacaoService.chave, this.negociacaoService.codunidade)
      .subscribe((data) => {
        this.empresaService.config = data;
      });
  }

  getVendaProduto(): boolean {
    return this.vendaProduto;
  }

  getVendaPlano(): boolean {
    return this.vendaPlano;
  }

  getPlanoSelecionado(): Plano {
    return this.planoService.planoSelecionado;
  }

  getConfig(): Config {
    return this.empresaService.config;
  }

  getApresentarValorDoPlano(): boolean {
    return this.empresaService.config.apresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano;
  }

  getUnidadeSelecionada(): Empresa {
    return this.empresaService.unidadeSelecionada;
  }

  limparCarrinho(): void {
    this.validado = false;
    this.limparPlano();
    this.limparProdutos();
  }

  limparProdutos(): void {
    this.produtoService.produtosSelecionados = [];
  }

  limparPlano(): void {
    this.planoService.planoSelecionado = null;
  }

  limparCupom() {
    this.negociacaoService.cupom = null;
  }

  /*
  obterTokenApiConversaoMeta(): string {
    if (this.empresaService && this.empresaService.config && this.empresaService.config.tokenApiConversao) {
      return this.empresaService.config.tokenApiConversao;
    }
    return null;
  }

  obterPixelId(): string {
    if (this.empresaService && this.empresaService.config && this.empresaService.config.pixelId) {
      return this.empresaService.config.pixelId;
    }
    return null;
  }
*/
  continuar() {
    this.negociacaoService.cupom = null;

    if (this.getProdutosSelecionados().length > 0) {
      this.getProdutosSelecionados().forEach(produto => {
        const paramsProduto = {
          produtoNome: produto.descricao,
          produtoValor: produto.valorUnitario,
        };
        this.pixelService.triggerEventFacebookIrParaCheckoutProduto(paramsProduto);
      });
    }

    if (this.getPlanoSelecionado()) {
      const paramsPlano = {
        planoNome: this.getPlanoSelecionado().nome,
        planoValor: this.getPlanoSelecionado().valorTotalDoPlano
      };
      this.pixelService.triggerEventFacebookIrParaCheckout(paramsPlano);
    }
/*
    const pixelId = this.obterPixelId();
    const tokenApiConversao = this.obterTokenApiConversaoMeta(); */

    if (this.empresaService.config && this.empresaService.config.usarFormaPagamentoPlanoProduto) {
      this.validarConfigsFormasPagamentosPlanoProduto().subscribe(isConfigValida => {
        if (!isConfigValida) {
          this.exibirMensagemFormasDePagamentoDiferentes();
          this.limparCarrinho();
          return;
        } else {
          this.proximoPasso();
        }
      });
    } else {
      this.proximoPasso();
    }
  }

  proximoPasso(): void {
    this.limparCupom();
    const planoSelecionado = this.getPlanoSelecionado();

    if (this.componentSource == 'turma' && (this.currentTurmaIndex < this.weekSchedule - 1)) {
      this.nextTurma.emit();
    } else {
      if ((this.componentSource == 'plano' || this.componentSource == 'loja') && planoSelecionado && planoSelecionado.vendaComTurma) {
        this.router.navigate(['/nascimento']);

      } else if (this.componentSource == 'nascimento') {
        this.router.navigate(['/modalidade']);

      } else if (this.componentSource == 'modalidade') {
        this.router.navigate(['/turma']);
      } else {
        this.router.navigate(['/checkout']);
      }
    }
  }

  validarConfigsFormasPagamentosPlanoProduto(): Observable<boolean> {
    return this.empresaService.obterDadosFormaPagamentoPlanoProduto(this.negociacaoService.chave, this.negociacaoService.codunidade)
      .pipe(
        map(data => {
          if (data.hasOwnProperty('return')) {
            const vendasConfigsFormaPagamentoPlanoProduto = data.return;
            let isConfigValida = true;
            if (vendasConfigsFormaPagamentoPlanoProduto.length > 0) {
              const mapaFormasPgProdutos: Map<number, number[]> = this.obterMapaFormasPgProdutos(vendasConfigsFormaPagamentoPlanoProduto);
              let codigosFormasPgPlanos = [];

              if (this.getPlanoSelecionado() && this.getPlanoSelecionado().codigo > 0) {
                codigosFormasPgPlanos = vendasConfigsFormaPagamentoPlanoProduto
                  .filter(cfg => cfg.plano === this.getPlanoSelecionado().codigo)
                  .map(cfg => cfg.formaPagamento);
              }
              if (this.getProdutosSelecionados().length > 0) {
                this.getProdutosSelecionados().forEach(prod => {
                  const codigosFormasPgProdutos = mapaFormasPgProdutos.get(prod.produto);
                  // validar se nao existem formas de pagamentos diferentes entre os produtos
                  if (codigosFormasPgProdutos && codigosFormasPgProdutos.length > 0) {
                    this.getProdutosSelecionados().forEach(prod2 => {
                      const codigosFormasPgProdutos2 = mapaFormasPgProdutos.get(prod2.produto);
                      if (codigosFormasPgProdutos2 && codigosFormasPgProdutos2.length
                        && prod2.produto !== prod.produto) {
                        if (!this.possuemMesmosCodigos(codigosFormasPgProdutos, codigosFormasPgProdutos2)) {
                          isConfigValida = false;
                        }
                      }
                    });
                    // Validar se nao existem formas de pagamentos diferentes entre planos e produtos
                    if (codigosFormasPgPlanos && codigosFormasPgPlanos.length > 0
                      && !this.possuemMesmosCodigos(codigosFormasPgPlanos, codigosFormasPgProdutos)) {
                      isConfigValida = false;
                    }
                  }
                });
              }
            }
            return isConfigValida;
          }
        })
      );
  }

  possuemMesmosCodigos(lista1: number[], lista2: number[]): boolean {
    if (lista1.length !== lista2.length) {
      return false;
    }
    const listaOrdenada1 = [...lista1].sort((a, b) => a - b);
    const listaOrdenada2 = [...lista2].sort((a, b) => a - b);
    for (let i = 0; i < listaOrdenada1.length; i++) {
      if (listaOrdenada1[i] !== listaOrdenada2[i]) {
        return false;
      }
    }
    return true;
  }

  private obterMapaFormasPgProdutos(vendasConfigsFormaPagamentoPlanoProduto): Map<number, number[]> {
    const mapa = new Map<number, number[]>();
    if (vendasConfigsFormaPagamentoPlanoProduto) {
      vendasConfigsFormaPagamentoPlanoProduto.forEach(cfg => {
        if (cfg.produto && cfg.produto > 0) {
          if (!mapa.get(cfg.produto)) {
            mapa.set(cfg.produto, []);
          }
          mapa.get(cfg.produto).push(cfg.formaPagamento);
        }
      });
    }
    return mapa;
  }

  exibirMensagemFormasDePagamentoDiferentes() {
    Swal.fire({
      type: 'warning',
      title: 'Atenção',
      text: 'Você está tentando adicionar plano e produtos em uma única compra, ' +
        'mas a forma de pagamento permitida para ambos é diferente. É necessário fazer a compra separadamente.',
      showConfirmButton: true,
    });
  }

  getProdutosSelecionados(): Array<VendaProduto> {
    return this.produtoService.produtosSelecionados;
  }

  removerProduto(produto: VendaProduto) {
    this.produtoService.removerProduto(produto);
  }

  adicionarQtdProduto(produto: VendaProduto) {
    produto.qtd = (produto.qtd + 1);
    this.produtoService.alterarQtdProduto(produto);
  }

  removerQtdProduto(produto: VendaProduto) {
    produto.qtd = (produto.qtd - 1);
    if (produto.qtd === 0) {
      this.removerProduto(produto);
    }
    this.produtoService.alterarQtdProduto(produto);
  }

  getQtdItensCarrinho(): number {
    return this.negociacaoService.getQtdItensCarrinho();
  }

  visualizarCarrinho(): void {
    this.carrinho = !this.carrinho;
  }

  getClassPLano(): string {

    if (this.vendaComTurma) {
      return 'col-md-3';
    }
    else if (this.getVendaPlano() && this.getVendaProduto()) {
      return 'col-md-2';
    } else if (this.getVendaPlano() && !this.getVendaProduto()) {
      return 'col-md-10';
    }
  }

  carregarModalidadesSelecionadas(): void {
    this.modalidadeService.modalidadesSelecionadas$.subscribe(modalidades => {
      this.modalidadesSelecionadas = modalidades.filter(mod => mod.selected);
    });
  }

  carregarTurmasSelecionadas(): void {
    this.turmaService.turmasSelecionadas$.subscribe(response => {
      if (!response) {
        response = [];
      }
      response.forEach(novaTurma => {
        const existente = this.daySchedules.find(ds => ds.modalidadeName === novaTurma.modalidadeName);
        if (existente) {
          existente.currentTurmaSessions = this.mergeSessions(existente.currentTurmaSessions, novaTurma.currentTurmaSessions);
        } else {
          this.daySchedules.push(novaTurma);
        }
      });
    });
  }

  private mergeSessions(existingSessions: any[], newSessions: any[]): any[] {
    const merged = [...existingSessions];
    newSessions.forEach(session => {
      const index = merged.findIndex(s => ((s.session.codigo == session.session.codigo) && (s.session.turma == session.session.turma)));
      if (index === -1) {
        merged.push(session);
      }
    });
    return newSessions;
  }

  getTooltipContent(): string {
    if (this.modalidadesSelecionadas.length > 3) {
      const extras = this.modalidadesSelecionadas.slice(3).map(mod => mod.modalidade).join(`\n`).toUpperCase();
      return extras;
    }
    return '';
  }

  getDaysTooltipContent(turma: any): string {
    return turma.currentTurmaSessions.map(s => `${s.day} - ${s.session.horaInicial + ' às ' + s.session.horaFinal}`).join(`\n`).toUpperCase();
  }

  getExibirValorPorMes(): boolean {
    const resposta = this.planoService.planoSelecionado.primeiraParcela == this.planoService.planoSelecionado.mensalidade && this.modalidadesSelecionadas != null && this.modalidadesSelecionadas.length > 0;
    return resposta;
  }

  getExibirValorPrimeiroMes(): boolean {
    const resposta = this.planoService.planoSelecionado.primeiraParcela != this.planoService.planoSelecionado.mensalidade && this.modalidadesSelecionadas != null && this.modalidadesSelecionadas.length > 0;
    return resposta;
  }

}
