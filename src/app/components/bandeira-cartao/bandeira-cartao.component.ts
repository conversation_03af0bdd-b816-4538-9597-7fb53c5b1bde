import {Component, Input, OnInit} from '@angular/core';
import creditCardType from 'credit-card-type';

@Component({
  selector: 'pacto-bandeira-cartao',
  templateUrl: './bandeira-cartao.component.html',
  styleUrls: ['./bandeira-cartao.component.scss']
})
export class BandeiraCartaoComponent implements OnInit {
  @Input() numero: string;
  cartoes = {
    VISA: /^4[0-9]{12}(?:[0-9]{3})/,
    MASTERCARD: /^5[1-5][0-9]{14}/,
    AMEX: /^3[47][0-9]{13}/,
    DINERSCLUB: /^3(?:0[0-5]|[68][0-9])[0-9]{11}/,
    DISCOVER: /^6(?:011|5[0-9]{2})[0-9]{12}/,
    ELO: /^((((636368)|(438935)|(504175)|(451416)|(636297))\d{0,10})|((5067)|(4576)|(4011))\d{0,12})/,
    JCB: /^(?:2131|1800|35\d{3})\d{11}/
  };

  constructor() {
  }

  ngOnInit() {
  }

  getBandeiraCartao(): string {
    try {
      if (!this.numero) {
        return null;
      }
      const tipo = creditCardType(this.numero);
      if (tipo[0]) {
        sessionStorage.setItem('BAND', tipo[0].type.toUpperCase());
        return tipo[0].type.toUpperCase();
      }
    } catch (e) {
    }
    return null;
  }
}
