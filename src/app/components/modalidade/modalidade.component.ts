import { Location } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Config } from '@base-core/empresa/config.model';
import { Empresa } from '@base-core/empresa/empresa.model';
import { EmpresaService } from '@base-core/empresa/empresa.service';
import { ModalidadeService } from '@base-core/modalidade/modalidade-service';
import { NascimentoService } from '@base-core/nascimento/nascimento.service';
import { NegociacaoService } from '@base-core/negociacao/negociacao.service';
import { Venda } from '@base-core/negociacao/venda.model';
import { PlanoService } from '@base-core/plano/plano.service';
import { VendaProduto } from '@base-core/produto/produto.model';
import { ProdutoService } from '@base-core/produto/produto.service';
import { TurmaService } from '@base-core/turma/turma.service';
import { combineLatest, from, of } from 'rxjs';
import { finalize, map, mergeMap, switchMap, take } from 'rxjs/operators';
import { Modalidade, TimesPerWeekDescriptions } from 'src/app/base/model/vendasOnlineV3.model';
import Swal from 'sweetalert2';
import { NascimentoComponent } from '../nascimento/nascimento.component';
import { OrigemSistemaEnum } from '@base-core/negociacao/enum-origem-sistema';

@Component({
  selector: 'pacto-modalidade',
  templateUrl: './modalidade.component.html',
  styleUrls: ['./modalidade.component.scss']
})
export class ModalidadeComponent implements OnInit {

  @ViewChild(NascimentoComponent) nascimentoComponent: NascimentoComponent;

  timesPerWeekDescriptions = TimesPerWeekDescriptions;
  modalidades: Modalidade[] = [];
  selectedModalitiesOrder: number[] = [];
  submitted = false;
  formGroup: FormGroup = new FormGroup({});

  empresaChave: any;
  unidadeSelecionada: any;
  nascimento: any;

  constructor(
    private empresaService: EmpresaService,
    private location: Location,
    private turmaService: TurmaService,
    private modalidadeService: ModalidadeService,
    private nascimentoService: NascimentoService,
    private planoService: PlanoService,
    private produtoService: ProdutoService,
    private negociacaoService: NegociacaoService
  ) {
    this.montarTratamentoF5(empresaService);
  }

  ngOnInit(): void {
    this.empresaChave = this.empresaService.unidadeSelecionada.chave;
    this.unidadeSelecionada = this.empresaService.unidadeSelecionada.codigo;
    this.nascimento = this.nascimentoService.getIdade();
    this.getData();
  }

  getData(): any {
    combineLatest([
      of(this.planoService.planoSelecionado.modalidadesDTO),
      this.modalidadeService.modalidadesSelecionadas$.pipe(take(1))
    ])
      .pipe(
        switchMap(([modalidades, selecionadas]) => {
          const updatedModalidades = modalidades.map(mod => {
            const selecionada = selecionadas.find(s => s.id === mod.id);
            const isAgeAppropriate = true;
            const hasVaga = true;

            return {
              ...mod,
              selected: isAgeAppropriate && hasVaga ? mod.selected : false,
              selectedTimesPerWeek: selecionada ? selecionada.selectedTimesPerWeek : mod.selectedTimesPerWeek || null,
              isAgeAppropriate: isAgeAppropriate,
              hasVaga: hasVaga
            };
          });

          return from(updatedModalidades).pipe(
            mergeMap(m => {
              Swal.fire({
                title: "Processando",
                allowOutsideClick: false,
                onOpen: function () {
                  Swal.showLoading();
                }
              });

              return this.turmaService.obterTurmas(this.empresaChave, [m.codigo], this.unidadeSelecionada, this.nascimento, null).pipe(
                map(modalidadeTurma => ({ m, modalidadeTurma })),
                finalize(() => {
                  Swal.close();
                })
              );
            })
          );
        })
      )
      .subscribe(({ m, modalidadeTurma }) => {
        if (modalidadeTurma.length > 0) {
          let todasCheia = true;
          modalidadeTurma.forEach(mT => {
            mT.turmas.forEach(t => {
              t.diasSemana.forEach(d => {
                d.horarios.forEach(h => {
                  if (!(h.ocupacao >= h.nrMaximoAluno)) {
                    todasCheia = false;
                  }
                });
              });
            });
          });
          if (todasCheia) {
            this.modalidades.push({
              ...m,
              selected: false,
              hasVaga: false
            });
          } else {
            this.modalidades.push({
              ...m,
              selected: this.empresaService.config.modalidadesIniciarSelecionadasContratoTurma,
              selectedTimesPerWeek: m.nrsVezesSemana.length === 1 ? m.nrsVezesSemana[0] : null
            });
          }
        } else {
          if (!m.utilizarTurma) {
            this.modalidades.push({
              ...m,
              selected: true,
              hasVaga: true,
              selectedTimesPerWeek: m.nrsVezesSemana.length === 1 ? m.nrsVezesSemana[0] : null
            });
          } else {
            this.modalidades.push({
              ...m,
              selected: false,
              isAgeAppropriate: false
            });
          }
        }
        this.atualizarModalidadesSelecionadas();
      });
  }

  montarTratamentoF5(empService) {
    window.addEventListener('keydown', function (e) {
      const code = e.which || e.keyCode;
      if (code == 116) {
        e.preventDefault();
      } else {
        return true;
      }
      Swal.fire({
        type: 'warning',
        text: 'Ao atualizar a página, você será redirecionado para o fluxo inicial de planos.',
        showConfirmButton: true,
        onClose: () => {
          let url = window.location.href.replace('modalidade', 'planos');
          url += '?un=' + empService.unidadeSelecionada.codigo + '&k=' + empService.unidadeSelecionada.chave;
          window.location.href = url;
        }
      });
    });
  }

  toggleActivity(modalidade: Modalidade): void {
    modalidade.selected = !modalidade.selected;
    if (modalidade.selected) {
      modalidade.selectedTimesPerWeek = modalidade.nrsVezesSemana.length === 1 ? modalidade.nrsVezesSemana[0] : null;
      if (!this.selectedModalitiesOrder.includes(modalidade.codigo)) {
        this.selectedModalitiesOrder.push(modalidade.codigo);  // Adiciona a ID ao final do array quando selecionada
      }
    } else {
      modalidade.selectedTimesPerWeek = null;  // Resetar a frequência quando desmarcar
      this.selectedModalitiesOrder = this.selectedModalitiesOrder.filter(id => id !== modalidade.codigo);  // Remove a ID do array
    }
    this.atualizarModalidadesSelecionadas();
  }

  atualizarModalidadesSelecionadas(): void {
    const modalidadesSelecionadas: Modalidade[] = [];
    for (const modalidade of this.modalidades) {
      if (modalidade.selected) {
        modalidadesSelecionadas.push(modalidade);
      }
    }

    const selecionadas = modalidadesSelecionadas.sort((a, b) => this.selectedModalitiesOrder.indexOf(a.codigo) - this.selectedModalitiesOrder.indexOf(b.codigo));

    this.modalidadeService.atualizarModalidadesSelecionadas(selecionadas);
    this.simularVenda();
  }

  selectFrequency(modalidade: Modalidade, frequency: number): void {
    modalidade.selectedTimesPerWeek = frequency;
    this.atualizarModalidadesSelecionadas();
    this.simularVenda();
  }

  simularVenda() {
    if (this.planoService.planoSelecionado.codigo) {
      this.negociacaoService
        .simularVenda(this.getVendaJson(), this.negociacaoService.codunidade)
        .subscribe((contrato) => {
          this.setarValorFinalPlano(contrato);
        });
    }
  }

  getVendaJson(): Venda {
    return new Venda(this.empresaService.unidadeSelecionada.codigo,
      (this.planoService.planoSelecionado ? this.planoService.planoSelecionado.codigo : 0),
      (this.formGroup.get('nome') ? this.formGroup.get('nome').value : null),
      (this.formGroup.get('cpf') ? this.formGroup.get('cpf').value : null),
      (this.formGroup.get('sexo') ? this.formGroup.get('sexo').value : null),
      (this.formGroup.get('dataNascimento') ? this.formGroup.get('dataNascimento').value : ''),
      this.formGroup.get('email') ? this.formGroup.get('email').value : null,
      this.formGroup.get('nomecartao') ? this.formGroup.get('nomecartao').value : null,
      this.formGroup.get('nrcartao') ? this.formGroup.get('nrcartao').value.replace(/ /g, '') : '',
      this.formGroup.get('validade') ? this.formGroup.get('validade').value.replace('/', '/20') : '',
      this.formGroup.get('cvv') ? this.formGroup.get('cvv').value : null,
      (this.formGroup.get('telefone') ? this.formGroup.get('telefone').value : null),
      (this.formGroup.get('endereco') ? this.formGroup.get('endereco').value : null),
      (this.formGroup.get('numero') ? this.formGroup.get('numero').value : null),
      (this.formGroup.get('bairro') ? this.formGroup.get('bairro').value : null),
      (this.formGroup.get('complemento') ? this.formGroup.get('complemento').value : null),
      (this.formGroup.get('cep') ? this.formGroup.get('cep').value : null),
      (this.formGroup.get('diavencimento') ? this.formGroup.get('diavencimento').value : null),
      (this.formGroup.get('parcelasCartao') ? this.formGroup.get('parcelasCartao').value : null),
      (this.formGroup.get('cupomdesconto') ? this.formGroup.get('cupomdesconto').value : ''),
      (this.formGroup.get('cpftitularcard') ? this.formGroup.get('cpftitularcard').value : null),
      (this.formGroup.get('vencimentoFatura') ? this.formGroup.get('vencimentoFatura').value : 0),
      [], this.negociacaoService.cobrarParcelasEmAberto,
      (this.formGroup.get('dataInicioContrato') ? this.formGroup.get('dataInicioContrato').value : null),
      (this.formGroup.get('responsavelPai') ? this.formGroup.get('responsavelPai').value : null),
      (this.formGroup.get('responsavelMae') ? this.formGroup.get('responsavelMae').value : null),
      (this.formGroup.get('cpfMae') ? this.formGroup.get('cpfMae').value : null),
      (this.formGroup.get('cpfPai') ? this.formGroup.get('cpfPai').value : null),
      [], false, (this.negociacaoService.categoriaPlano ? this.negociacaoService.categoriaPlano : null),
      this.negociacaoService.origemCobranca, this.negociacaoService.cobrancaAntecipada, this.negociacaoService.responsavel,
      this.negociacaoService.token, this.planoService.vezesEscolhidasParcelarTaxaMatricula,
      (this.formGroup.get('rg') ? this.formGroup.get('rg').value : null), this.negociacaoService.codigoEvento,
      this.negociacaoService.usuarioResponsavel, this.negociacaoService.codigoRegistroAcessoPagina,
      '',
      (this.formGroup.get('tipoCredito') ? this.formGroup.get('tipoCredito').value : null), '',
      (this.formGroup.get('cnpj') ? this.formGroup.get('cnpj').value : null),
      (this.formGroup.get('nomeResponsavelEmpresa') ? this.formGroup.get('nomeResponsavelEmpresa').value : null),
      (this.formGroup.get('cpfResponsavelEmpresa') ? this.formGroup.get('cpfResponsavelEmpresa').value : null),
      this.negociacaoService.lancarContratoConcomitanteAoInvesDeRenovar, this.negociacaoService.pactoPayComunicacao, null,
      null, this.modalidadeService.modalidadesSelecionadas, null, null, false,
      OrigemSistemaEnum.VENDAS_ONLINE, null, false,
      false,
      []
    );
  }

  private setarValorFinalPlano(data): void {
    this.negociacaoService.valorFinalContrato = data.return.valorBase;
    this.negociacaoService.parcelas = data.return.parcelas;
    this.negociacaoService.valorPrimeiraParcela = data.return.valorPrimeiraParcela;
    this.negociacaoService.valorProRata = data.return.valorProRata;
    this.negociacaoService.descricaoCobrancaPrimeiraParcela = data.return.descricaoCobrancaPrimeiraParcela;
    this.negociacaoService.anoCobrancaAnuidade = data.return.anoCobrancaAnuidade;
    this.planoService.planoSelecionado.primeiraParcela = data.return.valorPrimeiraParcela;
    this.planoService.planoSelecionado.valorTotalPlanoTurma = data.return.valorFinal;
  }

  get hasSelectedModalidade(): boolean {
    const allValid = this.modalidades.every(mod => {
      if (!mod.selected) return true;
      if (mod.nrsVezesSemana) {
        // Avalia se selectedTimesPerWeek está indefinido
        if (mod.selectedTimesPerWeek === undefined || mod.selectedTimesPerWeek === null) {
          // Se estiver indefinido, atribui o primeiro valor do array nrsVezesSemana
          if (mod.nrsVezesSemana && mod.nrsVezesSemana.length > 0) {
            mod.selectedTimesPerWeek = mod.nrsVezesSemana[0];
          }
        }
        return mod.selectedTimesPerWeek !== null;
      }
      return true;
    });
    const anySelected = this.modalidades.some(mod => mod.selected && mod.utilizarTurma);
    this.submitted = true;
    return allValid && anySelected;
  }

  getConfig(): Config {
    return this.empresaService.config;
  }
  getUnidadeSelecionada(): Empresa {
    return this.empresaService.unidadeSelecionada;
  }

  previousStep(): void {
    this.location.back();
    this.modalidadeService.atualizarModalidadesSelecionadas([]);
    this.modalidades = [];
  }

  getProdutosSelecionados(): Array<VendaProduto> {
    return this.produtoService.produtosSelecionados;
  }

}
