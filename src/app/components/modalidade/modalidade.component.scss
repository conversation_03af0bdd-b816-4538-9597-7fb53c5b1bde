@import "../../../assets/css/variaveis";
.container {
  width: 80%;
  margin: auto;
  margin-bottom: $alturafooter + 30px;
}

h1 {
  font-size: 30px;
  color: #2c343b;
  font-weight: normal;
}

h4 {
  font-size: 16px;
  color: #bdc3c7;
}

.resumo {
  font-size: 24px;
  color: #bdc3c7;
}

@media only screen and (max-width: 748px) {
  .container {
    width: 100%;
  }
}


.list-card {
  width: 100%;
  height: auto;
  border: 1px solid #E5E9F2;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 4px #E5E9F2;

  &.selected {
    height: auto;
    border: 1px solid #1998FC;
    background-color: #E8F4FE;
  }
}

:host ::ng-deep .list-card mat-radio-button:first-child .mdc-radio {
  padding: 10px 10px 10px 0 !important;
}

.custom-icon {
  border-radius: 50% !important;
}

.has-error input[type="radio"]:invalid {
  outline: 2px solid red; /* Estilo para destacar erro */
}

.has-error label {
  color: red;
}

.text-red{
  color: red;
}


.disabled {
  opacity: 0.5;
  pointer-events: none; /* Impede cliques e interações */
}

.disabled .material-symbols-outlined {
  color: #ECF0F1; /* Cor do ícone quando desabilitado */
}
