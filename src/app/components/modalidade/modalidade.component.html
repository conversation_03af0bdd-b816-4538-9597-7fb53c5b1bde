<div class="container">
  <pacto-header [config]="getConfig()" [unidade]="getUnidadeSelecionada()"></pacto-header>

  <div class="d-flex align-items-center">
    <img class="mr-2 cursor-pointer" src="assets/images/icon/arrow_circle_left.svg" (click)="previousStep()"/>
    <div>
      <p class="title">Selecione uma modalidade </p>
    </div>
  </div>
  <p class="title-Card">Quais modalidades você deseja incluir?</p>

  <div class="list-card" *ngFor="let activity of modalidades; let idx = index" [class.selected]="activity.selected" [class.disabled]="!activity.isAgeAppropriate || !activity.hasVaga">
    <div class="row  align-items-center">
      <div class="col-3">
        <span class="title-Card">{{ activity.modalidade }}</span>
      </div>
      <div class="col-7">
        <span class="subTitle-Card">{{ activity.description }}</span>
      </div>
      <div class="col-2 d-flex align-items-center justify-content-end cursor-pointer" (click)="(!activity.isAgeAppropriate || !activity.hasVaga) || toggleActivity(activity)">
        <span class="me-1 popins14">{{ activity.selected ? 'DESMARCAR' : 'SELECIONAR' }}</span>
          <div [attr.aria-label]="activity.selected ? 'DESMARCAR' : 'SELECIONAR'">
          <span class="material-symbols-outlined cursor-pointer ml-2"
            [style.color]="activity.selected && (activity.isAgeAppropriate || activity.hasVaga) ? '#1998FC' : '#ECF0F1'">
            task_alt
          </span>
        </div>
      </div>

      <div class="col-12 mt-2">
        <hr style="background: #95A5A6;">
      </div>

      <div *ngIf="!activity.isAgeAppropriate" class="col-12 mt-3 d-flex align-items-end ">
        <img src="assets/images/icon/pct-alert-triangle.svg" class="mr-2">
        <span class="nunito-sans16"> Esta modalidade não esta diponível para sua faixa etária </span>
      </div>

      <div *ngIf="!activity.hasVaga" class="col-12 mt-3 d-flex align-items-end ">
        <img src="assets/images/icon/pct-alert-triangle.svg" class="mr-2">
        <span class="nunito-sans16"> Esta modalidade não possui horário disponível </span>
      </div>

      <div *ngIf="activity.selected" class="col-12 mt-2">
        <div *ngIf="activity.nrsVezesSemana">
          <div class="mb-3 mt-3" *ngIf="activity.nrsVezesSemana.length == 1">
            <span class="interTitle-Card">Você pode realizar esta modalidade {{activity.nrsVezesSemana[0] > 1 ? activity.nrsVezesSemana[0]+' vezes por semana' : activity.nrsVezesSemana[0]+' vez por semana'}} </span>
          </div>
          <div class="mb-3 mt-3" *ngIf="activity.nrsVezesSemana.length > 1">
            <span class="interTitle-Card">Quantas vezes na semana deseja praticar suas modalidades?</span>
          </div>
          <div [class.has-error]="!activity.selectedTimesPerWeek && submitted" *ngIf="activity.nrsVezesSemana.length > 1">
            <label aria-label="Select times per week" class="d-flex">
              <div *ngFor="let time of activity.nrsVezesSemana; let i = index">
                <input type="radio" [id]="'time-' + idx + '-' + i" [name]="'timesPerWeek-' + idx"
                  [(ngModel)]="activity.selectedTimesPerWeek" [value]="time" (change)="selectFrequency(activity, time)">
                <label class="interTitle-Card-radio" [for]="'time-' + idx + '-' + i">{{ timesPerWeekDescriptions.get(time) }}</label>
              </div>
            </label>
          </div>
        </div>
        <div *ngIf="!activity.nrsVezesSemana && activity.selected" class="col-12 pl-0 mt-3 d-flex align-items-end ">
          <img src="assets/images/icon/pct-alert-triangle.svg" class="mr-2">
          <span class="interTitle-Card"> {{ activity.notice }} </span>
        </div>
      </div>

    </div>
  </div>
  <pacto-footer [vendaProduto]="getProdutosSelecionados().length > 0" [vendaPlano]="true" [componentSource]="'modalidade'"
    [isDisabledButtonCont]="!hasSelectedModalidade" [isVisiblededButtonClear]="true"
    [vendaComTurma]="true"></pacto-footer>
</div>
