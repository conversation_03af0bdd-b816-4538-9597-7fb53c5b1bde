<div class="layer" *ngIf="getPlanoSelecionado()">
  <div class="modal">
    <div class="fechar">
      <a (click)="desselecionarPlano()" *ngIf="getConfig()" [style.color]="getConfig().cor"
         [style.border-color]="getConfig().cor">
        <i class="pct-x-circle"></i>
      </a>
    </div>

    <div class="dadosplanomodal">
        <div class="container" [style.border-left-color]="getConfig().cor" *ngIf="getConfig()">

          <div class="arrow-right" [style.border-left-color]="getConfig().cor"></div>

          <div>

            <span class="titulo">{{"checkout.voce-selecionou"|translate}}</span>

            <div>
              <div class="nome-plano">
                <div><i class="pct-package"></i>{{getPlanoSelecionado().nome.toLowerCase()}} <div class="mensalidade">{{this.getMoeda()}} {{getPlanoSelecionado().mensalidade| currency:false:''}}/{{"checkout.mes"|translate}}</div></div>
              </div>

              <div>
              </div>
            </div>

          </div>


          <div class="span_2_of_5 ta-center pedido ta-center">
            <span class="titulo">{{"checkout.o-valor-da-sua-primeira-parcela-sera"|translate}}:</span>
            <span class="primeiraparcela" [style.color]="getConfig().cor"
                  *ngIf="getPlanoSelecionado() && getConfig() && !getPlanoSelecionado().inicioFuturo">{{this.getMoeda()}} {{(getPlanoSelecionado().primeiraParcela + (getPlanoSelecionado().regimeRecorrencia ? getPlanoSelecionado().adesao : getPlanoSelecionado().matricula) + getPlanoSelecionado().valorProdutos + (getPlanoSelecionado().mesAnuidade && getPlanoSelecionado().anuidadeAgora ? getPlanoSelecionado().anuidade : 0 ))| currency:false:''}}</span>

            <span class="primeiraparcela" [style.color]="getConfig().cor"
                  *ngIf="getPlanoSelecionado() && getConfig() && getPlanoSelecionado().inicioFuturo && !getPlanoSelecionado().regimeRecorrencia">{{this.getMoeda()}}{{(getPlanoSelecionado().matricula)| currency:false:''}}<small style="color: #bdc3c7; font-weight: normal"> *{{"checkout.matricula"|translate}}</small></span>
            <span class="primeiraparcela" [style.color]="getConfig().cor"
                  *ngIf="getPlanoSelecionado() && getConfig() && getPlanoSelecionado().inicioFuturo && getPlanoSelecionado().regimeRecorrencia">{{this.getMoeda()}} {{(getPlanoSelecionado().adesao)| currency:false:''}}<small style="color: #bdc3c7; font-weight: normal"> *{{"checkout.adesao"|translate}}</small></span>
          </div>



        </div>

        <div class="span_2_of_2 detalhesplano">
          <div *ngIf="getPlanoSelecionado().descricaoEncantamento">{{getPlanoSelecionado().descricaoEncantamento}}</div>

          <span class="titulo">{{"checkout.esse-plano-inclui"|translate}}:</span>
          <span *ngFor="let m of getPlanoSelecionado().modalidades" class="modalidade">
           <i [style.color]="getConfig().cor" class="tem pct-check-circle"></i> <span>{{m}}</span>
        </span>

          <div *ngIf="getPlanoSelecionado().produtos">
            <span class="titulo">{{"checkout.produtos-no-plano"|translate}}:</span>
            <span class="detalhe">{{getPlanoSelecionado().produtos}}</span>
          </div>

          <div *ngIf="getPlanoSelecionado().qtdCreditoPlanoCredito">
            <span class="titulo">{{"checkout.quantidade-de-creditos-a-adquirir"|translate}}:</span>
            <span class="detalhe">{{getPlanoSelecionado().qtdCreditoPlanoCredito}}</span>
          </div>

          <div *ngIf="getPlanoSelecionado().anuidade > 0.0">
            <span class="titulo">{{"checkout.mes-em-que-sera-cobrada-a-anuidade"|translate}}:</span>
            <span class="detalhe">{{getMesAnoCobrancaAnuidade()}}</span>
          </div>
        </div>

        <div class="leiacontrato">
          <pacto-visualizar-documento [tipo]="'contrato'" [inside]="true"
                                      [plano]="getPlanoSelecionado().codigo"
                                      [textoLink]="'global.leiacontrato'|translate"></pacto-visualizar-documento>
        </div>
    </div>

    <div>
      <span class="selecionar" *ngIf="getPlanoSelecionado() && getConfig()"
            [style.background-color]="getConfig().cor">
        <span class="btn" routerLink="/checkout">{{"checkout.continuar"|translate}}</span>
        <span class="detalhes"><i class="pct-arrow-right"></i> </span>
      </span>
    </div>

  </div>
</div>
