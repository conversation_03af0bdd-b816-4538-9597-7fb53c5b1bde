import { Component, OnInit } from '@angular/core';
import {PlanoService} from '@base-core/plano/plano.service';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {NegociacaoService} from '@base-core/negociacao/negociacao.service';
import {Plano} from '@base-core/plano/plano.model';
import {Config} from '@base-core/empresa/config.model';

@Component({
  selector: 'pacto-modal-plano-mobile',
  templateUrl: './modal-plano-mobile.component.html',
  styleUrls: ['./modal-plano-mobile.component.scss']
})
export class ModalPlanoMobileComponent implements OnInit {

  constructor(private planoService: PlanoService,
              private empresaService: EmpresaService,
              private negociacaoService: NegociacaoService) { }

  ngOnInit() {
  }
  getConfig(): Config {
    return this.empresaService.config;
  }
  getMoeda():string{
    return this.empresaService.unidadeSelecionada.moeda;
  }
  getPlanoSelecionado(): Plano {
    return this.planoService.planoSelecionado;
  }
  desselecionarPlano(): void {
    this.planoService.planoSelecionado = null;
  }
  getMesAnoCobrancaAnuidade(): string {
    return (this.planoService.planoSelecionado ? this.planoService.planoSelecionado.mesAnuidade : '') + (this.negociacaoService.anoCobrancaAnuidade ? ' / ' + this.negociacaoService.anoCobrancaAnuidade : '');
  }
}
