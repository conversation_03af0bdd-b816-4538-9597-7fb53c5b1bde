import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { PixAutomaticoService } from '@base-core/pix-automatico/pix-automatico.service';
import { PixAutomatico } from '@base-core/pix-automatico/pix-automatico.model';
import { EmpresaService } from '@base-core/empresa/empresa.service';
import { Empresa } from '@base-core/empresa/empresa.model';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-pix-automatico',
  templateUrl: './pix-automatico.component.html',
  styleUrls: ['./pix-automatico.component.scss']
})
export class PixAutomaticoComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  pixAutomatico: PixAutomatico | null = null;
  empresa: Empresa | null = null;
  chave: string = '';
  idRec: string = '';
  externalId: string = '';
  tipoConsulta: 'idRec' | 'external_id' | null = null;
  carregando: boolean = true;
  erro: boolean = false;
  mensagemErro: string = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private pixAutomaticoService: PixAutomaticoService,
    private empresaService: EmpresaService
  ) { }

  ngOnInit(): void {
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        this.chave = params['k'] || '';
        this.idRec = params['idRec'] || '';
        this.externalId = params['external_id'] || '';

        // Validação: deve ter chave e exatamente um dos parâmetros (idRec ou external_id)
        const temIdRec = !!this.idRec;
        const temExternalId = !!this.externalId;

        if (!this.chave) {
          this.exibirErro('Parâmetro "k" é obrigatório!');
          return;
        }

        if (temIdRec && temExternalId) {
          this.exibirErro('Não é possível usar idRec e external_id simultaneamente!');
          return;
        }

        if (!temIdRec && !temExternalId) {
          this.exibirErro('É necessário informar idRec ou external_id!');
          return;
        }

        // Define o tipo de consulta
        this.tipoConsulta = temIdRec ? 'idRec' : 'external_id';
        this.carregarDadosPixAutomatico();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  carregarDadosPixAutomatico(): void {
    this.carregando = true;
    this.erro = false;

    // Chama o método correto baseado no tipo de consulta
    const observable = this.tipoConsulta === 'idRec'
      ? this.pixAutomaticoService.obterPixAutomatico(this.chave, this.idRec)
      : this.pixAutomaticoService.obterPixAutomaticoPorExternalId(this.chave, this.externalId);

    observable
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (dados) => {
          this.pixAutomatico = dados;
          this.carregando = false;
          // Carregar dados da empresa após obter o código
          this.carregarDadosEmpresa();
        },
        error: (error) => {
          console.error('Erro ao carregar dados do PIX Automático:', error);

          // Verifica se é erro de link já utilizado
          if (error.message && error.message.includes('O link de autorização é de acesso único')) {
            Swal.fire({
              title: 'Aviso',
              text: error.message,
              type: 'warning',
              showConfirmButton: false,
              customClass: {
                popup: 'swal-popup-altura-customizada'
              }
            });
          } else {
            this.exibirErro('Erro ao carregar informações da autorização!');
          }
        }
      });
  }

  autorizarPix(): void {
    if (!this.pixAutomatico || !this.pixAutomatico.urlRedirect) {
      Swal.fire({
        title: 'Erro',
        text: 'URL de autorização não disponível',
        type: 'error',
        confirmButtonText: 'OK'
      });
      return;
    }

    // Redireciona para a URL de autorização
    window.location.href = this.pixAutomatico.urlRedirect;

    // Marca o link como utilizado no backend (só para consultas por idRec)
    if (this.tipoConsulta === 'idRec') {
      this.pixAutomaticoService.marcarLinkUtilizado(this.chave, this.idRec)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            console.log('Link marcado como utilizado:', response);
          },
          error: (error) => {
            console.error('Erro ao marcar link como utilizado:', error);
            // Não exibe erro para o usuário pois o redirecionamento já aconteceu
          }
        });
    }
  }

  formatarValor(valor: number | string): string {
    // Se já é uma string formatada (ex: "R$ 120,00"), retorna como está
    if (typeof valor === 'string' && valor.includes('R$')) {
      return valor;
    }

    // Se é string numérica, converte para number
    const numeroValor = typeof valor === 'string' ? parseFloat(valor.replace(',', '.')) : valor;

    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(numeroValor);
  }

  formatarData(data: string): string {
    // As datas já vêm formatadas do backend
    return data || 'Indeterminado (até que realize o cancelamento)';
  }

  carregarDadosEmpresa(): void {
    if (!this.pixAutomatico || !this.pixAutomatico.codigoEmpresa) {
      return;
    }

    // Usar código dinâmico que vem do PIX Automático
    const codigoUnidade = this.pixAutomatico.codigoEmpresa.toString();

    this.empresaService.obterEmpresa(this.chave, codigoUnidade)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (empresa) => {
          this.empresa = empresa;
        },
        error: (error) => {
          console.error('Erro ao carregar dados da empresa:', error);
          // Não exibe erro para não atrapalhar o fluxo principal
        }
      });
  }

  private exibirErro(mensagem: string): void {
    this.erro = true;
    this.carregando = false;
    this.mensagemErro = mensagem;
  }
}
