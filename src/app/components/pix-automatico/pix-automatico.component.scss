.pix-automatico-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

// Grid system classes (similar to /loja page)
.row {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  align-items: flex-start;
  margin: 0;
  padding: 0;
}

.span_1_of_2 {
  width: 50%;
  box-sizing: border-box;

  &:first-child {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    margin-top: 0;
    padding-top: 0;
  }
}

.pacto-no-margin-padding {
  margin: 0;
  padding: 0;
}

// Loading
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e3e3e3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    will-change: transform;
    margin-bottom: 16px;
  }

  p {
    color: #666;
    font-size: 16px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Erro
.erro-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  text-align: center;

  .erro-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  h3 {
    color: #333;
    margin-bottom: 8px;
    font-size: 20px;
  }

  p {
    color: #666;
    margin-bottom: 24px;
    font-size: 16px;
  }

  .btn-voltar {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;

    &:hover {
      background-color: #0056b3;
    }
  }
}

// Conteúdo principal
.conteudo-principal {
  max-width: 480px;
  margin: 0 auto;
  background-color: white;
  min-height: 100vh;
}

.header {
  background-color: white;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;

  .container-header {
    width: 100%;
    position: relative;
    margin: 0;
    border-bottom: none;
    height: auto;
    padding: 10px 0;
    box-sizing: initial;
    margin-bottom: 10px;
    align-items: flex-start;
  }

  .logo-unidade {
    width: 120px;
    height: 84px;
    object-fit: contain;
    vertical-align: top;
    margin: 0;
    padding: 0;
    display: block;
    align-self: flex-start;
  }

  .dadosunidade {
    text-align: right;
    margin-top: 5px;
    padding-top: 5px;

    .unidade {
      text-transform: capitalize;
      color: #333;
      display: block;
      font-size: 14px;
      font-weight: 600;
      margin-top: 0;
      line-height: 1.2;
    }

    .info-unidade {
      margin-top: 3px;
      color: #bdc3c7;
      font-size: 12px;
      line-height: 1.3;
    }
  }

  .titulo-pagina {
    text-align: center;
    padding: 20px 0;
    border-bottom: none;

    h1 {
      font-size: 18px;
      color: #333;
      margin: 0;
      font-weight: 600;
    }
  }
}

.secao-card {
  background-color: white;
  margin: 16px 20px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:first-of-type {
    margin-top: 0;
  }

  .secao-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .icone {
      font-size: 20px;
      margin-right: 12px;
    }

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }
}

.campo-grupo {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  label {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
  }

  .valor {
    font-size: 16px;
    color: #333;
    line-height: 1.4;
    word-break: break-word;
    overflow-wrap: break-word;
  }

  .valor-destaque {
    font-size: 16px;
    color: #333;
    font-weight: 600;
    line-height: 1.4;
    word-break: break-word;
    overflow-wrap: break-word;
  }

  .label-destaque {
    font-weight: bold !important;
  }

  .valor-vermelho {
    color: #e74c3c !important;
  }

  .sub-informacoes {
    margin-top: 10px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 4px;

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        font-size: 12px;
        color: #6c757d;
        line-height: 1.4;
        margin-bottom: 6px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .valor-secundario {
    font-size: 14px;
    color: #888;
    margin-top: 2px;
  }

  .valor-monetario {
    font-size: 20px;
    color: #2e7d32;
    font-weight: 700;
  }
}

// Seção de Pagamento inicial
.pagamento-inicial-secao {
  margin: 20px 0 20px -8px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;

  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 16px 0;
  }

  .campo-grupo {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    label {
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }

    .valor {
      font-size: 16px;
      color: #333;
    }
  }
}

.aviso-importante {
  margin: 20px;
  padding: 16px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;

  p {
    font-size: 14px;
    color: #856404;
    margin: 0;
    line-height: 1.5;
  }
}

.pacto-btn-primary {
  margin: 20px 15px;
  width:100%;
  background-color: #25d366;
  line-height: 0px;
  border: none;
  justify-self: center;
}

.botao-container {
  padding: 24px 20px;

  .btn-autorizar {
    width: 100%;
    background: linear-gradient(135deg, #1a365d 0%, #2c5282 100%);
    color: white;
    border: none;
    padding: 20px 24px;
    border-radius: 16px;
    font-size: 18px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(26, 54, 93, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #2c5282 0%, #3182ce 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(26, 54, 93, 0.4);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
    }

    &:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
  }
}

.footer-info {
  padding: 20px;
  text-align: center;

  p {
    font-size: 12px;
    color: #888;
    line-height: 1.5;
    margin: 0;
  }
}

// Responsividade para mobile
@media only screen and (max-width: 748px) {
  .container-header {
    height: auto;
  }

  .row {
    display: flex !important;
    flex-direction: row !important;
    align-items: flex-start;
  }

  .span_1_of_2 {
    width: 50% !important;

    &:first-child {
      width: 40% !important;
    }

    &:last-child {
      width: 60% !important;
    }
  }

  .logo-unidade {
    width: 80px;
    height: 56px;
  }

  .dadosunidade {
    text-align: right !important;
    margin-top: 0;
    padding-top: 0;

    .unidade {
      font-size: 12px;
    }

    .info-unidade {
      font-size: 10px;
    }
  }
}

// Responsividade para telas maiores
@media (min-width: 768px) {
  .conteudo-principal {
    max-width: 600px;
  }

  .secao-card {
    margin: 20px 40px;
    padding: 24px;
  }

  .header {
    padding: 24px 40px;
  }

  .botao-container {
    padding: 20px 40px;
  }

  .aviso-importante {
    margin: 20px 40px;
  }

  .footer-info {
    padding: 20px 40px;
  }
}
