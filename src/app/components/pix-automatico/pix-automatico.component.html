<div class="pix-automatico-container">
  <!-- Loading -->
  <div *ngIf="carregando" class="loading-container">
    <div class="spinner"></div>
    <p>Carregando informações...</p>
  </div>

  <!-- Erro -->
  <div *ngIf="erro" class="erro-container">
    <div class="erro-icon">⚠️</div>
    <h3>Ops! Algo deu errado</h3>
    <p>{{ mensagemErro }}</p>
    <button class="btn-voltar" (click)="carregarDadosPixAutomatico()">
      Tentar novamente
    </button>
  </div>

  <!-- Conteúdo principal -->
  <div *ngIf="!carregando && !erro && pixAutomatico" class="conteudo-principal">
    <!-- Header -->
    <div class="header">
      <div class="row" style="margin-top: -20px;">
        <div class="pacto-no-margin-padding span_1_of_2">
          <img *ngIf="empresa && empresa.logo" class="logo-unidade" [src]="empresa.logo" [style.border-radius]="'10px'" id="logo-unidade-zw"/>
        </div>

        <div *ngIf="empresa" class="pacto-no-margin-padding dadosunidade span_1_of_2">
          <span class="unidade">{{ pixAutomatico.nomeEmpresa.toLowerCase() }}</span>
          <div>
            <div class="info-unidade">
              <div *ngIf="empresa.telefone">{{ empresa.telefone }}</div>
              <div *ngIf="empresa.endereco">{{ empresa.endereco }} {{ empresa.complemento }}</div>
              <div *ngIf="empresa.cidade">{{ empresa.cidade }} - CEP: {{ empresa.cep }}</div>
              <div *ngIf="empresa.email">{{ empresa.email.toLowerCase() }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="titulo-pagina">
        <h1>{{ tipoConsulta === 'idRec' ? 'Autorização de Pix Automático' : 'Resumo da sua Autorização de Pix Automático' }}</h1>
      </div>
    </div>

    <!-- Informações de contrato -->
    <div class="secao-card">
      <div class="secao-header">
        <div class="icone">📋</div>
        <h3>Informações de contrato</h3>
      </div>

      <div class="campo-grupo">
        <label>Cliente</label>
        <div class="valor-destaque">{{ pixAutomatico.cliente }}</div>
      </div>

      <div class="campo-grupo">
        <label>CPF:</label>
        <div class="valor">{{ pixAutomatico.clienteIdentificador }}</div>
      </div>

      <div class="campo-grupo">
        <label>Descrição</label>
        <div class="valor">{{ pixAutomatico.descricao }}</div>
      </div>

      <div class="campo-grupo">
        <label>Instituição Bancária</label>
        <div class="valor">{{ pixAutomatico.nomeinstituicaobancaria }}</div>
      </div>

      <div class="campo-grupo">
        <label>Número do contrato</label>
        <div class="valor">{{ pixAutomatico.numeroContrato }}</div>
      </div>

      <div class="campo-grupo" *ngIf="pixAutomatico.externalId">
        <label>ID externo</label>
        <div class="valor">{{ pixAutomatico.externalId }}</div>
      </div>
    </div>

    <!-- Informações de pagamento -->
    <div class="secao-card">
      <div class="secao-header">
        <div class="icone">🔄</div>
        <h3>Informações de pagamento</h3>
      </div>

      <div class="campo-grupo" *ngIf="pixAutomatico.possuiCobrancaImediata">
          <label>Tipo da autorização</label>
          <div class="valor">Pagamento imediato + Autorização</div>
      </div>

      <div class="campo-grupo" *ngIf="!pixAutomatico.possuiCobrancaImediata">
        <label>Tipo da autorização</label>
        <div class="valor">Apenas autorização</div>
      </div>

      <!-- Seção de Pagamento inicial -->
      <div *ngIf="pixAutomatico.possuiCobrancaImediata" class="pagamento-inicial-secao">
        <h3>Pagamento inicial</h3>

        <div class="campo-grupo">
          <label>Valor inicial:</label>
          <div class="valor">{{ pixAutomatico.valorCobrancaImediata }}</div>
        </div>

        <div class="campo-grupo">
          <label>Descrição:</label>
          <div class="valor">Pagamento inicial</div>
        </div>

        <div class="campo-grupo">
          <label>Data do pagamento:</label>
          <div class="valor">Imediato</div>
        </div>
      </div>

      <div class="campo-grupo">
        <label>Método de pagamento</label>
        <div class="valor">{{ pixAutomatico.metodoPagamento }}</div>
      </div>

      <div class="campo-grupo">
        <label>Cobrança</label>
        <div class="valor">{{ pixAutomatico.cobranca }}</div>
      </div>

      <div class="campo-grupo">
        <label>Instituição Bancária</label>
        <div class="valor">{{ pixAutomatico.nomeInstituicaoBancaria }}</div>
      </div>

      <div class="campo-grupo">
        <label>Frequência da cobrança</label>
        <div class="valor">{{ pixAutomatico.frequenciaCobranca }}</div>
      </div>

      <div class="campo-grupo">
        <label>Valor a ser cobrado</label>
        <div class="valor">Entre {{ pixAutomatico.valorMinimo }} e {{ pixAutomatico.valorMaximo }} de acordo com a sua assinatura</div>
      </div>

      <div class="campo-grupo">
        <label>Limite máximo autorizado</label>
        <div class="valor">{{ pixAutomatico.valorMaximo }}</div>

        <!-- Sub informações sobre valor máximo -->
        <div class="sub-informacoes">
          <ul>
            <div *ngIf="pixAutomatico.possuiCobrancaImediata">
            <li>O valor do pagamento inicial (imediato) não está sujeito ao valor máximo definido</li>
            </div>
            <li>Caso o valor da cobrança ultrapasse este valor, o agendamento não será realizado e você será avisado para ajustar o limite ou buscar outra forma de pagamento</li>
            <li>Caso deseje alterar o valor máximo, você pode realizar a alteração na sua instituição</li>
          </ul>
        </div>
      </div>

      <div class="campo-grupo">
        <label>Início da autorização</label>
        <div class="valor">{{ formatarData(pixAutomatico.inicioAutorizacao) }}</div>
      </div>

      <div class="campo-grupo">
        <label>Autorização válida até</label>
        <div class="valor">{{ formatarData(pixAutomatico.autorizacaoValidaAte) }}</div>
      </div>
    </div>

    <!-- Informações do recebedor -->
    <div class="secao-card">
      <div class="secao-header">
        <div class="icone">🏢</div>
        <h3>Informações do recebedor</h3>
      </div>

      <div class="campo-grupo">
        <label>Recebedor</label>
        <div class="valor-destaque">{{ pixAutomatico.recebedor }}</div>
      </div>

      <div class="campo-grupo">
        <label>CNPJ:</label>
        <div class="valor">{{ pixAutomatico.cnpj }}</div>
      </div>

      <div class="campo-grupo">
        <label>Iniciador do pagamento</label>
        <div class="valor">{{ pixAutomatico.iniciadorPagamento }}</div>
      </div>
    </div>

    <!-- Aviso importante -->
    <div class="aviso-importante" *ngIf="tipoConsulta === 'idRec'">
      <p style="font-weight: bold;">
        1-Este link expira em 1 hora.
      </p>
      <p style="font-weight: bold;">
        2-Você precisará ter limites disponíveis, de acordo com as suas configurações do Pix.
      </p>
      <p style="font-weight: bold;">
        3-A cobrança poderá acontecer em um dia diferente do dia do vencimento da autorização
      </p>
      <p style="font-weight: bold;">
        4-Caso o pagamento não seja efetuado na primeira tentativa, novas tentativas poderão ser realizadas dentro de 7 dias.
      </p>
    </div>

    <!-- Botão de autorização (só para consultas por idRec) -->
    <div class="botao-container" *ngIf="tipoConsulta === 'idRec'">
      <button
        class="pacto-btn-primary"
        (click)="autorizarPix()"
        [disabled]="!pixAutomatico || !pixAutomatico.urlRedirect">
        Autorizar cobranças automáticas
      </button>
    </div>

    <!-- Footer informativo -->
    <div class="footer-info" *ngIf="tipoConsulta === 'idRec'">
      <p>
        {{ pixAutomatico.nomeEmpresa }} se conecta à sua conta Nubank usando o SISTEMA PACTO.
        Ao continuar, você aceita os Termos da Política de
        Privacidade da Pacto Soluções.
      </p>
    </div>
  </div>
</div>
