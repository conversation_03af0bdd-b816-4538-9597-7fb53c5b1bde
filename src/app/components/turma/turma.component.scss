@import "../../../assets/css/variaveis";

.container {
  width: 80%;
  margin: auto;
  margin-bottom: 120px;
  margin-bottom: $alturafooter + 30px;
}

h1 {
  font-size: 30px;
  color: #2c343b;
  font-weight: normal;
}

h4 {
  font-size: 16px;
  color: #bdc3c7;
}

.resumo {
  font-size: 24px;
  color: #bdc3c7;
}


.card-disabled {
  cursor: not-allowed;
  color: #666666;
  opacity: 0.5;
}

.card.card-disabled.disabled-red {
  //margin-top: 30px;
}

.schedule-container {
  display: flex;
  justify-content: space-around;

}

.schedule-day {
  box-shadow: 0px 4px 16px 0px #DFDFDF80;
  flex: 1 1 14%;
  padding: 10px;
  box-sizing: border-box;

  .session-card {
    margin-bottom: 10px;
    width: 130px;    
  }

  .day-header {
    font-family: Europa;
    font-size: 12px;
    font-weight: 700;
    line-height: 17px;
    letter-spacing: 0.25px;
    text-align: center;
  }

  .card {
    border: 1px solid #94979E; 
    border-radius: 5px;
    padding: 5px;

    .title {
      font-family: Europa;
      font-size: 16px;
      font-weight: 700;
      line-height: 20px;
      letter-spacing: 0.25px;
      text-align: center;
      color: #51555A;
    }

    .width14 {
      width: 14%;
    }

    .ajusteLine {
      width: 19%;
      margin-top: 3px;
    }

    .subTitle {
      font-family: Europa;
      font-size: 12px;
      font-weight: 700;
      line-height: 14px;
      letter-spacing: 0.25px;
      text-align: left;
      color: #83888F;
      padding: 2px 0;
      max-width: 100%;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .margin-left7 {
      margin-left: 4px !important;
    }

    .interSubtitle {
      font-family: Europa;
      font-size: 12px;
      font-weight: 700;
      line-height: 15px;
      letter-spacing: 0.25px;
      text-align: left;
      color: #80868b;
      max-width: 76%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .custom_icon {
      font-size: 18px
    }

    .h-24 {
      height: 24px;
    }
  }

  .selected {
    @extend .card;
    border: 1px solid #04AF04;

    .custom_icon {
      color: #04AF04;
    }
  }

}

.marginTop10 {
  margin-top: 10px;
}

.cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 10px;
}

@media only screen and (max-width: 748px) {
  .container {
    width: 100%;
  }

  .schedule-container {
    flex-direction: column;
    align-items: center;
  }

  .schedule-day {
    width: 100%;
    margin-bottom: 20px;

    .titleDay {
      margin-bottom: 0;
      h4 {
        margin: 0;
      }
    }
  }

  .session-card {
    width: 100%; 
    margin-bottom: 0 !important;
  }

  .cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
    margin-bottom: 10px;
  }

  .card.cursor-pointer {
    margin-top: 10px;
  }

  .card.card-disabled.disabled-red {
    margin-top: 10px;
  }

  .card.card-disabled {
    margin-top: 10px;
  }

}
