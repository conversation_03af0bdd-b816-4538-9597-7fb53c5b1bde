<div class="container">
  <pacto-header [config]="getConfig()" [unidade]="getUnidadeSelecionada()"></pacto-header>

  <div class="d-flex align-items-center">
    <img class="mr-2 cursor-pointer" src="assets/images/icon/arrow_circle_left.svg" (click)="previousStep()"/>
    <div>
      <p class="title">Selecione um horário  </p>
    </div>
  </div>
  <p class="subtitle mb-4">Qual o período do dia de sua preferência?</p>

    <div class="mt-5" >
      <div class="activity-container">
        <h3 class="title-Card">Quais horário(s) você deseja incluir para a modalidade {{ weekSchedule[currentTurmaIndex]?.descricao }}?</h3>
        <div class="schedule-container mt-2">
          <div *ngFor="let day of weekSchedule[currentTurmaIndex]?.diasSemanaN" class="schedule-day">
            <div class="titleDay">
              <h4 class="day-header">{{ obterDiaSemanaDescricao(day) }}</h4>
            </div>
            <div class="cards">
              <div *ngFor="let session of buscarHorariosDia(day)" class="session-card">
                <div [ngClass]="getClass(session)" (click)="handleClick(session, obterDiaSemanaDescricao(day))">
                  <div class="title">{{ session.horaInicial }}</div>
                  <div class="subTitle" [matTooltip]="session.nomeTurma + ' - ' + session.identificador" matTooltipClass="tooltip-custom">{{session.identificador}}</div>
                  <div>
                    <img class="width14" src="assets/images/icon/pct-bar-chart.svg" alt="">
                    <span class="ml-1 interSubtitle margin-left7">{{session.ocupacao+'/'+session.nrMaximoAluno}}</span>
                  </div>
                  <div class="interSubtitle">
                    <img class="ajusteLine" src="assets/images/icon/pct-time-cap.svg" alt="">
                    <span class="ml-1 interSubtitle">{{getTimeDifference(session.horaInicial, session.horaFinal) + ' min'}}</span>
                  </div>
                  <div class="d-flex justify-content-between align-items-center h-24">
                    <div class="d-flex interSubtitle">
                      <img class="ajusteLine" src="assets/images/icon/pct-user.svg" alt="">
                      <span class="ml-1 interSubtitle marginTop10" [matTooltip]="session.professor" matTooltipClass="tooltip-custom">{{session.professor}}</span>
                    </div>
                    <span *ngIf="isSelected(session)" class="material-symbols-outlined custom_icon notranslate">task_alt</span>
                    <span hidden="true">{{ findDay(session) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  <pacto-footer
    [currentTurmaIndex]="currentTurmaIndex"
    [weekSchedule]="weekSchedule.length"
    [componentSource]="'turma'"
    [vendaProduto]="getProdutosSelecionados().length > 0"
    [vendaPlano]="true"
    [isVisiblededButtonClear]="true"
    [isDisabledButtonCont]="hasTurmaSelected"
    (nextTurma)="nextTurma()"
    (previousTurma)="previousTurma()"
    [vendaComTurma]="true"
    [hasTurma]="true"
    >
  </pacto-footer>
</div>
