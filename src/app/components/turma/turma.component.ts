import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { Config } from '@base-core/empresa/config.model';
import { Empresa } from '@base-core/empresa/empresa.model';
import { EmpresaService } from '@base-core/empresa/empresa.service';
import { Modalidade } from 'src/app/base/model/vendasOnlineV3.model';
import { Location } from '@angular/common';
import { ModalidadeService } from '@base-core/modalidade/modalidade-service';
import { Subscription } from 'rxjs';
import { TurmaService } from '@base-core/turma/turma.service';
import {NascimentoService} from '@base-core/nascimento/nascimento.service';
import {Turma} from '@base-core/turma/turma.model';
import Swal from 'sweetalert2';
import {HorarioTurma} from '@base-core/turma/horarioTurma.model';
import {ModalidadeTurma} from '@base-core/turma/modalidadeTurma.model';
import {ProdutoService} from '@base-core/produto/produto.service';
import {VendaProduto} from '@base-core/produto/produto.model';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'pacto-turma',
  templateUrl: './turma.component.html',
  styleUrls: ['./turma.component.scss']
})
export class TurmaComponent implements OnInit {

  @Output() onContinue: EventEmitter<Function> = new EventEmitter();

  weekSchedule: ModalidadeTurma[];
  selectedSessions: any[] = [];
  selectedModalidades: Modalidade[] = [];
  currentTurmaIndex: number = 0;
  turmasSelecionadas: Turma;
  horariosSelecionados: HorarioTurma[] = [];
  horarioDia: HorarioTurma[] = [];

  private modalidadesSubscription: Subscription;
  constructor(
    private empresaService: EmpresaService,
    private location: Location,
    private modalidadeService: ModalidadeService,
    private nascimentoService: NascimentoService,
    private produtoService: ProdutoService,
    private turmaService: TurmaService
  ) {
    this.montarTratamentoF5(empresaService);
  }

  ngOnInit(): void {
    this.getModalidades();
    this.fetchTurmasForSelectedModalidades();
  }

  montarTratamentoF5(empService) {
    window.addEventListener('keydown', function (e) {
      const code = e.which || e.keyCode;
      if (code == 116) {
        e.preventDefault();
      } else {
        return true;
      }
      Swal.fire({
        type: 'warning',
        text: 'Ao atualizar a página, você será redirecionado para o fluxo inicial de planos.',
        showConfirmButton: true,
        onClose: () => {
          let url = window.location.href.replace('turma', 'planos');
          url += '?un=' + empService.unidadeSelecionada.codigo + '&k=' + empService.unidadeSelecionada.chave;
          console.log(url);
          window.location.href = url;
        }
      });
    });
  }

  private getModalidades(): void {
    this.modalidadesSubscription = this.modalidadeService.modalidadesSelecionadas$
      .subscribe(modalidades => {
        this.selectedModalidades = modalidades.filter(modalidade => (modalidade.utilizarTurma && modalidade.selectedTimesPerWeek != null));
      });
  }

  ngOnDestroy(): void {
    if (this.modalidadesSubscription) {
      this.modalidadesSubscription.unsubscribe();
    }
  }

  fetchTurmasForSelectedModalidades(): void {
    Swal.fire({
      title: "Processando",
      allowOutsideClick: false,
      onOpen: function () {
        Swal.showLoading();
      }
    });
    this.selectedModalidades.sort((a, b) => (a.codigo < b.codigo) ? -1 : 1);
    const modalidadeIds = this.selectedModalidades.map(mod => mod.codigo);
    this.turmaService.obterTurmas(this.empresaService.unidadeSelecionada.chave, modalidadeIds, this.empresaService.unidadeSelecionada.codigo,
      this.nascimentoService.getIdade(), localStorage.getItem('codigoPessoa'))
    .pipe(
      finalize(() => {
        Swal.close();
      })
    ).subscribe(schedule => {
      this.weekSchedule = schedule;
      localStorage.removeItem('codigoPessoa');
    });
  }

  nextTurma(): void {
    if (this.currentTurmaIndex < this.weekSchedule.length - 1) {
      this.currentTurmaIndex++;
    }
  }

  previousTurma(): void {
    if (this.currentTurmaIndex > 0) {
      this.currentTurmaIndex--;
    } else {
      this.previousStep();
    }
  }

  toggleSessionSelection(session: any, day: string): void {
    const currentTurmaSessions = this.selectedSessions[this.currentTurmaIndex] || [];
    const index = currentTurmaSessions.findIndex(s => ((s.session.codigo == session.codigo) && (s.session.turma == session.turma)));

    const currentModalidade = this.selectedModalidades[this.currentTurmaIndex];
    const modalidadeName = currentModalidade.modalidade;

    if (index > -1) {

      currentTurmaSessions.splice(index, 1);
      const indexH = this.horariosSelecionados.findIndex(s => ((s.codigo == session.codigo) && (s.turma == session.turma)));
      this.horariosSelecionados.splice(indexH, 1);
    } else {

      if (currentTurmaSessions.length < currentModalidade.selectedTimesPerWeek) {
        currentTurmaSessions.push({ session, day });
        this.turmasSelecionadas = currentTurmaSessions;
        this.horariosSelecionados.push(session);
      }
    }

    const modalidadeSelecionada = [{
      modalidadeName,
      currentTurmaSessions
    }];
    this.setTurmasSelecionadas(modalidadeSelecionada);
    this.turmaService.atualizarHorariosSelecionados(this.horariosSelecionados);
  }

  setTurmasSelecionadas(modalidadeSelecionada: any): void {
    this.turmaService.atualizarTurmasSelecionadas(modalidadeSelecionada);
  }

  turmaMesmaAnterior(session: any): boolean {
    if (this.empresaService.unidadeSelecionada.habilitarValidacaoHorariosMesmaTurma) {
      if (this.horariosSelecionados.some(
        (objeto) => objeto.turma !== session.turma
      )) {
        Swal.fire({
          type: 'error',
          title: 'Horário!',
          text: 'Só é permitido selecionar horários da mesma turma.',
          showConfirmButton: true,
        });
        return false;
      } else {
        return true;
      }
    }
    return true;
  }

  findDay(session: any): string {
    const found = this.selectedSessions.find(s => (s.session && (s.session.codigo == session.codigo)));
    return found ? found.day : '';
  }


  getConfig(): Config {
    return this.empresaService.config;
  }

  getUnidadeSelecionada(): Empresa {
    return this.empresaService.unidadeSelecionada;
  }

  previousStep(): void {
    this.location.back();
  }

  handleClick(session: any, day: string): void {
    if (session.ocupacao >= session.nrMaximoAluno) {
      Swal.fire({
        type: 'error',
        title: 'Horário lotado!',
        text: 'Este horário já está em sua ocupação máxima, selecione outro.',
        showConfirmButton: true,
      });
    } else {
      if (!this.isCurrentTurmaDisabled || this.isSelected(session)) {
        if (this.turmaMesmaAnterior(session)) {
          this.toggleSessionSelection(session, day);
        }
      }
    }
  }

  getClass(session: any): { [className: string]: boolean } {
    return {
      'selected': this.isSelected(session),
      'card': !this.isSelected(session),
      'card-disabled': this.isCurrentTurmaDisabled && !this.isSelected(session),
      'cursor-pointer': !this.isCurrentTurmaDisabled || this.isSelected(session),
      'disabled-red': (session.ocupacao >= session.nrMaximoAluno)
    };
  }

  get hasTurmaSelected(): boolean {

    if (!this.selectedSessions[this.currentTurmaIndex]) {
      this.selectedSessions[this.currentTurmaIndex] = [];
    }
    const currentTurmaSessions = this.selectedSessions[this.currentTurmaIndex];
    const requiredSessions = this.selectedModalidades[this.currentTurmaIndex].selectedTimesPerWeek || 0;

    return currentTurmaSessions.length < requiredSessions;
  }

  get isSelected(): (session: any) => boolean {
    return (session: any) => this.selectedSessions[this.currentTurmaIndex].some(s => s.session === session);
  }

  get isCurrentTurmaDisabled(): boolean {
    const currentTurmaSessions = this.selectedSessions[this.currentTurmaIndex] || [];
    return currentTurmaSessions.length >= (this.selectedModalidades[this.currentTurmaIndex].selectedTimesPerWeek || 0);
  }

  buscarHorariosDia(diaSemanaNr): HorarioTurma[] {
    this.horarioDia = [];
    this.weekSchedule[this.currentTurmaIndex].turmas.forEach(t => {
      t.diasSemana.forEach(h => {
        if (h.diaSemanaDescricao === '2' && diaSemanaNr === 2 && h.horarios.length > 0) {
          h.horarios.forEach(horario => {
            horario.nomeTurma = t.descricao;
            this.horarioDia.push(horario);
          });
        } else if (h.diaSemanaDescricao === '3' && diaSemanaNr === 3 && h.horarios.length > 0) {
          h.horarios.forEach(horario => {
            horario.nomeTurma = t.descricao;
            this.horarioDia.push(horario);
          });
        } else if (h.diaSemanaDescricao === '4' && diaSemanaNr === 4 && h.horarios.length > 0) {
          h.horarios.forEach(horario => {
            horario.nomeTurma = t.descricao;
            this.horarioDia.push(horario);
          });
        } else if (h.diaSemanaDescricao === '5' && diaSemanaNr === 5 && h.horarios.length > 0) {
          h.horarios.forEach(horario => {
            horario.nomeTurma = t.descricao;
            this.horarioDia.push(horario);
          });
        } else if (h.diaSemanaDescricao === '6' && diaSemanaNr === 6 && h.horarios.length > 0) {
          h.horarios.forEach(horario => {
            horario.nomeTurma = t.descricao;
            this.horarioDia.push(horario);
          });
        } else if (h.diaSemanaDescricao === '7' && diaSemanaNr === 7 && h.horarios.length > 0) {
          h.horarios.forEach(horario => {
            horario.nomeTurma = t.descricao;
            this.horarioDia.push(horario);
          });
        } else if (h.diaSemanaDescricao === '1' && diaSemanaNr === 1 && h.horarios.length > 0) {
          h.horarios.forEach(horario => {
            horario.nomeTurma = t.descricao;
            this.horarioDia.push(horario);
          });
        }
      });
    });
    return this.horarioDia;
  }

  obterDiaSemanaDescricao(diaSemanaNr): string {
    if (diaSemanaNr == 1) {
      return 'Domingo';
    } else if (diaSemanaNr == 2) {
      return 'Segunda-Feira';
    } else if (diaSemanaNr == 3) {
      return 'Terça-Feira';
    } else if (diaSemanaNr == 4) {
      return 'Quarta-Feira';
    } else if (diaSemanaNr == 5) {
      return 'Quinta-Feira';
    } else if (diaSemanaNr == 6) {
      return 'Sexta-Feira';
    } else if (diaSemanaNr == 7) {
      return 'Sabado';
    }
  }

  getTimeDifference(startTime: string, endTime: string): number {
    const [startHours, startMinutes] = startTime.split(':').map(Number);
    const [endHours, endMinutes] = endTime.split(':').map(Number);
    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;
    let difference = endTotalMinutes - startTotalMinutes;
    if (difference < 0) {
      difference += 24 * 60;
    }
    return difference;
  }

  getProdutosSelecionados(): Array<VendaProduto> {
    return this.produtoService.produtosSelecionados;
  }
}
