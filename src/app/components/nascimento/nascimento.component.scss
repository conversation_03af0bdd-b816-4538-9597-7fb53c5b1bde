@import "../../../assets/css/variaveis";

.container {
  width: 80%;
  margin: auto;
}

h1 {
  font-size: 30px;
  color: #2c343b;
  font-weight: normal;
}

h4 {
  font-size: 16px;
  color: #bdc3c7;
}

.resumo {
  font-size: 24px;
  color: #bdc3c7;
}

@media only screen and (max-width: 748px) {
  .container {
    width: 100%;
  }
}



.main {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  /* Isso fará com que um item fique no topo e outro na base */
  width: 100%;
  height: 50vh;

  .text-pacto {
    display: flex;
    white-space: nowrap;
    font-family: Rounded Mplus 1c;
    font-size: 12px;
    line-height: 12px;
    justify-content: center;

  }

  .align-flex{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  ::ng-deep .mat-form-field-appearance-legacy .mat-form-field-underline {
    height: 2px !important;
    background-color: #1998FC !important;
    background-image: none !important;
  }
}
