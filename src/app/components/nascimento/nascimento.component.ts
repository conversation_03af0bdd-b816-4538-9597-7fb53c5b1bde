import { Component, OnInit} from '@angular/core';
import { Config } from '@base-core/empresa/config.model';
import { Empresa } from '@base-core/empresa/empresa.model';
import { Location } from '@angular/common';
import { MAT_DATE_LOCALE } from '@angular/material/core';
import { AbstractControl, FormBuilder, FormGroup, ValidatorFn, Validators } from '@angular/forms';
import * as moment from 'moment';
import {debounceTime, filter} from 'rxjs/operators';
import { NascimentoService } from '@base-core/nascimento/nascimento.service';
import Swal from 'sweetalert2';
import {EmpresaService} from '@base-core/empresa/empresa.service';
import {ActivatedRoute} from '@angular/router';
import {ProdutoService} from '@base-core/produto/produto.service';
import {VendaProduto} from '@base-core/produto/produto.model';

declare var window;

@Component({
  selector: 'pacto-nascimento',
  templateUrl: './nascimento.component.html',
  styleUrls: ['./nascimento.component.scss'],
  providers: [
    { provide: MAT_DATE_LOCALE, useValue: 'en-GB' }
  ]
})
export class NascimentoComponent implements OnInit {

  form: FormGroup;
  age: number;
  maxDate = moment().format('YYYY-MM-DD');
  voltarHabilitado = true;

  constructor(
    private readonly route: ActivatedRoute,
    private empresaService: EmpresaService,
    private location: Location,
    private fb: FormBuilder,
    private produtoService: ProdutoService,
    private nascimentoService: NascimentoService
  ) {
    this.montarTratamentoF5(empresaService);

    if (this.route.snapshot.params['backoff']) {
      this.voltarHabilitado = false;
    }
  }

  ngOnInit() {
    this.buildForm();
  }

  buildForm(): void {
    this.form = this.fb.group({
      birthDate: [null, [, Validators.required, this.dateMaxValidator(this.maxDate)]]
    });
    this.form.get('birthDate').valueChanges.pipe(
      debounceTime(500)
    ).subscribe(date => {
      this.nascimentoService.setDataNascimento(date);
      this.calcAge(date);
    });
  }

  montarTratamentoF5(empService) {
    window.addEventListener('keydown', function (e) {
      const code = e.which || e.keyCode;
      if (code == 116) {
        e.preventDefault();
      } else {
        return true;
      }
      Swal.fire({
        type: 'warning',
        text: 'Ao atualizar a página, você será redirecionado para o fluxo inicial de planos.',
        showConfirmButton: true,
        onClose: () => {
          let url = window.location.href.replace('nascimento', 'planos');
          url += '?un=' + empService.unidadeSelecionada.codigo + '&k=' + empService.unidadeSelecionada.chave;
          console.log(url);
          window.location.href = url;
        }
      });
    });
  }

  dateMaxValidator(maxDate: string): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      const controlValue = control.value
        ? moment(control.value).format('YYYY-MM-DD')
        : null;
      if (controlValue) {
        const forbidden = controlValue > maxDate;
        return forbidden ? {dateMax: {value: control.value}} : null;
      }
      return null;
    };
  }

  calcAge(date: Date): void {
    if (date) {
      const today = moment();
      const birthdate = moment(date);
      this.age = today.diff(birthdate, 'years');
    } else {
      this.age = null;
    }
    this.nascimentoService.setIdade(this.age);
  }

  getConfig(): Config {
    return this.empresaService.config;
  }
  getUnidadeSelecionada(): Empresa {
    return this.empresaService.unidadeSelecionada;
  }

  previousStep(): void {
    this.location.back();
  }

  getProdutosSelecionados(): Array<VendaProduto> {
    return this.produtoService.produtosSelecionados;
  }
}
