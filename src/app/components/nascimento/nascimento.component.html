<div class="container">
  <pacto-header [unidade]="getUnidadeSelecionada()"></pacto-header>
  <div class="d-flex align-items-center">
      <img *ngIf="voltarHabilitado" class="mr-2 cursor-pointer" src="assets/images/icon/arrow_circle_left.svg" (click)="previousStep()"/>
    <div>
      <p class="title">Data de nascimento </p>
    </div>
  </div>
  <p class="subtitle mb-4">Antes de selecionar sua turma, por favor, informe sua data de nascimento.</p>
  <div class="main">
    <div class="col-6 mt-5 mb-5 align-flex">
      <form [formGroup]="form" class="w-100 d-flex justify-content-center">
        <mat-form-field class="w-100">
          <input onfocus="true" matInput type="date" formControlName="birthDate"
            [max]="maxDate" (dateChange)="calcAge($event.value)">
        </mat-form-field>
      </form>
    </div>
  </div>
  <pacto-footer [vendaProduto]="getProdutosSelecionados().length > 0" [vendaPlano]="true" [componentSource]="'nascimento'"
    [isDisabledButtonCont]="form.invalid" [isVisiblededButtonClear]="true">
  </pacto-footer>
</div>
