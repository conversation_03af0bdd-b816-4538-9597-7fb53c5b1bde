@import '../../../assets/css/variaveis';

.caixa-unidade {
  padding: 25px;
  margin-top: 15px;
  background: $branco;
  border: 1px solid rgba(189, 195, 199, 0.5);
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 4px;

  header {
    display: flex;

    .unidade-selecionada {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      padding-left: 0.5rem;

      .se<PERSON>cionou {
        color: rgba(44, 52, 59, 0.5);
        font-size: 1rem;
      }

      .nome-unidade {
        font-size: 1.2rem;
      }
    }
  }

  .endereco {
    margin-top: 24px;
    padding-top: 15px;
    border-top: solid 2px rgba(189, 195, 199, 0.3);
    font-size: 1rem;
    color: rgba(44, 52, 59, 0.5);
  }
}
