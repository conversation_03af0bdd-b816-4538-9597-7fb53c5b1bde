<div *ngIf="unidadeSelecionada" class="caixa-unidade">
  <header>
    <img src="assets/images/icon/icon-map-pin.png" height="40" width="37" />

    <div *ngIf="unidadeSelecionada" class="unidade-selecionada">
      <span class="selecionou">{{ 'unidade-selecionada.voce-selecionou-a' | translate }}</span>
      <span class="nome-unidade" [style.color]="config ? config.cor : 'black'">
        {{ unidadeSelecionada.nome }}
      </span>
    </div>
  </header>

  <div *ngIf="unidadeSelecionada" class="endereco">
    <div>{{ unidadeSelecionada.telefone }}</div>
    <div>{{ unidadeSelecionada.endereco }}</div>
    <div>{{ unidadeSelecionada.complemento }}</div>
    <div>
      {{ unidadeSelecionada.cidade }} - {{ 'checkout.cep' | translate | uppercase }}:
      {{ unidadeSelecionada.cep }}
    </div>
    <div [innerHTML]="quebrarLinhaDeEmail(unidadeSelecionada.email.toLowerCase())"></div>
  </div>
</div>
