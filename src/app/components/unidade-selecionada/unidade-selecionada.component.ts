import { Component, OnInit } from '@angular/core';
import { Config } from '@base-core/empresa/config.model';
import { Empresa } from '@base-core/empresa/empresa.model';
import { EmpresaService } from '@base-core/empresa/empresa.service';

@Component({
  selector: 'pacto-unidade-selecionada',
  templateUrl: './unidade-selecionada.component.html',
  styleUrls: ['./unidade-selecionada.component.scss'],
})
export class UnidadeSelecionadaComponent implements OnInit {
  constructor(private readonly empresaService: EmpresaService) {}

  public ngOnInit(): void {}

  public get unidadeSelecionada(): Empresa {
    return this.empresaService.unidadeSelecionada;
  }

  public get config(): Config {
    return this.empresaService.config;
  }

  public quebrarLinhaDeEmail(texto): string {
    texto = texto.split('.').join('.<wbr>');
    texto = texto.split('-').join('-<wbr>');
    texto = texto.split('_').join('_<wbr>');
    texto = texto.split('@').join('<wbr>@');
    return texto;
  }
}
