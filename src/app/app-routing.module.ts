import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { PlanosComponent } from './components/planos/planos.component';
import { CheckoutComponent } from './components/checkout/checkout.component';
import { PagamentoComponent } from './components/pagamento/pagamento.component';
import { PosPagamentoComponent } from './components/pos-pagamento/pospagamento.component';
import { ProdutosComponent } from './components/produtos/produtos/produtos.component';
import { LojaComponent } from './components/loja/loja/loja.component';
import { CadastroClienteComponent } from './components/checkout/cadastro-cliente/cadastro-cliente/cadastro-cliente.component';
import { AulasComponent } from './components/aulas/aulas.component';
import { EmailOptinComponent } from './components/email-optin/email-optin.component';
import { AgradecimentoComponent } from './components/email-optin/agradecimento/agradecimento.component';
import { UnsubscribeComponent } from './components/email-optin/unsubscribe/unsubscribe.component';
import { DescadastradoOptinComponent } from './components/email-optin/unsubscribe/descadastrado-optin/descadastrado-optin.component';
import { AutorizacoesComponent } from './components/autorizacoes/autorizacoes.component';
import { ModalidadeComponent } from './components/modalidade/modalidade.component';
import { TurmaComponent } from './components/turma/turma.component';
import { NascimentoComponent } from './components/nascimento/nascimento.component';
import { AgendaLocacaoAmbienteComponent } from './components/locacoes/agenda-locacao-ambiente/agenda-locacao-ambiente.component';

const routes: Routes = [{
  path: 'planos',
  component: PlanosComponent
}, {
  path: 'pagamento',
  component: PagamentoComponent
}, {
  path: 'pagamento/:chave/:token',
  component: PagamentoComponent
}, {
  path: 'p/:chave/:token',
  component: PagamentoComponent
}, {
  component: CheckoutComponent,
  path: 'checkout'
}, {
  component: PosPagamentoComponent,
  path: 'pospagamento'
}, {
  component: PosPagamentoComponent,
  path: 'pospagamento/:chave/:token',
}, {
  component: ProdutosComponent,
  path: 'produtos'
}, {
  component: LojaComponent,
  path: 'loja'
}, {
  component: CadastroClienteComponent,
  path: 'cadastro'
}, {
  component: AulasComponent,
  path: 'agenda-aulas'
}, {
  component: EmailOptinComponent,
  path: 'email-optin'
}, {
  component: AgradecimentoComponent,
  path: 'agradecimento-optin'
}, {
  component: UnsubscribeComponent,
  path: 'unsubscribe-optin'
}, {
  component: DescadastradoOptinComponent,
  path: 'descadastrado-optin'
}, {
  component: AutorizacoesComponent,
  path: 'autorizacoes'
}, {
  component: ModalidadeComponent,
  path: 'modalidade'
}, {
  component: NascimentoComponent,
  path: 'nascimento'
}, {
  component: NascimentoComponent,
  path: 'nascimento/:backoff'
}, {
  component: TurmaComponent,
  path: 'turma'
}, {
  component: AgendaLocacaoAmbienteComponent,
  path: 'agenda-locacao-ambiente'
}
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
