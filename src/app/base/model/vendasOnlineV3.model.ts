export interface Modalidade {
  id: number;
  codigo: number;
  modalidade: string;
  description: string;
  selected: boolean;
  utilizarTurma: boolean;
  nrsVezesSemana?: number[];
  notice?: string;
  selectedTimesPerWeek?: number;
  faixa_etaria: number;
  hasVaga: boolean;
  isAgeAppropriate: boolean;
}


// Para manter a descrição junto, você pode criar um mapa ou objeto separado:
export const TimesPerWeekDescriptions: Map<number, string> = new Map([
  [1, '1x por semana'],
  [2, '2x por semana'],
  [3, '3x por semana'],
  [4, '4x por semana'],
  [5, '5x por semana'],
  [6, '6x por semana'],
  [7, '7x por semana']
]);

