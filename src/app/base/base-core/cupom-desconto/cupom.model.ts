import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';
import {PremioCupom} from '@base-core/cupom-desconto/premio-cupom.model';


export class Cupom {
  numeroCupom: string;
  listaPremios: PremioCupom[];


  constructor(numeroCupom: string, listaPremios: PremioCupom[]) {
    this.numeroCupom = numeroCupom;
    this.listaPremios = listaPremios;
  }
}

@Injectable({
  providedIn: 'root'
})
export class CupomAdapter implements Adapter<Cupom> {

  adapt(item: any): Cupom {
    window.localStorage.setItem('ERROCUPOM', item);
    return new Cupom(item.numeroCupom,
      item.listaPremios
    );
  }
}
