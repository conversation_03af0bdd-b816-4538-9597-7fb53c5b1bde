import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';


export class PremioCupom {
  descricaoPlano:string;
  descricaoPremio: string;
  tipoPremio: string;
  percentualDesconto: number;
  valorDesconto: number;



  constructor(descricaoPlano: string,descricaoPremio: string, tipoPremio: string, percentualDesconto: number, valorDesconto: number) {
    this.descricaoPlano = descricaoPlano;
    this.descricaoPremio = descricaoPremio;
    this.tipoPremio = tipoPremio;
    this.percentualDesconto = percentualDesconto;
    this.valorDesconto = valorDesconto;
  }
}

@Injectable({
  providedIn: 'root'
})
export class PremioCupomAdapter implements Adapter<PremioCupom> {

  adapt(item: any): PremioCupom {
    return new PremioCupom(
      item.descricaoPlano,
      item.descricaoPremio,
      item.tipoPremio,
      item.percentualDesconto,
      item.valorDesconto
    );
  }
}
