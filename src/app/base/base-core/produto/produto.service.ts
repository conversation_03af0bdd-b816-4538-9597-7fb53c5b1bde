import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Categoria, CategoriaAdapter, Produto, ProdutoAdapter, VendaProduto } from '@base-core/produto/produto.model';
import { RestService } from '@base-core/rest/rest.service';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import Swal from 'sweetalert2';

@Injectable({
  providedIn: 'root'
})
export class ProdutoService {
  produtosSelecionados: Array<VendaProduto> = [];
  produtos: Array<Produto> = [];
  todosProdutos: Array<Produto> = [];
  categorias: Array<Categoria> = [];
  valorDaTaxa: number = 0;

  constructor(
    private http: HttpClient,
    private adapter: ProdutoAdapter,
    private categoriaAdapter: CategoriaAdapter,
    private restService: RestService
  ) {
  }

  obterProdutos(chave, unidade, categoria): Observable<Array<Produto>> {
    const url = this.restService.buildFullUrl('v2/vendas/' + chave + '/produtos/' + unidade + '/' + categoria);
    return this.http.get(url).pipe(map((response: any) => {
      return response.return.map(item => this.adapter.adapt(item));
    }), catchError(error => {
      console.log(error);
      return of([]);
    }));
  }

  obterProduto(chave, unidade, codigo): Observable<Produto> {
    const url = this.restService.buildFullUrl('v2/vendas/' + chave + '/produto/' + unidade + '/' + codigo);
    return this.http.get(url).pipe(map((response: any) => {
      return response.return;
    }), catchError(error => {
      console.log(error);
      return of([]);
    }));
  }

  obterCategorias(chave, unidade): Observable<Array<Categoria>> {
    const url = this.restService.buildFullUrl('v2/vendas/' + chave + '/categorias/' + unidade);
    return this.http.get(url).pipe(map((response: any) => {
      return response.return.map(item => this.categoriaAdapter.adapt(item));
    }), catchError(error => {
      console.log(error);
      return of([]);
    }));
  }

  obterValorDaTaxa(chave, unidade, numeroDoCartao, numeroDeVezes) {
    const url = this.restService.buildFullUrl(`v2/vendas/${chave}/${unidade}/taxasparcelamento`);
    return this.http.post(url, { numeroCartao: numeroDoCartao, nrVezesDividir: numeroDeVezes }).pipe(
      map((response: any) => {
        return response.return;
      }),
      catchError((error) => {
        console.log(error);
        return of(error);
      }),
    );
  }

  adicionarProduto(produto: Produto, qtd: number) {

    if (produto.posicaoEstoque === '0') {
      Swal.fire({
        type: 'error',
        title: 'Produto sem estoque!',
        text: 'Não há nenhuma unidade no estoque',
        showConfirmButton: true
      });
      return;
    }

    let existe = false;
    let semEstoque = false;
    for (let i = 0; i < this.produtosSelecionados.length; i++) {
      if (this.produtosSelecionados[i].produto === produto.codigo) {
        const qtdNova = (this.produtosSelecionados[i].qtd + 1);
        if (qtdNova > Number(produto.posicaoEstoque)) {
          semEstoque = true;
          break;
        }
        this.produtosSelecionados[i].qtd = qtdNova;
        existe = true;
      }
    }

    if (!existe && (qtd > Number(produto.posicaoEstoque))) {
      semEstoque = true;
    }

    if (semEstoque) {
      Swal.fire({
        type: 'error',
        title: 'A quantidade não está disponível.',
        text: 'Disponível ' + produto.posicaoEstoque + ' unidade(s)',
        showConfirmButton: true
      });
    } else if (!existe) {
      const venda = new VendaProduto(produto.codigo, qtd, produto.valor, produto.descricao, '');
      this.produtosSelecionados.push(venda);
    }
  }

  removerProduto(produto: VendaProduto) {
    for (let i = 0; i < this.produtosSelecionados.length; i++) {
      if (this.produtosSelecionados[i].produto === produto.produto) {
        this.produtosSelecionados.splice(i, 1);
        break;
      }
    }
  }

  alterarQtdProduto(produto: VendaProduto) {
    for (let i = 0; i < this.produtosSelecionados.length; i++) {
      if (this.produtosSelecionados[i].produto === produto.produto) {
        this.produtosSelecionados[i].qtd = produto.qtd;
      }
    }
  }

  getValorTotalProdutosSelecionados(): number {
    let total = 0.0;
    for (let i = 0; i < this.produtosSelecionados.length; i++) {
      total += this.produtosSelecionados[i].getValorTotal();
    }
    return total;
  }

  getTodosProdutos(chave: string, unidade: number): Array<Produto> {
    if (this.todosProdutos.length === 0) {
      this.obterProdutos(chave, unidade, 0).subscribe(data => {
        this.todosProdutos = data;
      });
    }
    return this.todosProdutos;
  }
}
