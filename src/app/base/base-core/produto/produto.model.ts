import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';

export class Produto {
  codigo: number;
  maxDivisao: number;
  descricao: string;
  observacao: string;
  valor: number;
  posicaoEstoque: string;
  imagens: string[];
  tipo: string;
  tipoDescricao: string;

  constructor(codigo: number, maxDivisao: number, descricao: string, observacao: string,
              valor: number, posicaoEstoque: string, imagens: string[], tipo: string, tipoDescricao: string) {
    this.codigo = codigo;
    this.maxDivisao = maxDivisao;
    this.descricao = descricao;
    this.observacao = observacao;
    this.valor = valor;
    this.posicaoEstoque = posicaoEstoque;
    this.imagens = imagens;
    this.tipo = tipo;
    this.tipoDescricao = tipoDescricao;
  }

  getPosicaoAtualEstoque() {
    const atual = this.posicaoEstoque;
    if (atual === 'NAO-CONTROLA-ESTOQUE') {
      return '';
    } else if (atual === '0') {
      return 'Sem estoque';
    } else if (atual === '1') {
      return atual + ' disponível';
    } else {
      return atual + ' disponíveis';
    }
  }

  getEstoqueDisponivel() {
    const atual = this.posicaoEstoque;
    if (atual === 'NAO-CONTROLA-ESTOQUE') {
      return true;
    } else if (atual === '0') {
      return false;
    } else if (atual === '1') {
      return true;
    } else {
      return true;
    }
  }
}

@Injectable({
  providedIn: 'root'
})
export class ProdutoAdapter implements Adapter<Produto> {
  adapt(item: any): Produto {
    return new Produto(
      item.codigo,
      item.maxDivisao,
      item.descricao,
      item.observacao,
      item.valor,
      item.posicaoEstoque,
      item.imagens,
      item.tipo,
      item.tipoDescricao
    );
  }
}

export class VendaProduto {
  produto: number;
  qtd: number;
  valorUnitario: number;
  descricao: string;
  observacao: string;

  constructor(produto: number, qtd: number, valorUnitario: number, descricao: string, observacao: string) {
    this.produto = produto;
    this.qtd = qtd;
    this.valorUnitario = valorUnitario;
    this.descricao = descricao;
    this.observacao = observacao;
  }

  getValorTotal() {
    return this.valorUnitario * this.qtd;
  }
}

export class Categoria {
  codigo: number;
  descricao: string;

  constructor(codigo: number, descricao: string) {
    this.codigo = codigo;
    this.descricao = descricao;
  }
}

@Injectable({
  providedIn: 'root'
})
export class CategoriaAdapter implements Adapter<Categoria> {
  adapt(item: any): Categoria {
    return new Categoria(
      item.codigo,
      item.descricao
    );
  }
}
