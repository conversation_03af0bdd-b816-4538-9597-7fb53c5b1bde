import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';

export class HorarioTurma {
  codigo: number;
  turma: number;
  nomeTurma: string;
  identificador: string;
  horaInicial: string;
  horaFinal: string;
  nrMaximoAluno: number;
  ocupacao: number;
  professor: string;
  ambiente: string;
  nivelTurma: number;
  diaSemana: string;
  diaSemanaNumero: number;

  constructor(codigo: number,
              turma: number,
              nomeTurma: string,
              identificador: string,
              horaInicial: string,
              horaFinal: string,
              nrMaximoAluno: number,
              ocupacao: number,
              professor: string,
              ambiente: string,
              nivelTurma: number,
              diaSemana: string,
              diaSemanaNumero: number) {
    this.codigo = codigo;
    this.turma = turma;
    this.nomeTurma = nomeTurma;
    this.identificador = identificador;
    this.horaInicial = horaInicial;
    this.horaFinal = horaFinal;
    this.nrMaximoAluno = nrMaximoAluno;
    this.ocupacao = ocupacao;
    this.professor = professor;
    this.ambiente = ambiente;
    this.nivelTurma = nivelTurma;
    this.diaSemana = diaSemana;
    this.diaSemanaNumero = diaSemanaNumero;
  }
}

@Injectable({
  providedIn: 'root'
})
export class HorarioTurmaAdapter implements Adapter<HorarioTurma> {

  adapt(item: any): HorarioTurma {
    return new HorarioTurma(
      item.codigo,
      item.turma,
      item.nomeTurma,
      item.identificador,
      item.horaInicial,
      item.horaFinal,
      item.nrMaximoAluno,
      item.ocupacao,
      item.professor,
      item.ambiente,
      item.nivelTurma,
      item.diaSemana,
      item.diaSemanaNumero,
    );
  }
}
