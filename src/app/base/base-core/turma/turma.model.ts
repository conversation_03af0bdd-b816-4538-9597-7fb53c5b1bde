import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';
import {DiasSemana} from '@base-core/turma/diasSemana.model';

export class Turma {
  codigo: number;
  descricao: string;
  diasSemana: DiasSemana[];
  idadeMinima: number;
  idadeMaxima: number;
  modalidade: number;
  modalidadeDesc: string;

  constructor(codigo: number, descricao: string, diasSemana: DiasSemana[], idadeMinima: number, idadeMaxima: number, modalidade: number, modalidadeDesc: string) {
    this.codigo = codigo;
    this.descricao = descricao;
    this.diasSemana = diasSemana;
    this.idadeMinima = idadeMinima;
    this.idadeMaxima = idadeMaxima;
    this.modalidade = modalidade;
    this.modalidadeDesc = modalidadeDesc;
  }
}

@Injectable({
  providedIn: 'root'
})
export class TurmaAdapter implements Adapter<Turma> {

  adapt(item: any): Turma {
    return new Turma(
      item.codigo,
      item.descricao,
      item.diasSemana,
      item.idadeMinima,
      item.idadeMaxima,
      item.modalidade,
      item.modalidadeDesc,
    );
  }
}
