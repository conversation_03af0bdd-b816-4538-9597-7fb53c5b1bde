import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';
import {Turma} from '@base-core/turma/turma.model';

export class ModalidadeTurma {
  codigo: number;
  descricao: string;
  turmas: Turma[];
  diasSemanaN: number[];

  constructor(codigo: number, descricao: string, turmas: Turma[]) {
    this.codigo = codigo;
    this.descricao = descricao;
    this.turmas = turmas;
    this.diasSemanaN = [1, 2, 3, 4, 5, 6, 7];
  }
}

@Injectable({
  providedIn: 'root'
})
export class ModalidadeTurmaAdapter implements Adapter<ModalidadeTurma> {

  adapt(item: any): ModalidadeTurma {
    return new ModalidadeTurma(
      item.codigo,
      item.descricao,
      item.turmas,
    );
  }
}
