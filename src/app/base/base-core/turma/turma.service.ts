import { Injectable } from '@angular/core';
import {BehaviorSubject, Observable} from 'rxjs';
import {HttpClient} from '@angular/common/http';
import {RestService} from '@base-core/rest/rest.service';
import {map} from 'rxjs/operators';
import {HorarioTurma} from '@base-core/turma/horarioTurma.model';
import {ModalidadeTurma, ModalidadeTurmaAdapter} from '@base-core/turma/modalidadeTurma.model';

@Injectable({
  providedIn: 'root'
})

export class TurmaService {

  constructor(
    private http: HttpClient,
    private modalidadeTurmaAdapter: ModalidadeTurmaAdapter,
    private restService: RestService
  ) { }

  private turmaSelecionada = new BehaviorSubject<any[] | null>(null);

  horariosSelecionados: HorarioTurma[] = [];

  turmasSelecionadas$ = this.turmaSelecionada.asObservable();

  atualizarTurmasSelecionadas(turmas: any[]): void {
    this.turmaSelecionada.next(turmas);
  }

  atualizarHorariosSelecionados(horarios: HorarioTurma[]): void {
    this.horariosSelecionados = horarios;
  }

  obterTurmas(chave: string, modalidade: number[], empresa: number, idade: number, codigoPessoa: any): Observable<Array<ModalidadeTurma>> {
    let url = this.restService.buildFullUrl('v2/vendas/' + chave + '/turmas/' + modalidade + '/' + empresa + '/' + idade);
    if (codigoPessoa) {
      url = url.concat('?codigoPessoa=' + codigoPessoa);
    }
    return this.http.get(url, {}).pipe(
      map((data: any) => data.return.map(item => this.modalidadeTurmaAdapter.adapt(item))),
    );
  }
}
