import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';
import {HorarioTurma} from '@base-core/turma/horarioTurma.model';

export class DiasSemana {
  diaSemanaDescricao: string;
  horarios: HorarioTurma[];

  constructor(diaSemanaDescricao: string,
              horarios: HorarioTurma[]) {
    this.diaSemanaDescricao = diaSemanaDescricao;
    this.horarios = horarios;
  }
}

@Injectable({
  providedIn: 'root'
})
export class DiasSemanaAdapter implements Adapter<DiasSemana> {

  adapt(item: any): DiasSemana {
    return new DiasSemana(
      item.diaSemanaDescricao,
      item.horarios,
    );
  }
}
