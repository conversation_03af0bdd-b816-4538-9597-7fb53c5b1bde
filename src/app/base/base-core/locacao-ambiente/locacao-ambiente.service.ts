import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { RestService } from "@base-core/rest/rest.service";
import { Observable } from "rxjs";
import { AgendaDisponibilidade, AgendaDisponibilidadeAdapter } from "./agenda-disponibilidade.model";
import { NegociacaoService } from "@base-core/negociacao/negociacao.service";
import * as moment from "moment";
import { map, switchMap, tap } from "rxjs/operators";
import { ProdutoService } from "@base-core/produto/produto.service";

@Injectable({
  providedIn: 'root'
})
export class LocacaoAmbienteService {
  dia;
  locacoesSelecionadas: Array<AgendaDisponibilidade> = [];

  constructor(
    private http: HttpClient,
    private adapter: AgendaDisponibilidadeAdapter,
    private restService: RestService,
    private negociacaoService: NegociacaoService,
    private produtoService: ProdutoService
  ) { }

  obterLocacoesDisponiveis(codEmpresa, chave): Observable<Array<AgendaDisponibilidade>> {
    return this.negociacaoService.obterURlTw().pipe(
      switchMap(urlTreino => {
        const url = urlTreino + "/prest/locacoes/obterDisponibilidades";
        const params = {
          dia: moment(this.dia).valueOf().toString(),
          empresaId: codEmpresa,
          chave: chave
        };

        return this.http.get<any>(url, {params}).pipe(
          map(data => {
            return data.content.map(item => this.adapter.adapt(item));
          }),
          switchMap(items => {
           return this.processarProdutosLocacoesDisponiveis(items, chave, codEmpresa);
          })
        )
      })

    )

  }

  private processarProdutosLocacoesDisponiveis(lstAgendaDisponibilidades: Array<AgendaDisponibilidade>, chave, codEmpresa): Observable<Array<AgendaDisponibilidade>> {
    return this.produtoService.obterProdutos(chave, codEmpresa, 0).pipe(
      map(produtos => {

        if(lstAgendaDisponibilidades && lstAgendaDisponibilidades.length > 0 && produtos && produtos.length > 0) {
          lstAgendaDisponibilidades.forEach(agendaDisponibilidade => {
            if(agendaDisponibilidade.lstProdutos && agendaDisponibilidade.lstProdutos.length > 0) {
              agendaDisponibilidade.lstProdutos.forEach(produtoAgendaDisponibilidade => {
                for(const produto of produtos) {
                  if(produtoAgendaDisponibilidade.produto.codigo === produto.codigo) {
                    produtoAgendaDisponibilidade.produto = produto;
                    break;
                  }
                }
              })
            }
          })
        }

        if(lstAgendaDisponibilidades && lstAgendaDisponibilidades.length > 0) {
          lstAgendaDisponibilidades.forEach(agendaDisponibilidade => {
            if(agendaDisponibilidade.produtoLocacao && agendaDisponibilidade.produtoLocacao.codigo) {
              for(const produto of produtos) {
                if(agendaDisponibilidade.produtoLocacao.codigo === produto.codigo) {
                  agendaDisponibilidade.produtoLocacao = produto;
                  break;
                }
              }
            }
          })
        }

        return lstAgendaDisponibilidades;
    }))
  }

}
