import { Adapter } from "@base-core/adaper.js";
import { ColaboradorSimples } from "./colaborador-simples.model";
import { TipoAtividade } from "./tipo-atividade.model";
import { Injectable } from "@angular/core";
import { LocacaoProdutoSugerido } from "./locacao-produto-sugerido.model";
import {Produto} from '@base-core/produto/produto.model';

export class AgendaDisponibilidade {
    id!: number;
    dia!: string;
    professor!: ColaboradorSimples;
    horarioInicial!: string;
    horarioFinal!: string;
    inicioDate!: Date;
    fimDate!: Date;
    agendaDisponibilidadeConfigId!: number;
    tiposAtividades!: TipoAtividade[];
    ambiente!: string;
    ambienteId!: number;
    nome!: string;
    tipoHorario!: string;
    produtoLocacao!: Produto;
    horarioDisponibilidadeCod!: number;
    bloqueado: boolean = false;
    codigoZwAmbiente!: number;
    fotoKey!: string;
    urlFoto!: string;
    descricao!: string;
    lstProdutos!: LocacaoProdutoSugerido[];
    duracaoMinutos!: number;
    tempoMinimoMinutos!: number;


  constructor(id: number, dia: string, professor: ColaboradorSimples, horarioInicial: string, horarioFinal: string, inicioDate: Date, fimDate: Date,
    agendaDisponibilidadeConfigId: number, tiposAtividades: TipoAtividade[], ambiente: string, ambienteId: number, nome: string, tipoHorario: string, produtoLocacao: Produto, horarioDisponibilidadeCod: number,
    bloqueado: boolean, codigoZwAmbiente: number, fotoKey: string, urlFoto: string, descricao: string, lstProdutos: LocacaoProdutoSugerido[],
    duracaoMinutos: number, tempoMinimoMinutos: number) {
      this.id = id;
      this.dia = dia;
      this.professor = professor;
      this.horarioInicial = horarioInicial;
      this.horarioFinal = horarioFinal;
      this.inicioDate = inicioDate;
      this.fimDate = fimDate;
      this.agendaDisponibilidadeConfigId = agendaDisponibilidadeConfigId;
      this.tiposAtividades = tiposAtividades;
      this.ambiente = ambiente;
      this.ambienteId = ambienteId;
      this.nome = nome;
      this.tipoHorario = tipoHorario;
      this.produtoLocacao = produtoLocacao;
      this.horarioDisponibilidadeCod = horarioDisponibilidadeCod;
      this.bloqueado = bloqueado;
      this.codigoZwAmbiente = codigoZwAmbiente;
      this.fotoKey = fotoKey;
      this.urlFoto = urlFoto;
      this.descricao = descricao;
      this.lstProdutos = lstProdutos;
      this.duracaoMinutos = duracaoMinutos;
      this.tempoMinimoMinutos = tempoMinimoMinutos;
    }

    get totalValorProdutos(): number {
      if(!this.lstProdutos || this.lstProdutos.length === 0) {
        return 0;
      }

      return this.lstProdutos.reduce((total, produto) => {
        const valorUnitario = produto.valor || 0;
        const quantidade = produto.quantidade || 1;
        return total + (valorUnitario * quantidade);
      }, 0);
    }

     get totalValorProdutosObrigatorios(): number {
        if(!this.lstProdutos || this.lstProdutos.length === 0) {
          return 0;
        }

        return this.lstProdutos.filter(produto => produto.obrigatorio).reduce((total, produto) => {
          const valorUnitario = produto.valor || 0;
          const quantidade = produto.quantidade || 1;
          return total + (valorUnitario * quantidade);
        }, 0);

      }

}

@Injectable({
  providedIn: 'root'
})
export class AgendaDisponibilidadeAdapter implements Adapter<AgendaDisponibilidade> {

  adapt(item: any): AgendaDisponibilidade {
    return new AgendaDisponibilidade(
      item.id,
      item.dia,
      item.professor,
      item.horarioInicial,
      item.horarioFinal,
      item.inicioDate,
      item.fimDate,
      item.agendaDisponibilidadeConfigId,
      item.tiposAtividades,
      item.ambiente,
      item.ambienteId,
      item.nome,
      item.tipoHorario,
      item.produtoLocacao,
      item.horarioDisponibilidadeCod,
      item.bloqueado,
      item.codigoZwAmbiente,
      item.fotoKey,
      item.urlFoto,
      item.descricao,
      item.lstProdutos,
      item.duracaoMinutos,
      item.tempoMinimoMinutos
    );
  }
}
