import { Produto } from "@base-core/produto/produto.model";

export class LocacaoProdutoSugerido {
    codigo!: number;
    produto!: Produto;
    valor!: number;
    obrigatorio!: boolean;
    quantidade!: number;

    constructor(codigo: number, produto: Produto, valor: number, obrigatorio: boolean, quantidade: number) {
        this.codigo = codigo;
        this.produto = produto;
        this.valor = valor;
        this.obrigatorio = obrigatorio;
        this.quantidade = quantidade;
    }
}
