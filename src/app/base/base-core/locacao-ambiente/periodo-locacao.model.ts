import { AgendaDisponibilidade } from "./agenda-disponibilidade.model";

export class PeriodoLocacao {
    periodo: string;
    lstAgendaDisponibilidade: Array<AgendaDisponibilidade>;

    constructor(periodo: string, lstAgendaDisponibilidade: Array<AgendaDisponibilidade>) {
        if(window.localStorage.getItem('usarSistemaInternacional') == 'true'){
            switch (periodo) {
              case ("Manhã"):
                this.periodo = "Morning"
                break;
              case ("Tarde"):
                this.periodo = "Evening"
                break;
              case ("Noite"):
                this.periodo = "At night"
                break;

              default:
                this.periodo = periodo
                break;
            }
          }else{
            this.periodo = periodo
          }

          this.lstAgendaDisponibilidade = lstAgendaDisponibilidade;
        }
}
