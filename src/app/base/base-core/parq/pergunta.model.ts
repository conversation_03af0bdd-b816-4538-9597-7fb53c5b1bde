import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';
import {Resposta} from '@base-core/parq/resposta.model';

export class Pergunta {
  codigo: number;
  descricao: string;
  respostas: Array<Resposta>;
  tipoPergunta: string;

  constructor(codigo: number, descricao: string, respostas: Resposta[], tipoPergunta: string) {
    this.codigo = codigo;
    this.descricao = descricao;
    this.respostas = respostas;
    this.tipoPergunta = tipoPergunta;
  }
}

@Injectable({
  providedIn: 'root'
})
export class PerguntaAdapter implements Adapter<Pergunta> {

  adapt(item: any): Pergunta {
    return new Pergunta(
      item.codigo,
      item.descricao,
      item.respostas,
      item.tipoPergunta
    );
  }
}
