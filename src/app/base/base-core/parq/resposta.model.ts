import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';

export class Resposta {
  codigo: number;
  descricao: string;
  value: string;

  constructor(codigo: number, descricao: string, value: string) {
    this.codigo = codigo;
    this.descricao = descricao;
    this.value = value;
  }
}

@Injectable({
  providedIn: 'root'
})
export class PerguntaAdapter implements Adapter<Resposta> {

  adapt(item: any): Resposta {
    return new Resposta(
      item.codigo,
      item.descricao,
      item.value
    );
  }
}
