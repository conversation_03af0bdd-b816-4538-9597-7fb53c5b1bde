import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';
import {Pergunta} from '@base-core/parq/pergunta.model';

export class Parq {
  codigo: number;
  descricao: string;
  perguntas: Array<Pergunta>;
  tipoQuestionario: string;

  constructor(codigo: number, descricao: string, perguntas: Pergunta[], tipoQuestionario: string) {
    this.codigo = codigo;
    this.descricao = descricao;
    this.perguntas = perguntas;
    this.tipoQuestionario = tipoQuestionario;
  }
}

@Injectable({
  providedIn: 'root'
})
export class ParqAdapter implements Adapter<Parq> {

  adapt(item: any): Parq {
    return new Parq(
      item.codigo,
      item.descricao,
      item.perguntas,
      item.tipoQuestionario
    );
  }
}
