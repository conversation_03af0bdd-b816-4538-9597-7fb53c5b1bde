import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class RestService {

  constructor(
    private http: HttpClient
  ) {}

  /**
   * Builds the full url, for example:
   *
   * 'resource/34' => 'http://localhost:4200/resource/34'
   *
   * In the case of unsecured routes such as 'login' the
   * fragment 'psec' is not present.
   */
  buildFullUrl(route: string): string {
    const apiUrl = environment.urlApi;
    return `${apiUrl}/${route}`;
  }

}


