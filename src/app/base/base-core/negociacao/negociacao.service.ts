import { Injectable } from '@angular/core';
import {Observable} from 'rxjs/index';
import {RestService} from '@base-core/rest/rest.service';
import {HttpClient} from '@angular/common/http';
import {Config, ConfigAdapter} from '@base-core/empresa/config.model';
import {catchError, map} from 'rxjs/operators';
import {Cupom, CupomAdapter} from '@base-core/cupom-desconto/cupom.model';
import {ProdutoService} from '@base-core/produto/produto.service';
import {PlanoService} from '@base-core/plano/plano.service';
import {Parcela} from '@base-core/negociacao/parcela.model';
import {DadosPagina, PaginaICV} from '@base-core/negociacao/acesso-pagina';
import {Parq} from '@base-core/parq/parq.model';
import {Plano} from '@base-core/plano/plano.model';

@Injectable({
  providedIn: 'root'
})
export class NegociacaoService {
  rede;
  aceito = false;
  matricula;
  chave;
  codunidade;
  valorFinalContrato;
  parcelas: Array<Parcela> = [];
  valorPrimeiraParcela;
  valorProRata;
  cupom: Cupom;
  parq: Parq;
  apresentarLeiParq: boolean = false;
  cobrarParcelasEmAberto = true;
  codigoPix: number = 0;
  descricaoCobrancaPrimeiraParcela;
  anoCobrancaAnuidade: number;
  categoriaPlano: number;
  categoriaProdutos: number;
  origemCobranca: number = 0;
  cobrancaAntecipada: number = 0;
  pactoPayComunicacao: number = 0;
  token: string;
  responsavel: number;
  codigoEvento:number;
  usuarioResponsavel: number = 0;
  codigoRegistroAcessoPagina: number = 0;
  temPlano: Plano;
  temProduto = false;
  lancarContratoConcomitanteAoInvesDeRenovar: boolean;
  utm_data: string;
  menorIdade: boolean;
  public tipoDocumento = 'CPF';
  todasEmAberto: boolean;
  parcelasSelecionadas: string;
  siglaEstadoLeiParq = '';


  constructor(private rest: RestService,
              private http: HttpClient,
              private planoService: PlanoService,
              private produtoService: ProdutoService,
              private cadapter: CupomAdapter) { }

  gravarVenda(venda, captcha): Observable<Object> {
    const url = this.rest.buildFullUrl('v2/vendas/' + this.chave + '/alunovendaonline/' + captcha);
    return this.http.post(url, venda);
  }

  gravarVendaPix(venda, captcha): Observable<Object> {
    const url = this.rest.buildFullUrl('v2/vendas/' + this.chave + '/alunovendaonlinepix/' + captcha);
    return this.http.post(url, venda);
  }

  simularVenda(venda, unidade): Observable<Object> {
    const url = this.rest.buildFullUrl('v2/vendas/' + this.chave + '/simularV2/' + unidade);
    return this.http.post(url, venda);
  }

  cobrarParcela(venda): Observable<Object> {
    const url = this.rest.buildFullUrl('v2/vendas/' + this.chave + '/cobrarparcelasabertas/' + this.matricula);
    return this.http.post(url, venda);
  }

  cobrarParcelaPix(venda): Observable<Object> {
    const url = this.rest.buildFullUrl('v2/vendas/' + this.chave + '/cobrarParcelasAbertasPix/' + this.matricula );
    return this.http.post(url, venda);
  }
  consulatarParcelaPix(): Observable<Object> {
    const url = this.rest.buildFullUrl('v2/vendas/' + this.chave + '/consultarParcelasPix/' + this.codigoPix);
    return this.http.post(url, null);
  }
  cobrarParcelaBoleto(venda): Observable<Object> {
    const url = this.rest.buildFullUrl('v2/vendas/' + this.chave + '/cobrarParcelasAbertasBoleto/' + this.matricula );
    console.log(url);
    console.log(venda);
    return this.http.post(url, venda);
  }

  gravarVendaBoleto(venda, captcha): Observable<Object> {
    const url = this.rest.buildFullUrl('v2/vendas/' + this.chave + '/alunovendaonlineboleto/' + captcha);
    return this.http.post(url, venda);
  }

  isExibirCampoPessoaCompartilhamento(campo: string, config: Config): boolean {
    return config && config.camposAdicionaisProduto.indexOf(campo) > -1;
  }

  validar(venda, config): string {

    //////////////////////////////// VALIDANDO DEPENDENTES ////////////////////////////
    let dependentesValidados = true;

    const dependentes = venda.clientesCadastradosComoDependentesPlanoCompartilhado;

    const camposObrigatoriosMsg = window.localStorage.getItem('usarSistemaInternacional') === 'false'
      ? 'Todos os campos dos dependentes são obrigatórios!'
      : 'All dependent fields required';

    if (dependentes && dependentes.length >= 1) {
      for (let i = 0; i < dependentes.length; i++) {
        const cliente = dependentes[i];

        const nomeInvalido = !cliente.nome || cliente.nome.trim().length === 0;
        const cpfInvalido = !cliente.cpf || cliente.cpf.trim().length < 11;
        const emailInvalido = !cliente.email || cliente.email.trim().length === 0;
        const cpfPaiInvalido = this.isExibirCampoPessoaCompartilhamento(String('CPF_RESPONSAVEL_PAI'), config) && !cliente.cpfPai;
        const cpfMaeInvalido = this.isExibirCampoPessoaCompartilhamento(String('CPF_RESPONSAVEL_MAE'), config) && !cliente.cpfMae;
        const nomePaiInvalido = this.isExibirCampoPessoaCompartilhamento(String('RESPONSAVEL_PAI'), config) && !cliente.responsavelPai;
        const nomeMaeInvalido = this.isExibirCampoPessoaCompartilhamento(String('RESPONSAVEL_MAE'), config) && !cliente.responsavelMae;
        const sexoInvalido = this.isExibirCampoPessoaCompartilhamento(String('SEXO'), config) && !cliente.sexo;
        const dtNascInvalido = this.isExibirCampoPessoaCompartilhamento(String('DT_NASCIMENTO'), config) && !cliente.dataNascimento;
        const telInvalido = this.isExibirCampoPessoaCompartilhamento(String('TELEFONE'), config) && !cliente.telefone;
        const cepInvalido = this.isExibirCampoPessoaCompartilhamento(String('CEP'), config) && !cliente.cep;
        const enderecoInvalido = this.isExibirCampoPessoaCompartilhamento(String('ENDERECO'), config) && !cliente.endereco;
        const numeroInvalido = this.isExibirCampoPessoaCompartilhamento(String('NUMERO'), config) && !cliente.numero;
        const bairroInvalido = this.isExibirCampoPessoaCompartilhamento(String('BAIRRO'), config) && !cliente.bairro;
        const complementoInvalido = this.isExibirCampoPessoaCompartilhamento(String('COMPLEMENTO'), config) && !cliente.complemento;
        const rgInvalido = this.isExibirCampoPessoaCompartilhamento(String('RG'), config) && !cliente.rg;

        if (nomeInvalido || cpfInvalido || emailInvalido || rgInvalido || cpfPaiInvalido || cpfMaeInvalido
          || nomePaiInvalido || nomeMaeInvalido || sexoInvalido || dtNascInvalido || telInvalido
          || cepInvalido || enderecoInvalido || numeroInvalido || bairroInvalido || complementoInvalido) {
          dependentesValidados = false;
        }
      }
    }

    if (!dependentesValidados) {
      return camposObrigatoriosMsg;
    }
    ////////////////////// FIM VALIDANDO DEPENDENTES ////////////////////

    let campos;
    if (this.existePlanoSelecionado && this.existeProdutoSelecionado) {
      campos = config.camposAdicionaisProdutoPlano;
    } else if (this.existePlanoSelecionado) {
      campos = config.camposAdicionais;
    } else {
      campos = config.camposAdicionaisProduto; // cadastro visitante ou venda produto
    }
    if (this.planoService && this.planoService.planoSelecionado &&
      ((this.planoService.planoSelecionado.qtdCreditoPlanoCredito !== null &&
      this.planoService.planoSelecionado.qtdCreditoPlanoCredito !== undefined &&
      this.planoService.planoSelecionado.qtdCreditoPlanoCredito > 0) || !this.planoService.planoSelecionado.regimeRecorrencia)) {
      const indexRemove = campos.findIndex(c => c === 'INICIO_CONTRATO');
      if (indexRemove > -1) {
        campos.splice(indexRemove, 1);
      }
    }
    if ( !campos ) {
      campos = [];
    } else {
      for (let c of campos) {
        let humanFieldName = '';
        const nomesDosAtributos = Reflect.ownKeys(venda);
        for (const v of nomesDosAtributos) {
          switch (c) {
            case 'CPF_RESPONSAVEL_PAI':
              c = 'cpfPai';
              humanFieldName = 'CPF do Pai';
              break;
            case 'CPF_RESPONSAVEL_MAE':
              c = 'cpfMae';
              humanFieldName = 'CPF da Mãe';
              break;
            case 'RESPONSAVEL_PAI':
              c = 'responsavelPai';
              humanFieldName = 'Nome do Pai';
              break;
            case 'RESPONSAVEL_MAE':
              c = 'responsavelMae';
              humanFieldName = 'Nome da Mãe';
              break;
            case 'DIA_VENCIMENTO':
              c = 'diaVencimento';
              humanFieldName = 'Dia de Vencimento';
              break;
            //Hibrael - 12/02/2025 - Comentado pois este campo nao estava sendo utilizado, e essa validacao estava impedindo que
            //compras fossem finalizadas pelo vendas online, ja que estava exigindo o preenchimento do campo
              //case 'VENCIMENTO_FATURA':
              //c = 'vencimentoFatura';
              //humanFieldName = 'Vencimento da Fatura';
              //break;
            case 'INICIO_CONTRATO':
              c = 'dataInicioContrato';
              humanFieldName = 'Início do Contrato';
              break;
            case 'DT_NASCIMENTO':
              c = 'dataNascimento';
              humanFieldName = 'Data de Nascimento';
              break;
          }

          if (this.menorIdade) {
            if (!venda.cpfPai && !venda.responsavelPai && !venda.cpfMae && !venda.responsavelMae) {
              return window.localStorage.getItem('usarSistemaInternacional') === 'false' ?
                'Informe o Nome e o CPF de pelo menos um responsável!' :
                'Provide the details of at least one person responsible';
            } else if (venda.cpfPai && !venda.responsavelPai) {
              return window.localStorage.getItem('usarSistemaInternacional') === 'false' ?
                'Informe o Nome do Pai responsável!' :
                'Provide the details of at least one person responsible';
            } else if (!venda.cpfPai && venda.responsavelPai) {
              return window.localStorage.getItem('usarSistemaInternacional') === 'false' ?
                'Informe o CPF do Pai responsável!' :
                'Provide the details of at least one person responsible';
            } else if (venda.cpfMae && !venda.responsavelMae) {
              return window.localStorage.getItem('usarSistemaInternacional') === 'false' ?
                'Informe o Nome da Mãe responsável!' :
                'Provide the details of at least one person responsible';
            } else if (!venda.cpfMae && venda.responsavelMae) {
              return window.localStorage.getItem('usarSistemaInternacional') === 'false' ?
                'Informe o CPF da Mãe responsável!' :
                'Provide the details of at least one person responsible';
            }
          }

          const nomeCampoEmCaixaBaixa = c.toLowerCase();
          const nomeAtributoCaixaBaixa = v.toString().toLowerCase();
          const ignorarValidacaoDoCampoParaMenorIdade = nomeCampoEmCaixaBaixa === 'rg' && this.menorIdade;

          if (nomeAtributoCaixaBaixa === nomeCampoEmCaixaBaixa && !ignorarValidacaoDoCampoParaMenorIdade) {
            if (!venda[v]) {
              const sistemaInternacional = window.localStorage.getItem('usarSistemaInternacional') === 'true';
              if (sistemaInternacional && c.toLowerCase() === 'cpf') {
                continue;
              }

              const emptyField = humanFieldName || c;
              return sistemaInternacional
                ? `All fields required! Please fill the field ${emptyField}`
                : `Todos os campos são obrigatórios! Preencha o campo ${emptyField}`;
            }
          }
        }
      }
    }

    const sistemaInternacional = window.localStorage.getItem('usarSistemaInternacional') === 'true';

    if ( !venda.nome
      || (this.tipoDocumento === 'CPF' && !this.menorIdade && !sistemaInternacional && !venda.cpf)
          // menor de idade pode ficar vazio pois é obrigatório pelo menos o do responsável
      || (this.tipoDocumento === 'CNPJ' && !this.menorIdade && !sistemaInternacional && !venda.cnpj)
          // menor de idade pode ficar vazio pois é obrigatório pelo menos o do responsável
      || (this.isExibirCampo('DT_NASCIMENTO', config) && !venda.dataNascimento)
      || (this.isExibirCampo('SEXO', config) && !venda.sexo)
      || !venda.email
      || !venda.numeroCartao
      || !venda.nomeCartao
      || !venda.cvv
      || !venda.validade
      || (this.isExibirCampo('TELEFONE', config) && !venda.telefone)
      || (this.isExibirCampo('CEP', config) && !venda.cep)
      || (this.isExibirCampo('ENDERECO', config) && !venda.endereco)
      || (this.isExibirCampo('DIA_VENCIMENTO', config) && !venda.diaVencimento && venda.plano != 0)
      || (this.isExibirCampo('RG', config) && !venda.rg && !this.menorIdade)) {

      const requiredFields = [
        { field: 'nome', label: 'nome' },
        { field: 'cpf', label: 'CPF', condition: this.tipoDocumento === 'CPF' && !this.menorIdade && !sistemaInternacional},
        { field: 'cnpj', label: 'CNPJ', condition: this.tipoDocumento === 'CNPJ' && !this.menorIdade && !sistemaInternacional},
        { field: 'dataNascimento', label: 'data de nascimento', condition: this.isExibirCampo('DT_NASCIMENTO', config) },
        { field: 'sexo', label: 'sexo', condition: this.isExibirCampo('SEXO', config) },
        { field: 'email', label: 'email' },
        { field: 'numeroCartao', label: 'número do cartão' },
        { field: 'nomeCartao', label: 'nome do cartão' },
        { field: 'cvv', label: 'CVV' },
        { field: 'validade', label: 'validade' },
        { field: 'telefone', label: 'telefone', condition: this.isExibirCampo('TELEFONE', config) },
        { field: 'cep', label: 'CEP', condition: this.isExibirCampo('CEP', config) },
        { field: 'endereco', label: 'endereço', condition: this.isExibirCampo('ENDERECO', config) },
        { field: 'diaVencimento', label: 'dia de vencimento', condition: this.isExibirCampo('DIA_VENCIMENTO', config) && venda.plano != 0 },
        { field: 'rg', label: 'RG', condition: this.isExibirCampo('RG', config) && !this.menorIdade }
      ];

      let missingField = '';
      for (const { field, label, condition } of requiredFields) {
        if ((condition === undefined || condition) && !venda[field]) {
          missingField = label;
          break;
        }
      }

      if (!(sistemaInternacional && missingField.toLowerCase() === 'cpf')) {
        return sistemaInternacional ?
          `All fields required! Please fill the field ${missingField}` :
          `Todos os campos são obrigatórios! Preencha o campo ${missingField}`;
      }
    }
    if ( !this.aceito && this.planoService.planoSelecionado) {
      return window.localStorage.getItem('usarSistemaInternacional') === 'false' ? 'Aceite os termos de uso!' : 'accept the terms of use';
    }
    return null;
  }

  isExibirCampo(campo: string, config: Config): boolean {
    if (this.existePlanoSelecionado && this.existeProdutoSelecionado) {
      return config.camposAdicionaisProdutoPlano.indexOf(campo) > -1;
    }
    if (this.existePlanoSelecionado) {
      return config && config.camposAdicionais.indexOf(campo) > -1;
    } else if (campo === 'ParQ') {
      return false;
    }
    // Cadastro Visitante ou Venda de produto
    return config && config.camposAdicionaisProduto.indexOf(campo) > -1;
  }

  validarCupom(numeroCupom, plano): Observable<Cupom> {
    const url = this.rest.buildFullUrl('v2/vendas/' + this.chave + '/adicionarCupomDescontoSite?numeroCupomDesconto=' + encodeURIComponent(numeroCupom));
    return this.http.post(url, plano).pipe(
      map((data: any) => this.cadapter.adapt(data.return ? JSON.parse(data.return) : data.erro)),
    );
  }

  getQtdItensCarrinho() {
    let qtdProdutos = 0;
    for (let i = 0; i < this.produtoService.produtosSelecionados.length; i++) {
      qtdProdutos += this.produtoService.produtosSelecionados[i].qtd;
    }

    if (this.planoService.planoSelecionado) {
      qtdProdutos += 1;
    }
    return qtdProdutos;
  }

  registrarAcessoPagina(tela: string, link: string): void {
    const paginaICV = this.pesquisarPaginaICV(tela, link);
    if (paginaICV == null) {
      this.registrarAcessoPaginaPost(tela, link).subscribe({
        next: (data) => {
          if (data.hasOwnProperty('return')) {
            this.codigoRegistroAcessoPagina = JSON.parse(data['return']).codigo;
            this.gravarPaginaIcvSession(JSON.parse(data['return']));
          } else {
            console.log('erro ao registrarInicioAcessoPagina:', data['erro'] );
          }
        },
        error: (prErro) => {
          console.log('erro ao registrarInicioAcessoPagina:', prErro);
        },
        complete: () => {}
      });
    }

  }

  pesquisarPaginaICV(tela: string, link: string) {
    const identificador = `${tela}-${link}`;
    const mapaJson = sessionStorage.getItem('mapaPaginaICV');
    if (mapaJson != null) {
      const mapaTela = new Map(JSON.parse(mapaJson));
      const paginaICV = mapaTela.get(identificador) as PaginaICV;
      if (paginaICV != null) {
        this.codigoRegistroAcessoPagina = paginaICV.codigo;
      }
      return paginaICV;
    }
    return null;
  }

  gravarPaginaIcvSession(paginaICV: PaginaICV) {
    const mapaJson = sessionStorage.getItem('mapaPaginaICV');
    let mapaTela = new Map<string, PaginaICV>();
    if (mapaJson != null) {
      mapaTela = new Map(JSON.parse(mapaJson));
    }
    const identificador = `${paginaICV.tela}-${paginaICV.link}`;
    mapaTela.set(identificador, paginaICV);
    sessionStorage.setItem('mapaPaginaICV', JSON.stringify(Array.from(mapaTela.entries())));
  }


  registrarAcessoPaginaPost(tela: string, link: string): Observable<Object> {
    const url = this.rest.buildFullUrl(`v2/vendasonlineicv/${this.chave}/registrarInicioAcessoPagina/`);
    const dadosPagina: DadosPagina = {
      tela: tela,
      link: link,
      empresa: this.codunidade,
      evento: this.codigoEvento,
      usuario: this.usuarioResponsavel
    };
    return this.http.post(url, dadosPagina);
  }

  obterURlTw(): Observable<string> {
    const url = this.rest.buildFullUrl('treino/' + this.chave + '/urltreino');
    return this.http.get(url).pipe(map((data: any) => {
        return data.return;
      }),
    );
  }

  obterFormularioParq(linkTw, unidade): Observable<any> {
    return this.http.post(linkTw + '/prest' + '/avaliacao/' + this.chave + '/parq?' + 'empresa=' + unidade, '').pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => {
        console.log(error);
        return null;
      }),
    );
  }

  povoarJsonRespostaParq(utilizaParq: boolean): string {
    const json = new Map<string, string>();
    if (utilizaParq) {
      this.parq.perguntas.forEach(p => {
        p.respostas.forEach(r => {
          if (!(r.value === null || r.value === undefined)) {
            const key = 'resp' + p.codigo + '-' + r.codigo;
            json.set(key, r.value);
          }
        });
      });
      return JSON.stringify(
        Array.from(
          json.entries()).reduce((o, [key, value]) => {
          o[key] = value;
          return o;
        }, {})
      );
    } else {
      return '';
    }
  }

  get existePlanoSelecionado(): boolean {
    return this.planoService.planoSelecionado && this.planoService.planoSelecionado.codigo > 0;
  }

  get planoSelecionadoComTurma(): boolean {
    return this.planoService.planoSelecionado && this.planoService.planoSelecionado.codigo > 0 &&
      this.planoService.planoSelecionado.vendaComTurma;
  }

  get existeProdutoSelecionado(): boolean {
    return this.produtoService.produtosSelecionados && this.produtoService.produtosSelecionados.length > 0;
  }
}
