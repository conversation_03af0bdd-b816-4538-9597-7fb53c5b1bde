import {VendaProduto} from '@base-core/produto/produto.model';
import {Cliente} from '@base-core/cliente/cliente.model';
import { OrigemSistemaEnum } from './enum-origem-sistema';
import {AgendaDisponibilidade} from '@base-core/locacao-ambiente/agenda-disponibilidade.model';

export class Venda {
  unidade: number;
  plano: number;
  nome: string;
  cpf: string;
  cnpj: string;
  sexo: string;
  dataNascimento: string;
  responsavelPai: string;
  responsavelMae: string;
  cpfMae: string;
  cpfPai: string;
  email: string;
  nomeCartao: string;
  numeroCartao: string;
  validade: string;
  cvv: string;
  telefone: string;
  endereco: string;
  numero: string;
  bairro: string;
  complemento: string;
  cep: string;
  diaVencimento: number;
  nrVezesDividir: number;
  numeroCupomDesconto: string;
  cpftitularcard: string;
  vencimentoFatura: number;
  produtos: VendaProduto[];
  aulasMarcadas: string[];
  cobrarParcelasEmAberto: boolean;
  dataInicioContrato: string;
  permitirRenovacao: boolean;
  codigoCategoriaPlano: number;
  origemCobranca: number;
  cobrancaAntecipada: number;
  responsavelLink: number;
  token: string;
  nrVezesDividirMatricula: number;
  rg: string;
  codigoEvento: number;
  usuarioResponsavel: number;
  codigoRegistroAcessoPagina: number;
  ipPublico: string;
  tipoParcelamentoCredito: string;
  respostaParqJson: string;
  nomeResponsavelEmpresa: string;
  cpfResponsavelEmpresa: string;
  lancarContratoConcomitanteAoInvesDeRenovar: boolean;
  pactoPayComunicacao: number;
  utm_data: string;
  modalidadesSelecionadas: any[];
  horariosSelecionados: any[];
  parcelasSelecionadas: string;
  todasEmAberto: boolean;
  origemSistema: OrigemSistemaEnum;
  dataUtilizacao: string;
  permiteInformarDataUtilizacao: boolean;
  locacaoAmbiente: boolean;
  locacoesSelecionadas: AgendaDisponibilidade[];

  clientesCadastradosComoDependentesPlanoCompartilhado: Cliente[];

  constructor(unidade: number, plano: number, nome: string, cpf: string, sexo: string, dataNascimento: string, email: string,
              nomeCartao: string, numeroCartao: string, validade: string, cvv: string,
              telefone: string, endereco: string, numero: string, bairro: string, complemento: string, cep: string,
              diaVencimento: number, nrVezesDividir: number, numeroCupomDesconto: string, cpftitularcard: string,
              vencimentoFatura: number, produtos: VendaProduto[], cobrarParcelasEmAberto: boolean, dataInicioContrato: string,
              responsavelPai: string, responsavelMae: string, cpfMae: string, cpfPai: string, aulasMarcadas: string[],
              permitirRenovacao: boolean, codigoCategoriaPlano: number, origemCobranca: number, cobrancaAntecipada: number,
              responsavelLink: number, token: string, nrVezesDividirMatricula: number, rg: string, codigoEvento: number,
              usuarioResponsavel: number, codigoRegistroAcessoPagina: number, ipPublico: string, tipoParcelamentoCredito: string,
              respostaParqJson: string, cnpj: string, nomeResponsavelEmpresa: string, cpfResponsavelEmpresa: string,
              lancarContratoConcomitanteAoInvesDeRenovar: boolean, pactoPayComunicacao: number, utm_data: string,
              clientesCadastradosComoDependentesPlanoCompartilhado: Cliente[], modalidadesSelecionadas: any[], horariosSelecionados: any[],
              parcelasSelecionadas: string, todasEmAberto: boolean, origemSistema: OrigemSistemaEnum = OrigemSistemaEnum.VENDAS_ONLINE, dataUtilizacao: string,
              permiteInformarDataUtilizacao: boolean, locacaoAmbiente: boolean, locacoesSelecionadas: AgendaDisponibilidade[]) {
    this.unidade = unidade;
    this.plano = plano;
    this.nome = nome;
    this.cpf = cpf;
    this.sexo = sexo;
    this.dataNascimento = dataNascimento;
    this.responsavelPai = responsavelPai;
    this.responsavelMae = responsavelMae;
    this.cpfMae = cpfMae;
    this.cpfPai = cpfPai;
    this.email = email;
    this.numeroCartao = numeroCartao;
    this.nomeCartao = nomeCartao;
    this.cvv = cvv;
    this.validade = validade;
    this.telefone = telefone;
    this.endereco = endereco;
    this.numero = numero;
    this.bairro = bairro;
    this.complemento = complemento;
    this.cep = cep;
    this.diaVencimento = diaVencimento;
    this.nrVezesDividir = nrVezesDividir;
    this.numeroCupomDesconto = numeroCupomDesconto;
    this.cpftitularcard = cpftitularcard;
    this.vencimentoFatura = vencimentoFatura;
    this.produtos = produtos;
    this.cobrarParcelasEmAberto = cobrarParcelasEmAberto;
    this.dataInicioContrato = dataInicioContrato;
    this.aulasMarcadas = aulasMarcadas;
    this.permitirRenovacao = permitirRenovacao;
    this.codigoCategoriaPlano = codigoCategoriaPlano;
    this.origemCobranca = origemCobranca;
    this.cobrancaAntecipada = cobrancaAntecipada;
    this.responsavelLink = responsavelLink;
    this.token = token;
    this.nrVezesDividirMatricula = nrVezesDividirMatricula;
    this.rg = rg;
    this.codigoEvento = codigoEvento;
    this.usuarioResponsavel = usuarioResponsavel;
    this.codigoRegistroAcessoPagina = codigoRegistroAcessoPagina;
    this.ipPublico = ipPublico;
    this.tipoParcelamentoCredito = tipoParcelamentoCredito;
    this.respostaParqJson = respostaParqJson;
    this.cnpj = cnpj;
    this.nomeResponsavelEmpresa = nomeResponsavelEmpresa;
    this.cpfResponsavelEmpresa = cpfResponsavelEmpresa;
    this.lancarContratoConcomitanteAoInvesDeRenovar = lancarContratoConcomitanteAoInvesDeRenovar;
    this.pactoPayComunicacao = pactoPayComunicacao;
    this.utm_data = utm_data;
    this.clientesCadastradosComoDependentesPlanoCompartilhado = clientesCadastradosComoDependentesPlanoCompartilhado;
    this.modalidadesSelecionadas = modalidadesSelecionadas;
    this.horariosSelecionados = horariosSelecionados;
    this.parcelasSelecionadas = parcelasSelecionadas;
    this.todasEmAberto = todasEmAberto;
    this.origemSistema = origemSistema;
    this.dataUtilizacao = dataUtilizacao;
    this.permiteInformarDataUtilizacao = permiteInformarDataUtilizacao;
    this.locacaoAmbiente = locacaoAmbiente;
    this.locacoesSelecionadas = locacoesSelecionadas;
  }
}
