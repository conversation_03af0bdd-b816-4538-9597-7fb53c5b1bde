import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';

export class Parcela {
  codigo: number;
  descricao: string;
  valor: number;
  desconto: number;
  tipoProduto: string;
  nrDiasVigencia: string;
  vencida: boolean;
  juros: number;
  multa: number;

  constructor(codigo: number, descricao: string, valor: number, tipoProduto: string, nrDiasVigencia: string, vencida: boolean, juros: number, multa: number) {
    this.codigo = codigo;
    this.descricao = descricao;
    this.valor = valor;
    this.tipoProduto = tipoProduto;
    this.nrDiasVigencia = nrDiasVigencia;
    this.vencida = vencida;
    this.juros = juros;
    this.multa = multa;
  }
}


@Injectable({
  providedIn: 'root'
})

export class ParcelaAdapter implements Adapter<Parcela> {
  adapt(item: any): Parcela {
    return new Parcela(
      item.codigo,
      item.descricao,
      item.valor,
      item.tipoProduto,
      item.nrDiasVigencia,
      item.vencida,
      item.juros,
      item.multa
    );
  }
}

