import { Injectable } from '@angular/core';
import { BehaviorSubject} from 'rxjs';

@Injectable({
  providedIn: 'root'
})

export class NascimentoService {

  private idadeUser = new BehaviorSubject<number | null>(null);
  private dataNascimentoUser = new BehaviorSubject<string | null>(null);

  idadeUsuario$ = this.idadeUser.asObservable();

  setIdade(idade:number): void {
    this.idadeUser.next(idade);
  }

  setDataNascimento(nasc:string): void {
    this.dataNascimentoUser.next(nasc);
  }

  getIdade(): number | null {
    return this.idadeUser.getValue();
  }

  getDataNascimento(): string | null {
    return this.dataNascimentoUser.getValue().split('-').join('/');
  }
}
