import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';
import {Config} from '@base-core/empresa/config.model';


export class Cliente {
  unidade: number;
  nome: string;
  cpf: string;
  responsavelPai: string;
  responsavelMae: string;
  sexo: string;
  email: string;
  dataNascimento: string;
  telefone: string;
  endereco: string;
  numero: string;
  bairro: string;
  complemento: string;
  cep: string;
  cpfPai: string;
  cpfMae: string;
  remetenteConviteMatricula: string;
  rg: string;
  codigoEvento: number;
  usuarioResponsavel: number;
  codigoRegistroAcessoPagina: number;
  freepass: String;
  aulasMarcadas: string[];
  cadastradoComoDependentePlanoCompartilhado?: boolean;
  cpfTitular?: string;

constructor(unidade: number, nome: string, cpf: string, responsavelPai: string, responsavelMae: string, sexo: string,
            datanasc: string, email: string, telefone: string,   cep: string, endereco: string, numero: string,
            bairro: string,  complemento: string, cpfMae: string, cpfPai: string, remetenteConviteMatricula: string,
            rg: string, codigoEvento: number, usuarioResponsavel: number, codigoRegistroAcessoPagina: number,
            freepass: String, aulasMarcadas: string[], cadastradoComoDependentePlanoCompartilhado: boolean, cpfTitular: string) {
    this.unidade = unidade;
    this.nome = nome;
    this.cpf = cpf;
    this.responsavelPai = responsavelPai;
    this.responsavelMae = responsavelMae;
    this.sexo = sexo;
    this.dataNascimento = datanasc;
    this.email = email;
    this.telefone = telefone;
    this.cep = cep;
    this.endereco = endereco;
    this.numero = numero;
    this.bairro = bairro;
    this.complemento = complemento;
    this.cpfMae = cpfMae;
    this.cpfPai = cpfPai;
    this.remetenteConviteMatricula = remetenteConviteMatricula;
    this.rg = rg;
    this.codigoEvento = codigoEvento;
    this.usuarioResponsavel = usuarioResponsavel;
    this.codigoRegistroAcessoPagina = codigoRegistroAcessoPagina;
    this.freepass = freepass;
    this.aulasMarcadas = aulasMarcadas;
    if (cadastradoComoDependentePlanoCompartilhado) {
      this.cadastradoComoDependentePlanoCompartilhado = cadastradoComoDependentePlanoCompartilhado;
      this.cpfTitular = cpfTitular;
    } else {
      this.cadastradoComoDependentePlanoCompartilhado = false;
      this.cpfTitular = null;
    }
  }

}

@Injectable({
  providedIn: 'root'
})
export class ClienteAdapter implements Adapter<Cliente> {

  adapt(item: any): Cliente {
    return new Cliente(
      item.unidade,
      item.nome,
      item.cpf,
      item.responsavelPai,
      item.responsavelMae,
      item.sexo,
      item.dataNascimento,
      item.email,
      item.telefone,
      item.endereco,
      item.numero,
      item.complemento,
      item.bairro,
      item.cep,
      item.cpfMae,
      item.cpfPai,
      item.remetenteConviteMatricula,
      item.rg,
      item.codigoEvento,
      item.usuarioResponsavel,
      item.codigoRegistroAcessoPagina,
      item.freepass,
      item.aulasMarcadas,
      item.cadastradoComoDependentePlanoCompartilhado,
      item.cpfTitular
    );
  }
  adaptSimples(item: any): Cliente {
    return new Cliente(
      item.unidade,
      item.nome,
      item.cpf,
      item.responsavelPai,
      item.responsavelMae,
      item.sexo,
      item.email,
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      item.cpfMae,
      item.cpfPai,
      item.remetenteConviteMatricula,
      item.rg,
      item.codigoEvento,
      item.usuarioResponsavel,
      item.codigoRegistroAcessoPagina,
      item.freepass,
      item.aulasMarcadas,
      item.cadastradoComoDependentePlanoCompartilhado,
      item.cpfTitular
    );
  }


}
