import {Injectable} from '@angular/core';
import {Config} from '@base-core/empresa/config.model';
import {RestService} from '@base-core/rest/rest.service';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {Cliente} from '@base-core/cliente/cliente.model';
import {TranslateService} from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})

export class ClienteService {
  chave;
  menorIdade = false;

  constructor(private rest: RestService,
              private http: HttpClient,
              private translateService: TranslateService) {
  }

  validar(cliente: Cliente, config: Config): string {
    let campos;
    if (config.camposAdicionais != undefined && config.camposAdicionais.length != 0) {
      campos = config.camposAdicionais;
    } else if (config.camposAdicionaisProduto != undefined && config.camposAdicionaisProduto.length != 0) {
      campos = config.camposAdicionaisProduto;
    } else if (config.camposAdicionaisProdutoPlano != undefined && config.camposAdicionaisProdutoPlano.length != 0) {
      campos = config.camposAdicionaisProdutoPlano;
    }
    if ( !campos ) {
      campos = [];
    } else {
      for (let c of campos) {
        let humanFieldName = '';
        const nomesDosAtributos = Reflect.ownKeys(cliente);
        for (const v of nomesDosAtributos) {
          switch (c) {
            case 'CPF_RESPONSAVEL_PAI':
              c = 'cpfPai';
              humanFieldName = 'CPF do Pai';
              break;
            case 'CPF_RESPONSAVEL_MAE':
              c = 'cpfMae';
              humanFieldName = 'CPF da Mãe';
              break;
            case 'RESPONSAVEL_PAI':
              c = 'responsavelPai';
              humanFieldName = 'Nome do Pai';
              break;
            case 'RESPONSAVEL_MAE':
              c = 'responsavelMae';
              humanFieldName = 'Nome da Mãe';
              break;
            case 'DIA_VENCIMENTO':
              c = 'diaVencimento';
              humanFieldName = 'Dia de Vencimento';
              break;
            //Hibrael - 12/02/2025 - Comentado pois este campo nao estava sendo utilizado, e essa validacao estava impedindo que 
            //compras fossem finalizadas pelo vendas online, ja que estava exigindo o preenchimento do campo 
            //case 'VENCIMENTO_FATURA':
              //c = 'vencimentoFatura';
              //humanFieldName = 'Vencimento da Fatura';
              //break;
            case 'INICIO_CONTRATO':
              c = 'dataInicioContrato';
              humanFieldName = 'Início do Contrato';
              break;
            case 'DT_NASCIMENTO':
              c = 'dataNascimento';
              humanFieldName = 'Data de Nascimento';
              break;
          }

          if (this.menorIdade) {
            if (!cliente.cpfPai && !cliente.responsavelPai && !cliente.cpfMae && !cliente.responsavelMae) {
              return window.localStorage.getItem('usarSistemaInternacional') === 'false' ?
                'Informe o Nome e o CPF de pelo menos um responsável!' :
                'Provide the details of at least one person responsible';
            } else if (cliente.cpfPai && !cliente.responsavelPai) {
              return window.localStorage.getItem('usarSistemaInternacional') === 'false' ?
                'Informe o Nome do Pai responsável!' :
                'Provide the details of at least one person responsible';
            } else if (!cliente.cpfPai && cliente.responsavelPai) {
              return window.localStorage.getItem('usarSistemaInternacional') === 'false' ?
                'Informe o CPF do Pai responsável!' :
                'Provide the details of at least one person responsible';
            } else if (cliente.cpfMae && !cliente.responsavelMae) {
              return window.localStorage.getItem('usarSistemaInternacional') === 'false' ?
                'Informe o Nome da Mãe responsável!' :
                'Provide the details of at least one person responsible';
            } else if (!cliente.cpfMae && cliente.responsavelMae) {
              return window.localStorage.getItem('usarSistemaInternacional') === 'false' ?
                'Informe o CPF da Mãe responsável!' :
                'Provide the details of at least one person responsible';
            }
          }

          const nomeCampoEmCaixaBaixa = c.toLowerCase();
          const nomeAtributoCaixaBaixa = v.toString().toLowerCase();
          const ignorarValidacaoDoCampoParaMenorIdade = nomeCampoEmCaixaBaixa == 'rg' && this.menorIdade;
          if (nomeAtributoCaixaBaixa == nomeCampoEmCaixaBaixa && !ignorarValidacaoDoCampoParaMenorIdade) {
            if (cliente[v] == '' || cliente[v] == null) {
              const sistemaInternacional = window.localStorage.getItem('usarSistemaInternacional') === 'true';
              if (sistemaInternacional && c.toLowerCase() === 'cpf') {
                continue;
              }

              const emptyField = (humanFieldName === '') ? c : humanFieldName;
              return sistemaInternacional ?
                'All fields required! Please fill the field ' + emptyField :
                'Todos os campos são obrigatórios! Preencha o campo ' + emptyField;
            }
          }
        }
      }
    }
    if ( !cliente.nome) {
      return this.translateService.instant('checkout.campo-nome-obrigatorio');
    }
    const temSobrenome = cliente.nome.trim().split(' ');
    if (temSobrenome.length < 2) {
      return this.translateService.instant('checkout.campo-nome-sobrenome-obrigatorio');
    }
    if (!this.menorIdade && !cliente.cpf && !( window.localStorage.getItem('usarSistemaInternacional') != undefined && window.localStorage.getItem('usarSistemaInternacional').toUpperCase() === 'TRUE')){
      return this.translateService.instant('checkout.digite-seu-cpf');
    }
    if (!this.menorIdade && campos.indexOf('RG') > -1 && !cliente.rg) {
      return this.translateService.instant('checkout.campo-rg-obrigatorio');
    }
    if (campos.indexOf('DT_NASCIMENTO') > -1 && (!cliente.dataNascimento || cliente.dataNascimento === 'Invalid date')) {
      return this.translateService.instant('checkout.campo-data-nascimento-obrigatorio');
    }
    if (!cliente.email) {
      return this.translateService.instant('checkout.digite-o-seu-email');
    }
    if (!this.isValidEmail(cliente.email)) {
      return this.translateService.instant('checkout.email-invalido');
    }
    if (campos.indexOf('TELEFONE') > -1 && !cliente.telefone) {
      return this.translateService.instant('checkout.campo-telefone-obrigatorio');
    }
    if (campos.indexOf('CEP') > -1 && !cliente.cep) {
      return '';
    }
    if (campos.indexOf('ENDERECO') > -1 && !cliente.endereco) {
      return this.translateService.instant('checkout.campo-endereco-obrigatorio');
    }
    if (campos.indexOf('BAIRRO') > -1 && !cliente.bairro) {
      return this.translateService.instant('checkout.campo-bairro-obrigatorio');
    }
    if (campos.indexOf('COMPLEMENTO') > -1 && !cliente.complemento) {
      return this.translateService.instant('checkout.campo-complemento-obrigatorio');
    }
    if ((window.localStorage.getItem('menor') &&  window.localStorage.getItem('menor').toUpperCase() === 'TRUE' &&
        (!cliente.responsavelPai  && !cliente.cpfPai ||  cliente.responsavelPai  !== ''  && !cliente.cpfPai || !cliente.responsavelPai   &&  cliente.cpfPai !== ''  ))
      && (window.localStorage.getItem('menor') &&  window.localStorage.getItem('menor').toUpperCase() === 'TRUE' &&
        (!cliente.responsavelMae  && !cliente.cpfMae ||  cliente.responsavelMae  !== ''  && !cliente.cpfMae || !cliente.responsavelMae   &&  cliente.cpfMae !== ''  ))) {
      return this.translateService.instant('checkout.obrigatorio-ao-menos-um-responsavel');
    }
    return null;
  }

  gravarCadastro(clienteDTO, captcha): Observable<Object> {
    const url = this.rest.buildFullUrl('v2/vendas/' + this.chave + '/gravarAlunoVendaOnline/' + captcha);
    return this.http.post(url, clienteDTO);
  }

  public isValidEmail(email) {
    const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return regex.test(email);
  }
}
