import { Injectable } from '@angular/core';
import {RestService} from '@base-core/rest/rest.service';
import {HttpClient} from '@angular/common/http';
import {map} from 'rxjs/operators';
import {take} from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class EmailService {
  public nomeEmpresa: string;
  public emailEmpresa: string;

  constructor(private rest: RestService,
              private http: HttpClient) { }

  getIpCliente() {
    return this.http.get("https://api.ipify.org/?format=json");
  }

  gravarEmail(encrypt, dados, captcha) {
    const url = this.rest.buildFullUrl('optin/' + encrypt + '/atualizar/' + captcha);
    return this.http.put(url, dados).pipe(map((data: any) => {
      return data.return;
    }));
  }

  obterLogoEmail(crypt) {
    const url = this.rest.buildFullUrl('v2/vendas/logoEmail/' + crypt);
    return this.http.get(url).pipe(map((data: any) => {
        return data.return;
      }),
    );
  }

  decrypt(dado) {
    const url = this.rest.buildFullUrl('optin/decrypt/' + dado);
    return this.http.post(url, {});
  }

  atualizarEmail(dados, captcha) {
    const url = this.rest.buildFullUrl('optin/' + dados.chave + '/atualizar/' + captcha);
    this.deletarDados(dados);
    return this.http.put(url, dados).pipe(take(1));
  }

  deletarDados (dados) {
    delete dados.chave;
    delete dados.emailEmpresa;
  }

}
