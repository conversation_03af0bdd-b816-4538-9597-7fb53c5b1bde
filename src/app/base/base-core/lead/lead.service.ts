import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { RestService } from '@base-core/rest/rest.service';
import { Config } from '@base-core/empresa/config.model';
import { Lead } from '@base-core/lead/lead.model';

@Injectable({
  providedIn: 'root'
})
export class LeadService {

  chave: any;

  // INPUT LEAD
  private nomeInput = new BehaviorSubject<string>('');
  private telefoneInput = new BehaviorSubject<string>('');
  private emailInput = new BehaviorSubject<string>('');

  nome$ = this.nomeInput.asObservable();
  telefone$ = this.telefoneInput.asObservable();
  email$ = this.emailInput.asObservable();

  constructor(
    private http: HttpClient,
    private restService: RestService
  ) {}

  validarLead(lead: Lead, config: Config): string {
    const campos: string[] = [];

    if (!lead.nome) {
      campos.push('Nome');
    }

    if (!lead.email) {
      campos.push('E-mail');
    }

    if (!lead.telefone) {
      campos.push('Telefone');
    }

    if (campos.length > 0) {
      return 'Por favor, preencha os seguintes campos: ' + campos.join(', ') + '.';
    }

    return 'Sucesso';
  }

  gravarCadastroLead (clienteDTO: any, codigo: any): Observable<Object> {
    const url = this.restService.buildFullUrl('lead/' + this.chave + '/' + codigo + '/v2/addLead');
    return this.http.post(url, clienteDTO);
  }

  setNome(nome: string) {
    this.nomeInput.next(nome);
  }

  setTelefone(telefone: string) {
    this.telefoneInput.next(telefone);
  }

  setEmail(email: string) {
    this.emailInput.next(email);
  }
}
