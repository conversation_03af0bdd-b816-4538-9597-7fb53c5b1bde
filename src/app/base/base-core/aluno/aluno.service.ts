import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {RestService} from '@base-core/rest/rest.service';
import {Observable} from 'rxjs/index';
import {Aluno, AlunoAdapter} from '@base-core/aluno/aluno.model';
import {map} from 'rxjs/operators';
import { OrigemSistemaEnum } from '@base-core/negociacao/enum-origem-sistema';

@Injectable({
  providedIn: 'root'
})
export class AlunoService {
  alunoSelecionado: Aluno;
  constructor(
    private http: HttpClient,
    private adapter: AlunoAdapter,
    private restService: RestService
  ) { }

  // aluno(chave, matricula, tipocobranca, cobrancaAntecipada): Observable<Aluno> {
  //   const url = this.restService.buildFullUrl('v2/vendas/' + chave + '/aluno/' + matricula + '?tipocobranca=' +
  //     tipocobranca + '&cobrancaAntecipada=' + cobrancaAntecipada);
  //   return this.http.get(url).pipe(
  //     map((data: any) => this.adapter.adapt(data.return)),
  //   );
  // }
  //Hibrael - 19/02/2025 - Acrescentando o parametro origem para identificar no back-end que a requisição veio do Vendas Online
  // 9 - Vendas Online no OrigemSistemaEnum do projeto da API
  aluno(chave, matricula, tipocobranca, cobrancaAntecipada, pactoPayComunicacao, todasEmAberto, parcelasSelecionadas): Observable<Object> {
    const url = this.restService.buildFullUrl('v2/vendas/' + chave + '/aluno/' + matricula + '?tipocobranca=' +
      tipocobranca + '&cobrancaAntecipada=' + cobrancaAntecipada + '&pactoPayComunicacao=' + (pactoPayComunicacao ? pactoPayComunicacao : 0) +
      (todasEmAberto ? ('&todasEmAberto=' + todasEmAberto) : '') + (parcelasSelecionadas ? ('&parcelasSelecionadas=' + parcelasSelecionadas) : '') +
      '&origem=' + OrigemSistemaEnum.VENDAS_ONLINE);
    return this.http.get(url);
  }
  cpf(chave, cpf, gRecaptchaResponse, consultarRestricoes?: boolean): Observable<Object> {
    let url = this.restService.buildFullUrl('cliente/' + chave + '/' + gRecaptchaResponse
      + '/consultarClienteJson?cpf='
      + cpf
      + '&consultarEndereco=true'
      + '&consultarQtdDependentes=true'
    );
    if (consultarRestricoes) {
      url += '&consultarRestricoes=true';
    }
    return this.http.post(url, {});
  }
  cpfTelaVisitante(chave, empresa, cpf, gRecaptchaResponse, consultarRestricoes?: boolean): Observable<Object> {
    let url = this.restService.buildFullUrl('cliente/' + chave + '/' + gRecaptchaResponse
      + '/consultarClienteJson?cpf='
      + cpf
      + '&consultarEndereco=true'
      + '&consultarQtdDependentes=true'
    );
    if (consultarRestricoes) {
      url += '&consultarRestricoes=true';
    }
    url += '&telaCadastroVisitante=true';
    url += '&empresa=' + empresa;
    return this.http.post(url, {});
  }
  emailTelaVisitante(chave, email, gRecaptchaResponse, empresa): Observable<Object> {
    const url = this.restService.buildFullUrl('cliente/' + chave + '/' + gRecaptchaResponse + '/consultarClienteJson?email=' + email + '&consultarEndereco=true' + '&empresa=' + empresa + '&telaCadastroVisitante=true');
    return this.http.post(url, {});
  }
  cnpj(chave, cnpj, gRecaptchaResponse): Observable<Object> {
    const url = this.restService.buildFullUrl('cliente/' + chave + '/' + gRecaptchaResponse + '/consultarClienteJson?cnpj=' + cnpj);
    return this.http.post(url, {});
  }
  cep(cep): Observable<Object> {
    const url = this.restService.buildFullUrl('cep' + '/consultar?cep=' + cep);
    return this.http.post(url, {});
  }
  email(chave, email, gRecaptchaResponse): Observable<Object> {
    const url = this.restService.buildFullUrl('cliente/' + chave + '/' + gRecaptchaResponse + '/consultarClienteJson?email=' + email + '&consultarEndereco=true');
    return this.http.post(url, {});
  }

  contratosRenovarComPermissao(chave, unidade, cliente): Observable<Object> {
    const url = this.restService.buildFullUrl('cliente/' + chave + '/consultarContratosRenovarComPermissao?unidade=' +
      unidade + '&cliente=' + cliente);
    return this.http.post(url, {});
  }

  clicouPactoPayComunicacao(chave, pactoPayComunicacao): Observable<Object> {
    const url = this.restService.buildFullUrl('pactopay/' + chave + '/reguacobranca/' + pactoPayComunicacao + '/clicou');
    return this.http.post(url, {});
  }

  consultarUnidadesRestricoes(chave: string, gRecaptchaResponse: string, cpf: string): Observable<Object> {
    const url = this.restService.buildFullUrl(`cliente/${chave}/${gRecaptchaResponse}/restricoes/${cpf}`);
    return this.http.get(url);
  }
}
