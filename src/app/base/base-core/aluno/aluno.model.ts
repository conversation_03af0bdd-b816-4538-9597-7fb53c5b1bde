import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';
import {Parcela} from '@base-core/negociacao/parcela.model';


export class Aluno {
  codigo: number;
  matricula: string;
  nome: string;
  cpf: string;
  email: string;
  referente: string;
  valorCobrar: number;
  desconto: number;
  parcelamentoOperadora: boolean;
  maximoVezesParcelar: number;
  parcelasCobrar: Array<Parcela> = [];
  nomeResponsavelMae: string;
  cpfResponsavelMae: string;
  nomeResponsavelPai: string;
  cpfResponsavelPai: string;


  constructor(codigo: number, nome: string, cpf: string, matricula: string, email: string,
              valorCobrar: number, referente: string, parcelamentoOperadora: boolean,
              maximoVezesParcelar: number, desconto: number, parcelasCobrar: Array<Parcela> = [], nomeResponsavelMae: string,
              cpfResponsavelMae: string, nomeResponsavelPai: string, cpfResponsavelPai: string) {
    this.codigo = codigo;
    this.nome = nome;
    this.cpf = cpf;
    this.email = email;
    this.matricula = matricula;
    this.referente = referente;
    this.valorCobrar = valorCobrar;
    this.parcelamentoOperadora = parcelamentoOperadora;
    this.maximoVezesParcelar = maximoVezesParcelar;
    this.desconto = desconto;
    this.parcelasCobrar = parcelasCobrar;
    this.nomeResponsavelMae = nomeResponsavelMae;
    this.cpfResponsavelMae = cpfResponsavelMae;
    this.nomeResponsavelPai = nomeResponsavelPai;
    this.cpfResponsavelPai = cpfResponsavelPai;
  }

}

@Injectable({
  providedIn: 'root'
})
export class AlunoAdapter implements Adapter<Aluno> {

  adapt(item: any): Aluno {
    return new Aluno(
      item.codigo,
      item.nome,
      item.cpf,
      item.matricula,
      item.email,
      item.valorCobrar,
      item.referente,
      item.parcelamentoOperadora,
      item.maximoVezesParcelar,
      item.desconto,
      item.parcelasCobrar,
      item.nomeResponsavelMae,
      item.cpfResponsavelMae,
      item.nomeResponsavelPai,
      item.cpfResponsavelPai
    );
  }
  adaptSimples(item: any): Aluno {
    return new Aluno(
      item.codigo,
      item.nome,
      item.cpf,
      item.matricula,
      item.email,
      0.0,
      '',
      false,
      1,
      0.0,
      null,
      '',
      '',
      '',
      ''
    );
  }
}
