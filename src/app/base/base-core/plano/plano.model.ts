import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';
import {PlanoAnuidadeParcelada} from '@base-core/plano/plano.anuidade.parcelada.model';
import {Modalidade} from '../../model/vendasOnlineV3.model';


export class Plano {
  codigo: number;
  nome: string;
  adesao: number;
  mensalidade: number;
  primeiraParcela: number;
  anuidade: number;
  fidelidade: number;
  quantidadeDeDiasDuracaoPlano: number;
  quantidadeCompartilhamentos: number;
  descricaoEncantamento: string;
  mesAnuidade: string;
  produtos: string;
  modalidades: string[];
  valorProdutos: number;
  inicioFuturo: boolean;
  anuidadeAgora: boolean;
  parcelamentoOperadora: boolean;
  maxDivisao: number;
  diasVencimento: number[];
  qtdCreditoPlanoCredito: number;
  regimeRecorrencia: boolean;
  matricula: number;
  parcelasAnuidade: Array<PlanoAnuidadeParcelada>;
  categorias: number[];
  nrVezesParcelarMatricula: number;
  renovavelAutomaticamente: boolean;
  cobrarPrimeiraParcelaCompra: boolean;
  valorTotalDoPlano: number;
  videoSiteUrl: string;
  observacaoSite: string;
  vendaComTurma: boolean;
  modalidadesDTO: Array<Modalidade>;
  valorTotalPlanoTurma: number;
  permitirCompartilharPLanoNoSite: boolean;



  constructor(codigo: number, nome: string, adesao: number, mensalidade: number, primeiraParcela: number,
              // tslint:disable-next-line:max-line-length
              anuidade: number, fidelidade: number, quantidadeDeDiasDuracaoPlano: number, quantidadeCompartilhamentos: number, descricaoEncantamento: string, produtos: string,
              valorProdutos: number, modalidades: string[], mesAnuidade: string, inicioFuturo: boolean, anuidadeAgora: boolean,
              diasVencimento: number[], parcelamentoOperadora: boolean, maxDivisao: number, qtdCreditoPlanoCredito: number,
              regimeRecorrencia: boolean, matricula: number, parcelasAnuidade: Array<PlanoAnuidadeParcelada>, categorias: number[],
              nrVezesParcelarMatricula: number, renovavelAutomaticamente: boolean, cobrarPrimeiraParcelaCompra: boolean,
              valorTotalDoPlano: number, videoSiteUrl: string, observacaoSite: string,
              vendaComTurma: boolean, modalidadesDTO: Array<Modalidade>, valorTotalPlanoTurma: number, permitirCompartilharPLanoNoSite: boolean) {
    this.codigo = codigo;
    this.nome = nome;
    this.adesao = adesao;
    this.mensalidade = mensalidade;
    this.primeiraParcela = primeiraParcela;
    this.anuidade = anuidade;
    this.fidelidade = fidelidade;
    this.quantidadeDeDiasDuracaoPlano = quantidadeDeDiasDuracaoPlano;
    this.quantidadeCompartilhamentos = quantidadeCompartilhamentos;
    this.descricaoEncantamento = descricaoEncantamento;
    this.produtos = produtos;
    this.valorProdutos = valorProdutos;
    this.modalidades = modalidades;
    this.mesAnuidade = mesAnuidade;
    this.inicioFuturo = inicioFuturo;
    this.anuidadeAgora = anuidadeAgora;
    this.parcelamentoOperadora = parcelamentoOperadora;
    this.maxDivisao = maxDivisao;
    this.diasVencimento = diasVencimento;
    this.qtdCreditoPlanoCredito = qtdCreditoPlanoCredito;
    this.regimeRecorrencia = regimeRecorrencia;
    this.matricula = matricula;
    this.parcelasAnuidade = parcelasAnuidade;
    this.categorias = categorias;
    this.nrVezesParcelarMatricula = nrVezesParcelarMatricula;
    this.renovavelAutomaticamente = renovavelAutomaticamente;
    this.cobrarPrimeiraParcelaCompra = cobrarPrimeiraParcelaCompra;
    this.valorTotalDoPlano = valorTotalDoPlano;
    this.videoSiteUrl = videoSiteUrl;
    this.observacaoSite = observacaoSite;
    this.vendaComTurma = vendaComTurma;
    this.modalidadesDTO = modalidadesDTO;
    this.valorTotalPlanoTurma = valorTotalPlanoTurma;
    this.permitirCompartilharPLanoNoSite = permitirCompartilharPLanoNoSite;
  }
}

@Injectable({
  providedIn: 'root'
})
export class PlanoAdapter implements Adapter<Plano> {

  adapt(item: any): Plano {
    return new Plano(
      item.codigo,
      item.nome,
      item.adesao,
      item.mensalidade,
      item.primeiraParcela,
      item.anuidade,
      item.fidelidade,
      item.quantidadeDeDiasDuracaoPlano,
      item.quantidadeCompartilhamentos,
      item.descricaoEncantamento,
      item.produtos,
      item.valorProdutos,
      item.modalidades,
      item.mesAnuidade,
      item.inicioFuturo,
      item.anuidadeAgora,
      item.diasVencimento,
      item.parcelamentoOperadora,
      item.maxDivisao,
      item.qtdCreditoPlanoCredito,
      item.regimeRecorrencia,
      item.matricula,
      item.parcelasAnuidade,
      item.categorias,
      item.nrVezesParcelarMatricula,
      item.renovavelAutomaticamente,
      item.cobrarPrimeiraParcelaCompra,
      item.valorTotalDoPlano,
      item.videoSiteUrl,
      item.observacaoSite,
      item.vendaComTurma,
      item.modalidadesDTO,
      item.valorTotalPlanoTurma,
      item.permitirCompartilharPLanoNoSite,
    );
  }
}
