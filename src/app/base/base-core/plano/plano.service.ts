import { Injectable } from '@angular/core';
import {Plano, PlanoAdapter} from '@base-core/plano/plano.model';
import {BehaviorSubject, Observable} from 'rxjs';
import {HttpClient} from '@angular/common/http';
import {RestService} from '@base-core/rest/rest.service';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class PlanoService {
  planos: Array<Plano> = [];
  planoSelecionado: Plano;
  permitirRenovacao: boolean = false;
  vezesEscolhidasParcelarTaxaMatricula: number;
  contratoRematricula: boolean = false;
  dadosCompletosDesmascarados: any;

  private exibirCamposCompartilhamentoPlanoSubject = new BehaviorSubject<boolean>(false);
  exibirCamposCompartilhamentoPlano$ = this.exibirCamposCompartilhamentoPlanoSubject.asObservable();

  setExibirCamposCompartilhamentoPlano(value: boolean) {
    this.exibirCamposCompartilhamentoPlanoSubject.next(value);
  }

  getExibirCamposCompartilhamentoPlanoValue(): boolean {
    return this.exibirCamposCompartilhamentoPlanoSubject.getValue();
  }
  constructor(
    private http: HttpClient,
    private adapter: PlanoAdapter,
    private restService: RestService
  ) { }

  obterPlanos(chave, codigo): Observable<Array<Plano>> {
    const url = this.restService.buildFullUrl('v2/vendas/' + chave + '/planos/' + codigo);
    return this.http.get(url).pipe(
      map((data: any) => data.return.map(item => this.adapter.adapt(item))),
    );
  }
  obterPlano(chave, unidade, codigo): Observable<Plano> {
    let url = this.restService.buildFullUrl('v2/vendas/' + chave + '/plano/' + unidade + '/' + codigo);
    url += `?isRenovacao=${this.permitirRenovacao}&isRematricula=${this.contratoRematricula}`;
    return this.http.get(url).pipe(
      map((data: any) => this.adapter.adapt(data.return)),
    );
  }
  obterTermoAceite(chave, plano): Observable<Object> {
    const url = this.restService.buildFullUrl('negociacao/' + chave + '/imprimirTermoAceite?codigoPlano=' + plano);
    return this.http.post(url, {});
  }
  obterTermoAceiteLinkPag(chave): Observable<Object> {
    const url = this.restService.buildFullUrl('/v2/vendas/' + chave + '/imprimirTermoAceiteLinkPag');
    return this.http.post(url, {});
  }
  obterContrato(chave, plano, unidade): Observable<Object> {
    const url = this.restService.buildFullUrl('v2/vendas/' + chave + '/plano/' + unidade + '/contrato/' + plano);
    return this.http.get(url);
  }

}
