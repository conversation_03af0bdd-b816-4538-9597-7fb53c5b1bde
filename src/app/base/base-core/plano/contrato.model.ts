import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';


export class Contrato {
  codigo: number;
  codigoCliente: number;
  codigoPlano: number;
  valorFinal: number;
  contratoEnviado: boolean;
  reciboEnviado: boolean;
  situacaoContrato: string;
  dataLancamento: string;
  vigenciaDe: string;
  vigenciaAteAjustada: string;
  situacao: string;
  nomePlano: string;
  renovacao: boolean;
  rematricula: boolean;


  constructor(codigo: number, codigoCliente: number, codigoPlano: number, valorFinal: number, contratoEnviado: boolean, reciboEnviado: boolean, situacaoContrato: string, dataLancamento: string, vigenciaDe: string, vigenciaAteAjustada: string, situacao: string, nomePlano: string, renovacao: boolean, rematricula: boolean) {
    this.codigo = codigo;
    this.codigoCliente = codigoCliente;
    console.log('Plano cód = '+codigoPlano);
    this.codigoPlano = codigoPlano;
    this.valorFinal = valorFinal;
    this.contratoEnviado = contratoEnviado;
    this.reciboEnviado = reciboEnviado;
    this.situacaoContrato = situacaoContrato;
    this.dataLancamento = dataLancamento;
    this.vigenciaDe = vigenciaDe;
    this.vigenciaAteAjustada = vigenciaAteAjustada;
    this.situacao = situacao;
    this.nomePlano = nomePlano;
    this.renovacao = renovacao;
    this.rematricula = rematricula;
  }
}

@Injectable({
  providedIn: 'root'
})
export class ContratoAdapter implements Adapter<Contrato> {

  adapt(item: any): Contrato {
    return new Contrato(
      item.codigo,
      item.codigoCliente,
      item.codigoPlano,
      item.valorFinal,
      item.contratoEnviado,
      item.reciboEnviado,
      item.situacaoContrato,
      item.dataLancamento,
      item.vigenciaDe,
      item.vigenciaAteAjustada,
      item.situacao,
      item.nomePlano,
      item.renovacao,
      item.rematricula
    );
  }
}
