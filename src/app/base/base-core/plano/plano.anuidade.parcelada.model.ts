import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';


export class PlanoAnuidadeParcelada {
  numero: number;
  valor: number;
  valorApresentar: string;
  parcela: number;
  parcelaApresentar: string;


  constructor(numero: number, valor: number, valorApresentar: string, parcela: number, parcelaApresentar: string) {
    this.numero = numero;
    this.valor = valor;
    this.valorApresentar = valorApresentar;
    this.parcela = parcela;
    this.parcelaApresentar = parcelaApresentar;
  }
}

@Injectable({
  providedIn: 'root'
})
export class PlanoAnuidadeParceladaAdapter implements Adapter<PlanoAnuidadeParcelada> {

  adapt(item: any): PlanoAnuidadeParcelada {
    return new PlanoAnuidadeParcelada(
      item.numero,
      item.valor,
      item.valorApresentar,
      item.parcela,
      item.parcelaApresentar,
    );
  }
}
