import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import {DiscoveryResponse} from '@base-core/discovery/discovery.model';
import {environment} from '../../../../environments/environment';
import {map} from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class DiscoveryServiceService {

  constructor(private http: HttpClient) { }

  chave(): String {
    return window.localStorage.getItem('chave');
  }

  discovery(): Observable<DiscoveryResponse> {
    const url = `${environment.discoveryUrl}/find/${this.chave()}`;
    return this.http.get<DiscoveryResponse>(url);
  }

  serviceUrls(): Observable<any> {
    return this.discovery().pipe(
      map(response => response.content.serviceUrls)
    );
  }

  crmMsUrl(): Observable<string> {
    return this.serviceUrls().pipe(
      map(serviceUrls => serviceUrls.contatoMsUrl)
    );
  }
}
