export interface DiscoveryResponse {
  content: {
    serviceUrls: {
      alunoMsUrl: string;
      loginMsUrl: string;
      colaboradorMsUrl: string;
      graduacaoMsUrl: string;
      treinoApiUrl: string;
      treinoUrl: string;
      loginAppUrl: string;
      oamdUrl: string;
      zwUrl: string;
      personagemMsUrl: string;
      autenticacaoUrl: string;
      frontPersonal: string;
      planoMsUrl: string;
      clienteMsUrl: string;
      produtoMsUrl: string;
      relatorioFull: string;
      sinteticoMsUrl: string | null;
      pactoPayDashUrl: string;
      pactoPayMsUrl: string;
      financeiroMsUrl: string;
      cadastroAuxiliarUrl: string;
      zwFrontUrl: string;
      treinoFrontUrl: string;
      apiZwUrl: string;
      relatorioMsUrl: string;
      midiaMsUrl: string;
      clubeVantagensMsUrl: string;
      acessoSistemaMsUrl: string;
      admCoreUrl: string;
      pessoaMsUrl: string;
      integracoesMsUrl: string;
      loginFrontUrl: string;
      biMsUrl: string;
      gameUrl: string | null;
      vendasOnlineUrl: string;
      contatoMsUrl: string;
      admMsUrl: string;
      integracaoGympassMsUrl: string | null;
      urlMidiaSocialMs: string;
      urlCrmFront: string;
      urlMarketingMs: string | null;
      notificacaoMs: string;
      urlTreinoPersonal: string;
      recursoMsUrl: string;
      zwBack: string;
    };
  };
}
