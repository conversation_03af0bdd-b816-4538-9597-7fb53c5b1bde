import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {RestService} from '@base-core/rest/rest.service';
import {Observable, of} from 'rxjs';
import {AutorizacaoAcesso, AutorizacaoAcessoAdapter} from '@base-core/acesso/autorizacaoAcesso.model';
import {catchError, map} from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AcessoService {

  constructor(private http: HttpClient,
              private restService: RestService,
              private adapter: AutorizacaoAcessoAdapter) {
  }

  findAutorizacaoByCpfOrTelefone(chave, text): Observable<AutorizacaoAcesso> {
    const url = this.restService.buildFullUrl('autorizacoesAcesso/' + chave + '/findBy?cpfOrTelefone=' + text);
    return this.http.get(url).pipe(
      map((res: any) => {
        if (res.meta) {
          return null;
        }
        return this.adapter.adapt(res.content);
      }),
      catchError(error => {
        console.log(error);
        return of(null);
      })
    );
  }

  accessValidation(key, accessCode): Observable<any> {
    const url = this.restService.buildFullUrl('autorizacoesAcesso/' + key + '/accessValidation?accessCode=' + accessCode);
    return this.http.post(url, {});
  }
}
