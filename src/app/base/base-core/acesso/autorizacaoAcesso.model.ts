import {IntegracaoAcesso} from '@base-core/acesso/integracaoAcesso.model';
import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';

export class AutorizacaoAcesso {
  codigo: number;
  tipoPessoa: string;
  nomePessoa: string;
  codigoPessoa: number;
  codigoAutorizacao: string;
  integracao: IntegracaoAcesso;
  empresaRemota: number;

  constructor(parameters: {
    codigo: number,
    tipoPessoa: string,
    nomePessoa: string,
    codigoPessoa: number,
    codigoAutorizacao: string,
    integracao: IntegracaoAcesso,
    empresaRemota: number,
  }) {
    const {
      codigo, tipoPessoa, nomePessoa, codigoPessoa, codigoAutorizacao, integracao, empresaRemota
    } = parameters;
    this.codigo = codigo;
    this.tipoPessoa = tipoPessoa;
    this.nomePessoa = nomePessoa;
    this.codigoPessoa = codigoPessoa;
    this.codigoAutorizacao = codigoAutorizacao;
    this.integracao = integracao;
    this.empresaRemota = empresaRemota;
  }
}


@Injectable({
  providedIn: 'root'
})
export class AutorizacaoAcessoAdapter implements Adapter<AutorizacaoAcesso> {
  adapt(item: any): AutorizacaoAcesso {
    return new AutorizacaoAcesso(item);
  }
}

export enum TipoAutorizacao {
  NAO_AUTORIZADO = 'NAO_AUTORIZADO',
  AUTORIZADO = 'AUTORIZADO',
  NAO_ENCONTRADO = 'NAO_ENCONTRADO'
}
