
import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';

export class Config {
  url: string;
  cor: string;
  camposAdicionais: string[];
  camposAdicionaisProduto: string[];
  camposAdicionaisProdutoPlano: string[];
  cobrarPrimeiraParcelaCompra: string;
  detalharParcelaTelaCheckout: string;
  titulocheckout: string;
  cobrarProdutoJuntoAdesaoMatricula: string;
  apresentarvaloranuidade: string;
  tipoConvenio: string;
  apresentarCPFLinkPag: boolean;
  apresentarDtFaturaLinkPag: boolean;
  apresentarTermoAceiteLinkPag: boolean;
  apresentarCartao: boolean;
  apresentarPix: boolean;
  apresentarBoleto: boolean;
  apresentarPixVenda: boolean;
  analyticsId: string;
  pixelId: string;
  tokenApiConversao: string;
  googleTagId: string;
  apresentarBoletoVenda: boolean;
  apresentarCartaoVenda: boolean;
  permitirMudarTipoParcelamento: boolean;
  apresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano: boolean;
  permiteVendaProdutoAlunoOutraUnidade: boolean;
  exibirTipoDocumentoTelaVendasOnline: boolean;
  habilitarAgendamentoAulaExperimentalLinkVisitante: boolean;
  habilitarPreCadastro: boolean;
  permitecontratosconcomintante: boolean;
  permiteRenovacaoDeContrato: boolean;
  configSescHabilitada: boolean;
  usarFormaPagamentoPlanoProduto: boolean;
  primeiraCobrancaPixEGuardarCartao: boolean;
  modalidadesIniciarSelecionadasContratoTurma: boolean;
  ativarLinksGooglePlayEAppleStore: boolean;
  urlLinkGooglePlay: string;
  urlLinkAppleStore: string;
  apresentarCartaoRegua: boolean;
  apresentarPixRegua: boolean;
  apresentarBoletoRegua: boolean;
  exibeDataUtilizacao: boolean;

  constructor(parameters: {
    url: string,
    cor: string,
    camposAdicionais: string[],
    camposAdicionaisProduto: string[],
    camposAdicionaisProdutoPlano: string[];
    cobrarPrimeiraParcelaCompra: string,
    detalharParcelaTelaCheckout: string,
    titulocheckout: string,
    cobrarProdutoJuntoAdesaoMatricula: string,
    apresentarvaloranuidade: string,
    tipoConvenio: string,
    apresentarCPFLinkPag: boolean,
    apresentarDtFaturaLinkPag: boolean,
    apresentarTermoAceiteLinkPag: boolean,
    apresentarCartao: boolean,
    apresentarPix: boolean,
    apresentarBoleto: boolean,
    apresentarPixVenda: boolean,
    analyticsId: string;
    pixelId: string;
    tokenApiConversao: string;
    googleTagId: string;
    apresentarBoletoVenda: boolean,
    apresentarCartaoVenda: boolean,
    permitirMudarTipoParcelamento: boolean,
    apresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano: boolean,
    permiteVendaProdutoAlunoOutraUnidade: boolean,
    exibirTipoDocumentoTelaVendasOnline: boolean,
    habilitarAgendamentoAulaExperimentalLinkVisitante: boolean,
    habilitarPreCadastro: boolean,
    permitecontratosconcomintante: boolean,
    configSescHabilitada: boolean;
    permiteRenovacaoDeContrato: boolean,
    usarFormaPagamentoPlanoProduto: boolean;
    primeiraCobrancaPixEGuardarCartao: boolean;
    modalidadesIniciarSelecionadasContratoTurma: boolean;
    ativarLinksGooglePlayEAppleStore: boolean;
    urlLinkGooglePlay: string;
    urlLinkAppleStore: string;
    apresentarCartaoRegua: boolean,
    apresentarPixRegua: boolean,
    apresentarBoletoRegua: boolean,
    exibeDataUtilizacao: boolean,
  }) {
    const {
      cor, url, camposAdicionais, camposAdicionaisProduto,
      camposAdicionaisProdutoPlano, cobrarPrimeiraParcelaCompra, detalharParcelaTelaCheckout, titulocheckout,
      cobrarProdutoJuntoAdesaoMatricula, apresentarvaloranuidade, tipoConvenio, apresentarCPFLinkPag,
      apresentarDtFaturaLinkPag, apresentarTermoAceiteLinkPag, apresentarCartao, apresentarPix, apresentarBoleto, analyticsId,
      pixelId, tokenApiConversao, apresentarPixVenda, googleTagId, apresentarBoletoVenda, apresentarCartaoVenda,
      permitirMudarTipoParcelamento, apresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano, permiteVendaProdutoAlunoOutraUnidade,
      exibirTipoDocumentoTelaVendasOnline, habilitarAgendamentoAulaExperimentalLinkVisitante, habilitarPreCadastro,
      permitecontratosconcomintante , permiteRenovacaoDeContrato, usarFormaPagamentoPlanoProduto, configSescHabilitada,
      primeiraCobrancaPixEGuardarCartao, modalidadesIniciarSelecionadasContratoTurma, ativarLinksGooglePlayEAppleStore,
      urlLinkGooglePlay, urlLinkAppleStore, apresentarCartaoRegua, apresentarPixRegua, apresentarBoletoRegua, exibeDataUtilizacao
    } = parameters;
    this.url = url;
    this.cor = cor;
    this.camposAdicionais = camposAdicionais;
    this.camposAdicionaisProduto = camposAdicionaisProduto;
    this.camposAdicionaisProdutoPlano = camposAdicionaisProdutoPlano;
    this.cobrarPrimeiraParcelaCompra = cobrarPrimeiraParcelaCompra;
    this.detalharParcelaTelaCheckout = detalharParcelaTelaCheckout;
    this.titulocheckout = titulocheckout;
    this.cobrarProdutoJuntoAdesaoMatricula = cobrarProdutoJuntoAdesaoMatricula;
    this.apresentarvaloranuidade = apresentarvaloranuidade;
    this.tipoConvenio = tipoConvenio;
    this.apresentarCPFLinkPag = apresentarCPFLinkPag;
    this.apresentarDtFaturaLinkPag = apresentarDtFaturaLinkPag;
    this.apresentarTermoAceiteLinkPag = apresentarTermoAceiteLinkPag;
    this.apresentarCartao = apresentarCartao;
    this.apresentarPix = apresentarPix;
    this.apresentarBoleto = apresentarBoleto;
    this.apresentarPixVenda = apresentarPixVenda;
    this.analyticsId = analyticsId;
    this.pixelId = pixelId;
    this.tokenApiConversao = tokenApiConversao;
    this.googleTagId = googleTagId;
    this.apresentarBoletoVenda = apresentarBoletoVenda;
    this.apresentarCartaoVenda = apresentarCartaoVenda;
    this.permitirMudarTipoParcelamento = permitirMudarTipoParcelamento;
    this.apresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano = apresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano;
    this.permiteVendaProdutoAlunoOutraUnidade = permiteVendaProdutoAlunoOutraUnidade;
    this.exibirTipoDocumentoTelaVendasOnline = exibirTipoDocumentoTelaVendasOnline;
    this.habilitarAgendamentoAulaExperimentalLinkVisitante = habilitarAgendamentoAulaExperimentalLinkVisitante;
    this.habilitarPreCadastro = habilitarPreCadastro;
    this.permitecontratosconcomintante = permitecontratosconcomintante;
    this.permiteRenovacaoDeContrato = permiteRenovacaoDeContrato;
    this.configSescHabilitada = configSescHabilitada;
    this.usarFormaPagamentoPlanoProduto = usarFormaPagamentoPlanoProduto;
    this.primeiraCobrancaPixEGuardarCartao = primeiraCobrancaPixEGuardarCartao;
    this.modalidadesIniciarSelecionadasContratoTurma = modalidadesIniciarSelecionadasContratoTurma;
    this.ativarLinksGooglePlayEAppleStore = ativarLinksGooglePlayEAppleStore;
    this.urlLinkGooglePlay = urlLinkGooglePlay;
    this.urlLinkAppleStore = urlLinkAppleStore;
    this.apresentarCartaoRegua = apresentarCartaoRegua;
    this.apresentarPixRegua = apresentarPixRegua;
    this.apresentarBoletoRegua = apresentarBoletoRegua;
    this.exibeDataUtilizacao = exibeDataUtilizacao;
  }
}

@Injectable({
  providedIn: 'root'
})
export class ConfigAdapter implements Adapter<Config> {

  adapt(item: any): Config {
    return new Config(item);
  }
}
