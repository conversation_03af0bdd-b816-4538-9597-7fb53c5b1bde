import { Injectable } from '@angular/core';
import { Empresa, EmpresaAdapter } from '@base-core/empresa/empresa.model';
import { HttpClient } from '@angular/common/http';
import { RestService } from '@base-core/rest/rest.service';
import { Observable } from 'rxjs';
import {catchError, map} from 'rxjs/operators';
import { Config, ConfigAdapter } from '@base-core/empresa/config.model';
import { TranslateService } from '@ngx-translate/core';
import Swal from 'sweetalert2';

@Injectable({
  providedIn: 'root'
})
export class EmpresaService {
  unidadeSelecionada: Empresa;
  config: Config;

  constructor(private http: HttpClient,
    private adapter: EmpresaAdapter,
    private cfgadapter: ConfigAdapter,
    private restService: RestService,
    private translate: TranslateService) {
    translate.addLangs(['ptbr', 'en']);
    translate.setDefaultLang('ptbr');
  }

  obterEmpresa(chave, codigo): Observable<Empresa> {
    const url = this.restService.buildFullUrl('v2/vendas/' + chave + '/unidade/' + codigo);
    return this.http.get(url).pipe(map((data: any) => {
      if (data.return.usarSistemaInternacional !== undefined) {
        window.localStorage.setItem('usarSistemaInternacional', data.return.usarSistemaInternacional.toString());
        if (data.return.usarSistemaInternacional === true && data.return.locale !== undefined) {
          window.localStorage.setItem('locale', data.return.locale);
          switch (data.return.locale) {
            case 'pt_BR':
              this.translate.use('ptbr');
              break;
            case 'en_US':
              this.translate.use('en');
              break;
            default:
              this.translate.use('ptbr');
              break;
          }
        }
      }
      if (data.return.mascaraTelefone !== undefined) {
        window.localStorage.setItem('mascaraTelefone', data.return.mascaraTelefone);
      }
      return this.adapter.adapt(data.return);
    }),
    );
  }
  obterConfigs(chave, codigo): Observable<Config> {
    var url2 = window.location.href;
    let tela;
    const regex = /\/([^\/?]+)\?/;
    const matches = url2.match(regex);
    if (matches && matches.length > 1) {
      tela = matches[1];
      window.localStorage.setItem('nometela', tela);
    } else {
      console.log('Não foi possível extrair a palavra desejada da URL.');
    }
    const url = this.restService.buildFullUrl('v2/vendas/' + chave + '/configs/' + codigo + '/tela/' + window.localStorage.getItem('nometela'));
    return this.http.get(url).pipe(
      map((data: any) => {
        if (data.erro) {
          Swal.fire({
            type: 'error',
            title: 'Erro',
            text: data.erro,
            showConfirmButton: true
          });
          return null;
        } else {
          return this.cfgadapter.adapt(data.return);
        }
      }),
    );
  }

  obterDadosToken(chave, token): Observable<any> {
    const url = this.restService.buildFullUrl('v2/vendas/' + chave + '/' + token);
    return this.http.get(url).pipe(map((data: any) => {
        return data;
      }),
    );
  }

  obterDadosFormaPagamentoPlanoProduto(chave, empresa): Observable<any> {
    const url = this.restService.buildFullUrl('v2/vendas/' + chave + '/formaPagamentoPlanoProduto/' + empresa);
    return this.http.get(url).pipe(map((data: any) => {
        return data;
      }),
    );
  }

  obterFormaPagamentoPlanoProduto(chave, empresa, idConsulta, produto): Observable<Array<string>> {
    const url = this.restService.buildFullUrl('v2/vendas/' + chave + '/obterFormaPagamentoPlanoProduto/' + empresa +
      '/' + idConsulta + '/' + produto);
    return this.http.get<Array<string>>(url)
      .pipe(
        catchError(this.handleError)
      );
  }

  private handleError(error: any): Observable<never> {
    console.error('Erro na requisição:', error);
    throw error;
  }
}

