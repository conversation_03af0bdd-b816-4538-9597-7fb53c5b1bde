
import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';

export class Empresa {
  codigo: number;
  nome: string;
  logo: string;
  chave: string;
  cidade: string;
  estado: string;
  telefone: string;
  email: string;
  cep: string;
  longitude: string;
  latitude: string;
  endereco: string;
  complemento: string;
  imagens: string[];
  usarSistemaInternacional: boolean;
  moeda: string;
  locale: string;
  mascaraTelefone: string;
  utilizaGestaoClientesComRestricoes: boolean;
  habilitarValidacaoHorariosMesmaTurma: boolean;

  constructor(parameters: {
      codigo: number,
      nome: string,
      logo: string,
      chave: string,
      cidade: string,
      estado: string,
      telefone: string,
      email: string,
      cep: string,
      longitude: string,
      latitude: string,
      endereco: string,
      imagens: string[],
      complemento: string,
      usarSistemaInternacional: boolean,
      moeda: string,
      locale: string,
      mascaraTelefone: string,
      utilizaGestaoClientesComRestricoes: boolean
    habilitarValidacaoHorariosMesmaTurma: boolean
  }) {
    const {codigo, nome, logo, chave, cidade, estado, telefone, email, cep,
      longitude, latitude, endereco, imagens, complemento, usarSistemaInternacional , moeda, locale, mascaraTelefone,
      utilizaGestaoClientesComRestricoes, habilitarValidacaoHorariosMesmaTurma} = parameters;
    this.codigo = codigo;
    this.nome = nome;
    this.logo = logo;
    this.chave = chave;
    this.cidade = cidade;
    this.estado = estado;
    this.telefone = telefone;
    this.email = email;
    this.cep = cep;
    this.longitude = longitude;
    this.latitude = latitude;
    this.imagens = imagens;
    this.endereco = endereco;
    this.complemento = complemento;
    this.usarSistemaInternacional = usarSistemaInternacional;
    this.moeda = moeda;
    this.locale = locale;
    this.mascaraTelefone = mascaraTelefone;
    this.utilizaGestaoClientesComRestricoes = utilizaGestaoClientesComRestricoes;
    this.habilitarValidacaoHorariosMesmaTurma = habilitarValidacaoHorariosMesmaTurma;
  }
}

@Injectable({
  providedIn: 'root'
})
export class EmpresaAdapter implements Adapter<Empresa> {

  adapt(item: any): Empresa {
    return new Empresa(item);
  }
}
