import { Injectable } from '@angular/core';
import {Plano, PlanoAdapter} from '@base-core/plano/plano.model';
import { Observable } from 'rxjs';
import {HttpClient} from '@angular/common/http';
import {RestService} from '@base-core/rest/rest.service';
import { map } from 'rxjs/operators';
import {Periodo, PeriodoAdapter} from '@base-core/agenda/periodo.model';
import {Aula} from '@base-core/agenda/aula.model';
import * as moment from 'moment';
@Injectable({
  providedIn: 'root'
})
export class AulasService {
  dia;
  aulasSelecionadas: Array<Aula> = [];
  constructor(
    private http: HttpClient,
    private adapter: PeriodoAdapter,
    private restService: RestService
  ) { }

  obterPeriodos(chave, codigo, plano, produto, linkVisitante): Observable<Array<Periodo>> {
    const url = this.restService.buildFullUrl('v2/vendas/' + chave + '/aulasColetivas/' + codigo);
    const params = {
      dia: (moment(this.dia)).format('DD/MM/YYYY'),
      plano: plano,
      produto: produto,
      linkVisitante: linkVisitante,
    };
    return this.http.get(url, {params}).pipe(
      map((data: any) => data.return.map(item => this.adapter.adapt(item))),
    );
  }

  codigosAulasSelecionadas(): Array<any> {
    const codigos = [];
    this.aulasSelecionadas.forEach(aula => {
      codigos.push(aula.codigo);
    });
    return codigos;
  }

  setDia(dia) {
    this.dia = dia;
  }

}
