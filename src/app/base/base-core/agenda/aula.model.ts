import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';


export class Aula {
  codigo: number;
  passou: boolean;
  inicio: string;
  dia: string;
  ocupacao: number;
  capacidade: number;
  duracao: number;
  descricao: string;
  duracaoDesc?: string;


  constructor(codigo: number, inicio: string, ocupacao: number, capacidade: number, duracao: number,
              descricao: string, duracaoDesc: string, dia: string, passou: boolean) {
    this.codigo = codigo;
    this.inicio = inicio;
    this.ocupacao = ocupacao;
    this.capacidade = capacidade;
    this.duracao = duracao;
    this.descricao = descricao;
    this.duracaoDesc = duracaoDesc;
    this.dia = dia;
    this.passou = passou;
  }
}

@Injectable({
  providedIn: 'root'
})
export class AulaAdapter implements Adapter<Aula> {

  adapt(item: any): Aula {
    return new Aula(
      item.codigo,
      item.inicio,
      item.ocupacao,
      item.capacidade,
      item.duracao,
      item.descricao,
      item.duracaoDesc,
      item.dia,
      item.passou
    );
  }
}
