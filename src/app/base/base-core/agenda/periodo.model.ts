import {Injectable} from '@angular/core';
import {Adapter} from '@base-core/adaper.js';
import {PlanoAnuidadeParcelada} from '@base-core/plano/plano.anuidade.parcelada.model';
import {Aula} from '@base-core/agenda/aula.model';


export class Periodo {
  periodo: string;
  aulas: Array<Aula>;

  constructor(periodo: string, aulas: Array<Aula>) {
    if(window.localStorage.getItem('usarSistemaInternacional') == 'true'){
      switch (periodo) {
        case ("Manhã"):
          this.periodo = "Morning"
          break;
        case ("Tarde"):
          this.periodo = "Evening"
          break;
        case ("Noite"):
          this.periodo = "At night"
          break;

        default:
          this.periodo = periodo
          break;
      }
    }else{
      this.periodo = periodo
    }
    this.aulas = aulas;
  }
}

@Injectable({
  providedIn: 'root'
})
export class PeriodoAdapter implements Adapter<Periodo> {

  adapt(item: any): Periodo {
    return new Periodo(
      item.periodo,
      item.aulas
    );
  }
}
