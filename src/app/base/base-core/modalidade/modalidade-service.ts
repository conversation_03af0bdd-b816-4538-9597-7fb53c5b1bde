import { Injectable } from '@angular/core';
import { BehaviorSubject} from 'rxjs';
import { Modalidade } from '../../model/vendasOnlineV3.model';

@Injectable({
  providedIn: 'root'
})

export class ModalidadeService {

  private modalidadesSelecionadasSource = new BehaviorSubject<Modalidade[]>([]);

  modalidadesSelecionadas: Modalidade[] = [];

  modalidadesSelecionadas$ = this.modalidadesSelecionadasSource.asObservable();

  atualizarModalidadesSelecionadas(modalidades: Modalidade[]): void {
    this.modalidadesSelecionadasSource.next(modalidades);
    this.modalidadesSelecionadas = modalidades;
  }
}
