import { Injectable } from '@angular/core';
import {Observable} from 'rxjs/index';
import {HttpClient} from '@angular/common/http';
import {catchError, map} from 'rxjs/operators';
import {of} from 'rxjs';
import CryptoJS from 'crypto-js';

@Injectable({
  providedIn: 'root'
})
export class FacebookApiConversaoService {

  apiVersion: string = 'v18.0';

  constructor(private http: HttpClient) {
  }

  enviarEvento(pixelId: string, token: string, params: any): Observable<any> {
    const url = 'https://graph.facebook.com/' + this.apiVersion + '/' + pixelId + '/events?access_token=' + token;
    return this.http.post(url, params).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => {
        console.log(error);
        return of(error);
      }),
    );
  }

  stringToSha256(str: string): string {
    if (str) {
      str = str.toLowerCase();
      str = str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
      str = str.replace('/', '\x2f');
      str = str.replace(' ', '\x20');
      str = str.replace('-', '\x2d');
      str = str.replace('.', '\x2e');
      str = str.replace('(', '\x28');
      str = str.replace(')', '\x29');
      str = str.replace('@', '\x40');
      return CryptoJS.SHA256(str).toString(CryptoJS.enc.Hex);
    } else {
      return null;
    }
  }
}
