import { Injectable } from '@angular/core';
declare const fbq: any; // Declarando a função fbq do Facebook Pixel

@Injectable({
  providedIn: 'root'
})
export class FacebookPixelService {
  constructor() { }


  triggerEventFacebookPurchase(params: {value,currency,formaPagamento}) {
    if (!document.getElementById('pixel_facebook_id')) {
      console.info('FaceBookPixel sem ID configurada');
    } else {
      console.log('FaceBookPixel carregado');
      const fpEventScript = document.createElement('script');
      fpEventScript.setAttribute('id', 'pixel_facebook_id_event');
      fpEventScript.innerText = `!fbq('track', 'Purchase', ${JSON.stringify(params)});`;
      document.documentElement.firstChild.appendChild(fpEventScript);
      console.log('Pixel Meta: evento de venda disparado');
    }
  }

  triggerEventFacebookClicouComprarProduto(params: { nomeProduto: string, valorProduto: any }) {
    if (!document.getElementById('pixel_facebook_id')) {
      console.info('FaceBookPixel sem ID configurada');
    } else {
      console.log('FaceBookPixel carregado');
      const fpEventScript = document.createElement('script');
      fpEventScript.setAttribute('id', 'pixel_facebook_id_clicou_comprar_produto_event');
      fpEventScript.innerText = `!fbq('trackCustom', 'clicouBotaoComprarProduto', ${JSON.stringify(params)});`;
      document.documentElement.firstChild.appendChild(fpEventScript);
      console.log('Pixel Meta: evento de selecionar produto na tela de produtos disparado');
    }
  }

  triggerEventFacebookSelecionarPlano(params: { planoNome: string, planoValor: any }) {
    if (!document.getElementById('pixel_facebook_id')) {
      console.info('FaceBookPixel sem ID configurada');
    } else {
      console.log('FaceBookPixel carregado');
      const fpEventScript = document.createElement('script');
      fpEventScript.setAttribute('id', 'pixel_facebook_id_selecionar_plano_event');
      fpEventScript.innerText = `!fbq('trackCustom', 'SelecionarPlano', ${JSON.stringify(params)});`;
      document.documentElement.firstChild.appendChild(fpEventScript);
      console.log('Pixel Meta: evento de seleção de plano disparado');
    }
  }

  triggerEventFacebookIrParaCheckout(params: { planoNome: string, planoValor: any }) {
    if (!document.getElementById('pixel_facebook_id')) {
      console.info('FaceBookPixel sem ID configurada');
    } else {
      console.log('FaceBookPixel carregado');
      const fpEventScript = document.createElement('script');
      fpEventScript.setAttribute('id', 'pixel_facebook_id_ir_checkout_event');
      fpEventScript.innerText = `!fbq('trackCustom', 'continuarParaCheckout', ${JSON.stringify(params)});`;
      document.documentElement.firstChild.appendChild(fpEventScript);
      console.log('Pixel Meta: evento de continuar para checkout disparado');
    }
  }

  triggerEventFacebookIrParaCheckoutProduto(params: { produtoNome: string, produtoValor: any }) {
    if (!document.getElementById('pixel_facebook_id')) {
      console.info('FaceBookPixel sem ID configurada');
    } else {
      console.log('FaceBookPixel carregado');
      const fpEventScript = document.createElement('script');
      fpEventScript.setAttribute('id', 'pixel_facebook_id_ir_checkout_produto_event');
      fpEventScript.innerText = `!fbq('trackCustom', 'continuarParaCheckoutProduto', ${JSON.stringify(params)});`;
      document.documentElement.firstChild.appendChild(fpEventScript);
      console.log('Pixel Meta: evento de continuar para checkout disparado');
    }
  }

  triggerEventRealizouPreCadastro(params: any) {
    if (!document.getElementById('pixel_facebook_id')) {
      console.info('FaceBookPixel sem ID configurada');
    } else {
      console.log('FaceBookPixel carregado');
      const fpEventScript = document.createElement('script');
      fpEventScript.setAttribute('id', 'pixel_facebook_id_event');
      fpEventScript.innerText = `!fbq('trackCustom', 'realizouPreCadastroLead', ${JSON.stringify(params)});`;
      document.documentElement.firstChild.appendChild(fpEventScript);
      console.log('Pixel Meta: evento de pre cadastro disparado');
    }
  }

  triggerCustomEventFacebookPurchase(params: {value,currency,formaPagamento}) {
    if (!document.getElementById('pixel_facebook_id')) {
      console.info('FaceBookPixel sem ID configurada');
    } else {
      console.log('FaceBookPixel carregado');
      const fpEventScript = document.createElement('script');
      fpEventScript.setAttribute('id', 'pixel_facebook_id_event');
      fpEventScript.innerText = `!fbq('trackCustom', 'vendaRealizada', ${JSON.stringify(params)});`;
      document.documentElement.firstChild.appendChild(fpEventScript);
      console.log('Pixel Meta: evento de venda disparado');
    }
  }
}
