import { Injectable } from '@angular/core';
import * as moment from 'moment';

@Injectable({
  providedIn: 'root'
})
export class LocalizationService {

  maskDate = [/\d|\*/, /\d|\*/, '/', /\d|\*/, /\d|\*/, '/', /\d|\*/, /\d|\*/, /\d|\*/, /\d|\*/];
  maskCep = [/\d|\*/, /\d|\*/, '.', /\d|\*/, /\d|\*/, /\d|\*/, '-', /\d/, /\d/, /\d/];
  maskZipCode = [/\d/, /\d/, /\d/, /\d/, /\d/];

  constructor() { }

  getPhoneLocaleMask() {
    return (value) => {
      if (this.usaSistemaInternacional() && !this.isEmptyOrUndefined(window.localStorage.getItem('mascaraTelefone'))) {
        const mascaraTelefone = window.localStorage.getItem('mascaraTelefone');
        const arrayMascara = [];
        mascaraTelefone.split('').forEach(v => {
          v === '#' ? arrayMascara.push(/\d/) : arrayMascara.push(v);
        });
        return arrayMascara;
      }
      value = value ? value.replace(/\D/g, '') : '';
      if (value.length <= 10) {
        return ['(', /\d/, /\d/, ')', /\d|\*/, /\d|\*/, /\d|\*/, /\d|\*/, '-', /\d/, /\d/, /\d/, /\d/];
      } else {
        return ['(', /\d/, /\d/, ')', /\d|\*/, /\d|\*/, /\d|\*/, /\d|\*/, /\d|\*/, '-', /\d/, /\d/, /\d/, /\d/];
      }
    };
  }

  getMascaraCep() {
    if (this.usaSistemaInternacional()) {
      switch (this.getLocale()) {
        case 'en_US':
          return {mask: this.maskZipCode, guide: true};
          break;
        default:
          return {mask: this.maskCep, guide: true};
          break;
      }
    } else {
      return {mask: this.maskCep, guide: true};
    }
  }

  getDataFormatoPtbr(data: string): string {
    const dataOriginal = moment(data, this.getFormatoDataPorLocale());
    const dataFormatada = dataOriginal.format('DD/MM/YYYY');
    return moment(data, this.getFormatoDataPorLocale()).format('DD/MM/YYYY');
  }

  getFormatoDataPorLocale(): string {
    switch (this.getLocale()) {
      case 'pt_BR':
        return 'DD/MM/YYYY';
        break;
      case 'en_US':
        return 'MM/DD/YYYY';
        break;
      default:
        return 'DD/MM/YYYY';
        break;
    }
  }

  getMascaraData() {
    return {mask: this.maskDate};
  }

  getLocale(): string {
    return this.isEmptyOrUndefined(window.localStorage.getItem('locale')) ? 'pt_BR' : window.localStorage.getItem('locale');
  }

  usaSistemaInternacional(): boolean {
    if (this.isEmptyOrUndefined(window.localStorage.getItem('usarSistemaInternacional'))) {
      return false;
    }
    return window.localStorage.getItem('usarSistemaInternacional').toUpperCase() === 'TRUE';
  }

  isEmptyOrUndefined(value: any): boolean {
    return value === undefined || value === '' || value === null || value === 'null';
  }
}
