import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { DiscoveryServiceService } from '@base-core/discovery/discovery.service';
import {Observable, of} from 'rxjs';
import {catchError, map, switchMap} from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class CrmMsService {

  constructor(private http: HttpClient, private discoveryService: DiscoveryServiceService) { }

  chave(): String {
    return window.localStorage.getItem('chave');
  }

  obterLinkApp(email: string): Observable<string> {
    const url = 'https://app-do-aluno-unificado.web.app/usuario/solicitarLinkDeLoginV2';
    const body = {
      chave: this.chave(),
      userName: email,
      codigoValidacao: ''
    };
    return this.http.patch<{ sucesso: string }>(url, body).pipe(
      map(response => response.sucesso),
      catchError(() => of(''))
    );
  }

  enviarMensagem(cpf: string, email: string): Observable<any> {
    return this.obterLinkApp(email).pipe(
      switchMap(obtidaUrl => {
        if (!obtidaUrl) {
          throw new Error('URL não obtida');
        }
        return this.discoveryService.crmMsUrl().pipe(
          switchMap(crmUrl => {
            const url = `${crmUrl}/v1/ia/conversa/pos-venda?cpf=${cpf}&chave=${this.chave()}&linkAppTreino=${obtidaUrl}`;
            return this.http.post<any>(url, {});
          })
        );
      })
    );
  }
}
