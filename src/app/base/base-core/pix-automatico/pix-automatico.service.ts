import {Injectable} from '@angular/core';
import {PixAutomatico, PixAutomaticoAdapter} from '@base-core/pix-automatico/pix-automatico.model';
import {Observable} from 'rxjs';
import {HttpClient} from '@angular/common/http';
import {RestService} from '@base-core/rest/rest.service';
import {map} from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class PixAutomaticoService {

  constructor(
    private http: HttpClient,
    private adapter: PixAutomaticoAdapter,
    private restService: RestService
  ) {
  }

  obterPixAutomatico(chave: string, idRec: string): Observable<PixAutomatico> {
    const url = this.restService.buildFullUrl('pixAutomatico/request');
    const payload = {chave: chave, idRec: idRec, operacao: 'consultarPixAutomaticoPorIdRec'};
    return this.http.post(url, payload).pipe(
      map((data: any) => {
        // Parse da string JSON retornada no campo 'return'
        const parsedData = JSON.parse(data.return);

        // Verifica se o link já foi utilizado
        if (parsedData.meta && parsedData.meta.error === 'Erro') {
          throw new Error(parsedData.meta.message);
        }

        return this.adapter.adapt(parsedData);
      }),
    );
  }

  obterPixAutomaticoPorExternalId(chave: string, externalId: string): Observable<PixAutomatico> {
    const url = this.restService.buildFullUrl('pixAutomatico/request');
    const payload = { chave: chave, external_id: externalId, operacao: 'consultarPixAutomaticoPorExternalId' };
    return this.http.post(url, payload).pipe(
      map((data: any) => {
        // Parse da string JSON retornada no campo 'return'
        const parsedData = JSON.parse(data.return);

        // Verifica se o link já foi utilizado
        if (parsedData.meta && parsedData.meta.error === 'Erro') {
          throw new Error(parsedData.meta.message);
        }

        return this.adapter.adapt(parsedData);
      }),
    );
  }

  marcarLinkUtilizado(chave: string, idRec: string): Observable<any> {
    if (!chave || !idRec) {
      throw new Error('Parâmetros chave e idRec são obrigatórios');
    }
    const url = this.restService.buildFullUrl('pixAutomatico/request');
    const payload = {chave: chave, idRec: idRec, operacao: 'marcarLinkJaUtilizado'};
    return this.http.post(url, payload);
  }

}
