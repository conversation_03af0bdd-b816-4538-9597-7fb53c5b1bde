import {Injectable} from '@angular/core';

export interface PixAutomatico {
  // Informações de contrato
  cliente: string;
  clienteIdentificador: string;
  descricao: string;
  numeroContrato: string;

  // Informações de pagamento
  valorCobrado: string;
  valorMinimo: string;
  valorMaximo: string;
  metodoPagamento: string;
  cobranca: string;
  nomeInstituicaoBancaria: string;
  frequenciaCobranca: string;
  inicioAutorizacao: string;
  autorizacaoValidaAte: string;

  possuiCobrancaImediata: boolean;
  valorCobrancaImediata: string;

  // ID externo (para consultas por external_id)
  externalId: string;

  // Informações do recebedor
  recebedor: string;
  iniciadorPagamento: string;
  nomeinstituicaobancaria: string;
  minimum_amount: string;
  maximum_amount: string;

  // Configurações adicionais
  nomeEmpresa: string;
  cnpj: string;
  codigoEmpresa: number;

  // URLs de configuração do método de pagamento
  paymentMethodConfigurationStatementDescription?: string;
  paymentMethodConfigurationAmount?: number;
  paymentMethodConfigurationContractId?: string;
  paymentMethodConfigurationFrequency?: string;
  paymentMethodConfigurationStartDate?: string;
  paymentMethodConfigurationEndDate?: string;
  paymentMethodConfigurationAuthorizationUrl?: string;

  // URL de redirecionamento para autorização
  urlRedirect: string;

  // Campo do backend (minúsculo)
  valorcobrancaimediata?: string;
}

@Injectable({
  providedIn: 'root'
})
export class PixAutomaticoAdapter {
  adapt(item: any): PixAutomatico {
    if (!item || typeof item !== 'object') {
      throw new Error('Invalid input data for PixAutomaticoAdapter');
    }
    const paymentConfig = item.payment_method_configuration || {};
    const customer = item.customer || {};
    const empresa = item.empresa || {};

    return {
      cliente: customer.name || '',
      clienteIdentificador: customer.identifier || '',
      descricao: paymentConfig.object || '',
      numeroContrato: paymentConfig.contract_id || '',

      valorCobrado: paymentConfig.maximum_amount || 'R$ 0,00',
      valorMinimo: paymentConfig.minimum_amount || 'R$ 0,00',
      valorMaximo: paymentConfig.maximum_amount || 'R$ 0,00',
      metodoPagamento: 'Pix Automático',
      cobranca: paymentConfig.object || '',
      nomeInstituicaoBancaria: item.nomeinstituicaobancaria || '',
      nomeinstituicaobancaria: item.nomeinstituicaobancaria || '',
      minimum_amount: paymentConfig.minimum_amount || 'R$ 0,00',
      maximum_amount: paymentConfig.maximum_amount || 'R$ 0,00',
      frequenciaCobranca: paymentConfig.frequency || '',
      inicioAutorizacao: paymentConfig.start_date || '',
      autorizacaoValidaAte: paymentConfig.end_date || '',

      recebedor: empresa.descricao || '',
      iniciadorPagamento: 'Belvo Pagamentos - Parceira oficial Pix da Pacto Soluções',

      nomeEmpresa: empresa.descricao || '',
      cnpj: empresa.cnpj || '',
      codigoEmpresa: empresa.CODIGO || 1,

      paymentMethodConfigurationStatementDescription: paymentConfig.object,
      paymentMethodConfigurationAmount: paymentConfig.maximum_amount,
      paymentMethodConfigurationContractId: paymentConfig.contract_id,
      paymentMethodConfigurationFrequency: paymentConfig.frequency,
      paymentMethodConfigurationStartDate: paymentConfig.start_date,
      paymentMethodConfigurationEndDate: paymentConfig.end_date,
      paymentMethodConfigurationAuthorizationUrl: paymentConfig.authorization_url,

      urlRedirect: paymentConfig.authorization_url || '',
      possuiCobrancaImediata: item.possuiCobrancaImediata || false,
      valorCobrancaImediata: item.valorcobrancaimediata || 'R$ 0,00',
      externalId: item.external_id || ''
    };
  }
}
