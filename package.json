{"name": "vendas", "version": "0.0.0", "scripts": {"ng": "ng", "start-docker": "ng s --port 4200 --host host.docker.internal", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^7.2.16", "@angular/cdk": "^7.3.7", "@angular/common": "^7.2.16", "@angular/compiler": "^7.2.16", "@angular/core": "^7.2.16", "@angular/forms": "^7.2.16", "@angular/material": "^7.3.7", "@angular/platform-browser": "^7.2.16", "@angular/platform-browser-dynamic": "^7.2.16", "@angular/router": "^7.2.16", "@ng-bootstrap/ng-bootstrap": "4.1.1", "@ngx-translate/core": "^11.0.1", "@ngx-translate/http-loader": "^4.0.0", "@sweetalert2/ngx-sweetalert2": "^5.1.0", "angular-font-awesome": "^3.1.2", "angular2-text-mask": "^9.0.0", "argparse": "^1.0.10", "bootstrap-4-grid": "^2.4.1", "core-js": "^2.6.12", "credit-card-type": "^8.3.0", "crypto-js": "^4.2.0", "font-awesome": "^4.7.0", "hammerjs": "^2.0.8", "moment": "^2.29.1", "ng": "0.0.0", "ng-recaptcha": "^5.0.0", "node-sass": "^4.14.1", "pretty-json-stringify": "0.0.2", "qrcode": "^1.5.4", "rxjs": "~6.3.3", "s": "^1.0.0", "scripts": "^0.1.0", "sweetalert2": "^8.19.0", "tslib": "^1.14.1", "typescript-require": "^0.2.10", "zone.js": "~0.8.26"}, "devDependencies": {"@angular-devkit/build-angular": "^0.13.10", "@angular/cli": "^7.3.10", "@angular/compiler-cli": "^7.2.16", "@angular/language-service": "^7.2.16", "@types/jasmine": "^2.8.17", "@types/jasminewd2": "^2.0.8", "@types/node": "~8.9.4", "codelyzer": "~4.5.0", "jasmine-core": "~2.99.1", "jasmine-spec-reporter": "~4.2.1", "karma": "~4.0.0", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "^2.0.6", "karma-jasmine": "~1.1.2", "karma-jasmine-html-reporter": "^0.2.2", "protractor": "^5.4.4", "ts-node": "~7.0.0", "tslint": "~5.11.0", "typescript": "~3.2.2"}}