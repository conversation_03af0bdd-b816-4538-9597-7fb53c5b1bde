#!/bin/bash
docker container prune --force
docker volume prune --force

echo
docker pull registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4
docker pull registry.gitlab.com/plataformazw/zw/tomcat:master
docker pull registry.gitlab.com/plataformazw/api:master
docker pull registry.gitlab.com/plataformazw/vendasonline-v2.0:master

source .env
export $(cat .env)

echo
docker stack deploy -c docker-compose-prepare.yml ${STACK_NAME} --with-registry-auth

echo
echo "Waiting services up..."

docker pull jwilder/dockerize

docker run --add-host "$HOST_DNS":"$HOST_PIPELINE" jwilder/dockerize -wait $ZW_URL --timeout 5m -wait-retry-interval 10s

set -e

echo "Waiting 60 seconds ..."
sleep 90s

echo
curl --request POST $ZW_URL/prest/versao?chave=teste
echo

docker run --add-host "$HOST_DNS":"$HOST_PIPELINE" jwilder/dockerize -wait $API_URL/prest/config/reload --timeout 5m -wait-retry-interval 10s