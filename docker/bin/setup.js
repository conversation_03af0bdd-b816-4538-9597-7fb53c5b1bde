const replace = require('replace-in-files')
const store = require('data-store')({ path: process.cwd() + '/bin/data-store.json' })

const CURRENT_URL_API = store.get('URL_API') || 'https://app.pactosolucoes.com.br/api/prest'
const URL_API = process.env.URL_API

const options = {
    files: '/usr/share/nginx/html',
    from: CURRENT_URL_API,
    to: URL_API,
    shouldSkipBinaryFiles: true
}

replace(options)
    .then(({ changedFiles, countOfMatchesByPaths }) => {
        store.set('URL_API', URL_API)
        console.log('Setup URL_API: ' + URL_API)
        console.log('files path changed :', countOfMatchesByPaths)
    })
    .catch(error => {
        console.error('Error on setup URL_API:', error);
    })