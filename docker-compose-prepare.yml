version: '3.3'
services:
  postgres:    
    image: registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4
    extra_hosts:
     - ${HOST_DNS}:${HOST_PIPELINE}
    ports:
      - ${PG_PORT}:5432
    environment:
      INIT_DB: teste
      IP_HOST: postgres
      RESTORE_DB: 'true'
      URL_ZW_FRONT: http://${HOST_DNS}:${ADM_PORT}
      URL_AUTENTICACAO: http://${HOST_DNS}:${AUT_PORT}
      URL_LOGIN: http://${HOST_DNS}:${LOGIN_PORT}/LoginApp
      URL_NOVO_TREINO: http://${HOST_DNS}:${NTR_PORT}
      URL_OAMD: http://${HOST_DNS}:${OAMD_PORT}/NewOAMD
      URL_PACTOPAY: http://${HOST_DNS}:${PACTO_PAY_PORT}
      URL_PACTOPAY_DASH: http://${HOST_DNS}:${PACTO_PAY_PORT}
      URL_PERSONAGEM: http://${HOST_DNS}:${PERSONAGEM_PORT}
      URL_TREINO: http://${HOST_DNS}:${TR_PORT}/TreinoWeb
      URL_VENDAS_2: http://${HOST_DNS}:${VENDAS_PORT}
      URL_ZW: http://${HOST_DNS}:${ZW_PORT}/ZillyonWeb
      URL_ZW_API: http://${HOST_DNS}:${API_PORT}/API-ZillyonWeb
      URL_ARAGORN: http://aragorn:8080      
      URL_PLANO_MS: http://${HOST_DNS}:${PLANO_MS_PORT}/plano-ms
      URL_PRODUTO_MS: http://${HOST_DNS}:${PRODUTO_MS_PORT}/produto-ms
      URL_CAD_AUX_MS: http://${HOST_DNS}:${CAD_MS_PORT}/cad-aux-ms
    tmpfs:
      - /var/lib/postgresql:exec,size=1G
      - /run
      - /var/cache
    logging:
      driver: json-file

  autenticacao:
    image: registry.gitlab.com/plataformazw/autenticacao:master
    extra_hosts:
     - ${HOST_DNS}:${HOST_PIPELINE}
    environment:      
      DISCOVERY_URL: http://${HOST_DNS}:${DISCOVERY_PORT}
    ports:
     - ${AUT_PORT}:8080    
    logging:
      driver: json-file
    depends_on:
      - postgres
  
  discovery:
    image: registry.gitlab.com/plataformazw/discovery-urls:master
    extra_hosts:
     - ${HOST_DNS}:${HOST_PIPELINE}    
    ports:
     - ${DISCOVERY_PORT}:8080         
    logging:
      driver: json-file
    depends_on:
      - postgres

  api:
    image: registry.gitlab.com/plataformazw/api:master
    extra_hosts:
     - ${HOST_DNS}:${HOST_PIPELINE}
    ports:
     - ${API_PORT}:8080         
    logging:
      driver: json-file
    depends_on:
      - postgres     

  oamd:
    image: registry.gitlab.com/plataformazw/oamd/tomcat:master
    extra_hosts:
     - ${HOST_DNS}:${HOST_PIPELINE}
    environment:      
      DISCOVERY_URL: http://${HOST_DNS}:${DISCOVERY_PORT}
    ports:
     - ${OAMD_PORT}:8080         
    logging:
      driver: json-file
    depends_on:
      - postgres   

  vendas-online:
    image: registry.gitlab.com/plataformazw/vendasonline-v2.0:master
    extra_hosts:
     - ${HOST_DNS}:${HOST_PIPELINE}
    environment:
      URL_API: http://${HOST_DNS}:${API_PORT}/API-ZillyonWeb/prest
    ports:
     - ${VENDAS_PORT}:80    
    logging:
      driver: json-file
    depends_on:
      - postgres      

  aragorn:
    image: registry.gitlab.com/pactopay/aragorn:master    
    environment:
      JAVA_OPTS: "-Xmx256m -Xss256k -XX:ParallelGCThreads=1 -XX:+UseG1GC"    
    logging:
      driver: json-file
    depends_on:
      - postgres
                
  zw:
    image: registry.gitlab.com/plataformazw/zw/tomcat:master
    extra_hosts:
     - ${HOST_DNS}:${HOST_PIPELINE}
    environment:
      JAVA_OPTS: "-Xms256m -Xmx2g -Xss256k -XX:ParallelGCThreads=1 -XX:+UseG1GC -Duser.timezone=America/Sao_Paulo -Duser.language=pt -Duser.region=BR"
      BI_MS: http://bi-ms:28091/bi-ms      
      DISCOVERY_URL: http://${HOST_DNS}:${DISCOVERY_PORT}
      INICIAR_TLS: 'false'
      SMTP_CONEXAOSEGURA_ROBO: 'true'
      SMTP_EMAIL_NOREPLY: <EMAIL>
      SMTP_EMAIL_ROBO: <EMAIL>
      SMTP_LOGIN_ROBO: <EMAIL>
      SMTP_SENHA_ROBO: jozmpjoyandinxrm
      SMTP_SERVER_ROBO: smtp.mail.yahoo.com
      URL_HTTP_PLATAFORMA_PACTO: http://${HOST_DNS}:${ADM_PORT}/pt
      URL_OAMD: http://${HOST_DNS}:${OAMD_PORT}/NewOAMD
      URL_VENDAS_ONLINE: http://${HOST_DNS}:${VENDAS_PORT}
      URL_ZW_AUTO: http://${HOST_DNS}:${ZW_AUTO_PORT}/zw-auto
    ports:
     - ${ZW_PORT}:8080    
    logging:
      driver: json-file
    depends_on:
      - postgres

  