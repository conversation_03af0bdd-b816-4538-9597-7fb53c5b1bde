stages:
  - build
  - finish
  - deploy

variables:
  IMAGE_NAME: "registry.gitlab.com/plataformazw/vendasonline-v2.0:$CI_COMMIT_REF_SLUG"

docker-build:
  stage: build
  resource_group: production
  tags:
    - shell
  only:
   - master
   - merge_requests
   - tags
  except:
    changes:
      - .gitlab-ci-tests.yml
      #- .gitlab-ci.yml
    variables:
      - $INV_VENDAS_PRT
  script:
    - docker login -u $CI_USER -p $CI_USER_PASS $CI_REGISTRY
    - docker build -t $IMAGE_NAME .
    - docker push $IMAGE_NAME
  retry:
   max: 2
   when: runner_system_failure

tag-version:
  image: registry.gitlab.com/plataformazw/tag-versions:master
  tags:
    - docker
  stage: finish
  when: on_success
  only:
    - master
  except:
    changes:
      - .gitlab-ci-tests.yml
      - .gitlab-ci.yml
      - .bumpversion.cfg
      - deploy-env.sh
    variables:
      - $INV_VENDAS_PRT
  script:
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - git config --global user.name "Pipeline"
    - git config --global user.email "<EMAIL>"
    - git tag -l | xargs git tag -d
    - bumpversion  patch
    - git remote show origin
    - git remote set-url --<NAME_EMAIL>:$CI_PROJECT_PATH
    - git remote show origin
    - git tag -f latest -m "Deploy tag"
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push origin HEAD:$CI_COMMIT_REF_NAME
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git push -f --tags
  retry:
    max: 2
    when: runner_system_failure
  cache: []


invalidate-cdn-vendas-pratique:
  image: registry.gitlab.com/plataformazw/docker-pacto/deploy-node-aws:master
  interruptible: true
  only:
    variables:
      - $INV_VENDAS_PRT
  tags:
    - docker
    - large
  stage: deploy
  script:
    - aws cloudfront create-invalidation --distribution-id E3OF8N3BF3ITA0 --paths "/*"
