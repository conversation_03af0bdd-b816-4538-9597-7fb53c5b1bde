.test-vendas-online: &job0
  extends: .job_teste_template
  artifacts:
    paths:
      - .env
  script:
    - docker run --add-host "$HOST_DNS":"$HOST_PIPELINE" --rm -e CYPRESS_VENDAS_URL=$VENDAS_URL -e CYPRESS_URL_API=$API_URL -e CYPRESS_BASE_URL=$VENDAS_URL -e CYPRESS_RECORD_KEY=$CYPRESS_RECORD_KEY -e COMMIT_INFO_MESSAGE=$COMMIT_INFO_MESSAGE -e COMMIT_INFO_AUTHOR=$COMMIT_INFO_AUTHOR -e COMMIT_INFO_BRANCH=$COMMIT_INFO_BRANCH $IMAGE_LATEST run -P vendasOnline --config baseUrl=$VENDAS_URL --config-file=$CY_CONFIG_FILE --record

test-vendas-todos:
  <<: *job0
