const fs = require('fs');
require('typescript-require');
const prettyJSONStringify = require('pretty-json-stringify');

/**
 * USAGE: node configEnvironment.js 
 * 
 * Optional arguments:
 *   -h, --help            Show this help message and exit.
 * 
 *   -m {dev,prod}, --env_mode {dev,prod}
 *   -u RELATIVE_PATH, --relative_path RELATIVE_PATH
 *   -a API_URL, --api_url API_URL
 * 
*/
var ArgumentParser = require('argparse').ArgumentParser;
var parser = new ArgumentParser();

parser.addArgument( [ '-m', '--envmode' ], { choices: ['dev', 'prod'], defaultValue: 'dev' });
parser.addArgument( [ '-u', '--relative_path' ]);
parser.addArgument( [ '-a', '--api_url' ]);

var args = parser.parseArgs();
var PROD = args.envmode === 'prod';

environment = require('../src/environments/environment.ts').environment;

/**
 * Replace values
 */
if (PROD) {
    environment.production = true;
} else {
    environment.production = false;
}
environment.urlApi = args.api_url;
environment.serverBasePath = args.relative_path;

console.log('------------------------'); 
console.log(environment); 
console.log('------------------------');

/**
 * Export and save file content
 */
dataOut = `export const environment = ${prettyJSONStringify(environment)}`; 
dataOut = dataOut.replace(/"/g, "'");

if(PROD) {
    fs.writeFileSync('./src/environments/environment.prod.ts', dataOut);
} else {
    fs.writeFileSync('./src/environments/environment.ts', dataOut);
}