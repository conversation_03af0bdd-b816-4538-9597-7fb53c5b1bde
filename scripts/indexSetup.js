require('typescript-require');
const fs = require('fs');

/**
 * USAGE: node configEnvironment.js 
 * 
 * Optional arguments:
 *   -h, --help            Show this help message and exit.
 * 
 *   -m {dev,prod}, --env_mode {dev,prod}
 * 
*/
var ArgumentParser = require('argparse').ArgumentParser;
var parser = new ArgumentParser();

parser.addArgument( [ '-m', '--envmode' ], { choices: ['dev', 'prod'], defaultValue: 'dev' });

var args = parser.parseArgs();

if (args.envmode === 'prod') {
    environmentPath = '../src/environments/environment.prod.ts';
} else {
    environmentPath = '../src/environments/environment.ts';
}
environment = require(environmentPath).environment;
const replaceUrl = environment.serverBasePath;

let data = fs.readFileSync('./src/index.root.html', 'utf8');
data = data.replace('{BASE_URL}', replaceUrl);
fs.writeFileSync('./src/index.html', data, () => {});



