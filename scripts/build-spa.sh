#!/bin/bash

# 
# 'm' - mode: 'dev' | 'prod'
# 'u' - relative_url
# 'a' - api_url
#
# Example:
# . scripts/build-spa.sh -m 'prod' -u '/vendas/' -a 'http://app.pactosolucoes.com.br/api/prest'
#

set -e
set -x

# Parsear opções
while getopts ":m:u:a:" opt; do
  case $opt in
    m) mode="$OPTARG"
    ;;
    u) relative_url="$OPTARG"
    ;;
    a) api_url="$OPTARG"
    ;;
    \?) echo "Invalid option -$OPTARG" >&2
    ;;
  esac
done

# Configurando o arquivo environment.ts
node ./scripts/configEnvironment.js -m "$mode" -u "$relative_url" -a "$api_url"

# Index setup
#!/bin/bash

node ./scripts/indexSetup.js -m "$mode"

# CONSTRUINDO DEV
if [ "$mode" = "dev" ]; then
    ./node_modules/@angular/cli/bin/ng build
fi

# CONSTRUINDO PROD
if [ "$mode" = "prod" ]; then
    ./node_modules/@angular/cli/bin/ng build --prod --output-path=dist
fi