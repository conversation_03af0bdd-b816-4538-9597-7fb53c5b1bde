#!/bin/bash
export HOST_PIPELINE="*********"
export HOST_DNS="pipe-vendas.pactosolucoes.com.br"

export ZW_PORT=$(shuf -i 3500-3599 |head -n 1)
export STACK_NAME=pipe-vendas-$ZW_PORT

find . -iname "*.ports" -delete

echo "$ZW_PORT" > $STACK_NAME.ports

function getRandomPort() {
    valorTemp=$(shuf -i 4000-4999 |head -n 1)

    while [[ `cat $STACK_NAME.ports |grep $valorTemp |wc -l` -gt 0 ]]
    do
        valorTemp=$(shuf -i 4000-4999 |head -n 1)        
        echo "$valorTemp" >> $STACK_NAME.ports
    done

    echo "$valorTemp" >> $STACK_NAME.ports
    
    echo "$valorTemp"
}

export PG_PORT=$(getRandomPort)
export PG_API_PORT=$(getRandomPort)
export TR_PORT=$(getRandomPort)
export DISCOVERY_PORT=$(getRandomPort)
export API_PORT=$(getRandomPort)
export AUT_PORT=$(getRandomPort)
export BI_MS_PORT=$(getRandomPort)
export CAD_MS_PORT=$(getRandomPort)
export DASH_API_PORT=$(getRandomPort)
export LOGIN_PORT=$(getRandomPort)
export ADM_PORT=$(getRandomPort)
export OAMD_PORT=$(getRandomPort)
export ZW_AUTO_PORT=$(getRandomPort)
export VENDAS_PORT=$(getRandomPort)
export FULL_REPORT_PORT=$(getRandomPort)
export PRODUTO_MS_PORT=$(getRandomPort)
export PACTO_PAY_PORT=$(getRandomPort)
export PERSONAGEM_PORT=$(getRandomPort)
export PLANO_MS_PORT=$(getRandomPort)
export NTR_PORT=$(getRandomPort)

export ZW_URL=http://$HOST_DNS:$ZW_PORT/ZillyonWeb
export LOGIN_URL=http://$HOST_DNS:$LOGIN_PORT/LoginApp
export TREINO_URL=http://$HOST_DNS:$TR_PORT/TreinoWeb
export VENDAS_URL=http://$HOST_DNS:$VENDAS_PORT
export API_URL=http://$HOST_DNS:$API_PORT/API-ZillyonWeb

sed -i -e "s/STACK_NAME=.*/STACK_NAME=$STACK_NAME/g" .env
sed -i -e "s~ZW_URL=.*~ZW_URL=$ZW_URL~g" .env
sed -i -e "s~URL_ZW_INTEGRACAO=.*~URL_ZW_INTEGRACAO=$ZW_URL~g" .env
sed -i -e "s~LOGIN_URL=.*~LOGIN_URL=$LOGIN_URL~g" .env
sed -i -e "s~TREINO_URL=.*~TREINO_URL=$TREINO_URL~g" .env
sed -i -e "s~VENDAS_URL=.*~VENDAS_URL=$VENDAS_URL~g" .env
sed -i -e "s~API_URL=.*~API_URL=$API_URL~g" .env
sed -i -e "s/ZW_PORT=.*/ZW_PORT=$ZW_PORT/g" .env
sed -i -e "s/TR_PORT=.*/TR_PORT=$TR_PORT/g" .env
sed -i -e "s/PLANO_MS_PORT=.*/PLANO_MS_PORT=$PLANO_MS_PORT/g" .env
sed -i -e "s/NTR_PORT=.*/NTR_PORT=$NTR_PORT/g" .env
sed -i -e "s/HOST_PIPELINE=.*/HOST_PIPELINE=$HOST_PIPELINE/g" .env
sed -i -e "s/HOST_DNS=.*/HOST_DNS=$HOST_DNS/g" .env
sed -i -e "s/DISCOVERY_PORT=.*/DISCOVERY_PORT=$DISCOVERY_PORT/g" .env
sed -i -e "s/API_PORT=.*/API_PORT=$API_PORT/g" .env
sed -i -e "s/AUT_PORT=.*/AUT_PORT=$AUT_PORT/g" .env
sed -i -e "s/BI_MS_PORT=.*/BI_MS_PORT=$BI_MS_PORT/g" .env
sed -i -e "s/CAD_MS_PORT=.*/CAD_MS_PORT=$CAD_MS_PORT/g" .env
sed -i -e "s/DASH_API_PORT=.*/DASH_API_PORT=$DASH_API_PORT/g" .env
sed -i -e "s/LOGIN_PORT=.*/LOGIN_PORT=$LOGIN_PORT/g" .env
sed -i -e "s/ADM_PORT=.*/ADM_PORT=$ADM_PORT/g" .env
sed -i -e "s/OAMD_PORT=.*/OAMD_PORT=$OAMD_PORT/g" .env
sed -i -e "s/ZW_AUTO_PORT=.*/ZW_AUTO_PORT=$ZW_AUTO_PORT/g" .env
sed -i -e "s/VENDAS_PORT=.*/VENDAS_PORT=$VENDAS_PORT/g" .env
sed -i -e "s/FULL_REPORT_PORT=.*/FULL_REPORT_PORT=$FULL_REPORT_PORT/g" .env
sed -i -e "s/PRODUTO_MS_PORT=.*/PRODUTO_MS_PORT=$PRODUTO_MS_PORT/g" .env
sed -i -e "s/PACTO_PAY_PORT=.*/PACTO_PAY_PORT=$PACTO_PAY_PORT/g" .env
sed -i -e "s/PERSONAGEM_PORT=.*/PERSONAGEM_PORT=$PERSONAGEM_PORT/g" .env
sed -i -e "s/PG_PORT=.*/PG_PORT=$PG_PORT/g" .env
sed -i -e "s/PG_API_PORT=.*/PG_API_PORT=$PG_API_PORT/g" .env

